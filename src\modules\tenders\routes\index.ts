import type { NIORouteMap } from '@/router';

export const routeMap = {
  tenders: {
    path: '/tenders',
    name: 'tenders',
    meta: {
      i18nTitle: 'tenders.title',
      authNotRequired: true,
      headTitlePrefix: 'Tenders',
    },
  },
  matching: {
    path: '/tenders/:id/pre-matching',
    name: 'matching',
    meta: {
      i18nTitle: 'tenders.matching.title',
      authNotRequired: true,
      headTitlePrefix: 'Tenders',
    },
  },
  detail: {
    path: '/tenders/:id',
    name: 'detail',
    meta: {
      i18nTitle: 'tenders.detail.title',
      authNotRequired: true,
      headTitlePrefix: 'Tenders',
    },
    children: {
      tenderDetail: {
        path: 'detail',
        name: 'detailDashboard',
        meta: {
          i18nTitle: 'tenders.detail.title',
          authNotRequired: true,
          headTitlePrefix: 'Tenders',
        },
      },
      tenderMatching: {
        path: 'matching/vendors',
        name: 'tenderMatching',
        meta: {
          i18nTitle: 'tenders.detail.title',
          authNotRequired: true,
          headTitlePrefix: 'Matching',
        },
      },
      tenderMatchingMarketplace: {
        path: 'matching/marketplace',
        name: 'tenderMatchingMarketplace',
        meta: {
          i18nTitle: 'tenders.detail.title',
          authNotRequired: true,
          headTitlePrefix: 'Marketplace',
        },
      },
      tenderProposal: {
        path: 'proposal',
        name: 'tenderProposal',
        meta: {
          i18nTitle: 'tenders.detail.title',
          authNotRequired: true,
          headTitlePrefix: 'Proposal',
        },
      },
    },
  },
} satisfies NIORouteMap;

export const routeIndex = [
  {
    ...routeMap.tenders,
    component: () => import('../pages/TendersPage.vue'),
  },
  {
    ...routeMap.detail,
    redirect: { name: routeMap.detail.children.tenderDetail.name },
    children: [
      {
        ...routeMap.detail.children.tenderDetail,
        component: () => import('../pages/TenderDetailPage.vue'),
      },
      {
        ...routeMap.detail.children.tenderMatching,
        component: () => import('../pages/TenderMatchingPage.vue'),
      },
      {
        ...routeMap.detail.children.tenderMatchingMarketplace,
        component: () => import('../pages/TenderMarketplaceMatchingPage.vue'),
      },
      {
        ...routeMap.detail.children.tenderProposal,
        component: () => import('../pages/TendersProposalPage.vue'),
      },
    ],
  },
];

export default routeIndex;
