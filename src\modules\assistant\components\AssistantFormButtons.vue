<script lang="ts" setup>
import { AssistantGuideSteps } from '@/modules/assistant/types/assistant-steps.ts';
import { routeMap } from '@/modules/tenders/routes';

interface Props {
  currentGuideStep: AssistantGuideSteps,
  loading?: boolean,
  tenderId?: string | null,
}

const props = defineProps<Props>();

const emit = defineEmits(['previousStep', 'downloadRfp', 'saveToTenders', 'saveForLater', 'submitForm']);

const safeEmit = (...args: Parameters<typeof emit>) => {
  if (props.loading) {
    return;
  }
  emit(...args);
};
</script>

<template>
  <section class="mt-8 flex items-center" :class="currentGuideStep === AssistantGuideSteps.GENERAL ? 'justify-end' : 'justify-between'">
    <button
      v-if="![AssistantGuideSteps.GENERAL, AssistantGuideSteps.REVIEW].includes(currentGuideStep) && !tenderId"
      type="button"
      :disabled="loading"
      class="cursor-pointer py-3 px-[14px] text-center rounded-full bg-nio-grey-100 hover:bg-nio-grey-hover-100 text-sm text-nio-grey-700 font-medium -tracking-028px leading-[16px] disabled:cursor-not-allowed disabled:opacity-50"
      @click="safeEmit('previousStep')"
    >
      Back
    </button>
    <button
      v-else-if="currentGuideStep === AssistantGuideSteps.REVIEW"
      type="button"
      :disabled="loading"
      class="cursor-pointer py-3 px-[14px] text-center rounded-full bg-nio-grey-100 hover:bg-nio-grey-hover-100 text-sm text-nio-grey-700 font-medium -tracking-028px leading-[16px] disabled:cursor-not-allowed disabled:opacity-50"
      @click="safeEmit('downloadRfp')"
    >
      Download RFP
    </button>
    <div class="flex items-center gap-2">
      <button
        v-if="!tenderId"
        type="button"
        :disabled="loading"
        class="cursor-pointer py-3 px-[14px] text-center rounded-full border border-nio-grey-200 text-sm text-nio-grey-700 hover:text-nio-blue-800 font-medium -tracking-028px leading-[16px] disabled:opacity-50 disabled:cursor-not-allowed"
        @click="safeEmit('saveForLater')"
      >
        Save For Later
      </button>
      <button
        v-if="currentGuideStep === AssistantGuideSteps.GENERAL && !tenderId"
        type="button"
        :disabled="loading"
        class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px]  disabled:opacity-50 disabled:cursor-not-allowed"
        @click="safeEmit('submitForm')"
      >
        Next
      </button>
      <button
        v-else-if="currentGuideStep === AssistantGuideSteps.RESOURCE_AND_TECH && !tenderId"
        type="button"
        :disabled="loading"
        class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px]  disabled:opacity-50 disabled:cursor-not-allowed"
        @click="safeEmit('submitForm')"
      >
        Create RFP
      </button>
      <button
        v-else-if="currentGuideStep === AssistantGuideSteps.REVIEW && !tenderId"
        type="button"
        :disabled="loading"
        class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px] disabled:cursor-not-allowed disabled:opacity-50"
        @click="safeEmit('saveToTenders')"
      >
        Create New Tender
      </button>
      <router-link
        v-else-if="tenderId"
        :to="{name: routeMap.detail.children.tenderDetail.name, params: { id: props.tenderId }}"
        :disabled="loading"
        class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px] disabled:cursor-not-allowed disabled:opacity-50"
      >
        Open Tender
      </router-link>
    </div>
  </section>
</template>
