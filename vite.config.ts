import { sentryVitePlugin } from '@sentry/vite-plugin';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import svgLoader from 'vite-svg-loader';
import vueDevTools from 'vite-plugin-vue-devtools';
import Components from 'unplugin-vue-components/vite';
import { PrimeVueResolver } from 'unplugin-vue-components/resolvers';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  base: '/',
  plugins: [vue(), tailwindcss(), vueDevTools(), Components({
    resolvers: [
      PrimeVueResolver()
    ]
  }), svgLoader(), sentryVitePlugin({
    org: 'nordicsio',
    project: 'enterprise'
  })],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    watch: {
      ignored: ['**/test/**']
    },
  },
  esbuild: {
    target: 'esnext',
  },
  build: {
    target: 'esnext',
    sourcemap: true
  },
});
