<script setup lang="ts">
import { CompanyCategory, type ProfileResponse } from '@/modules/auth/types/auth-types';
import CompaniesMatchingTableWidgetEntry from './CompaniesMatchingTableWidgetEntry.vue';
import { ref, computed, watch } from 'vue';
import NioPagination from '@/common/components/NioPagination.vue';
import MagnifyingGlassIcon from '@/assets/icons/magnifying-glass-icon.svg';
import { XIcon } from 'lucide-vue-next';
import { normalizeSearch } from '@/common/utils/strings';

interface Props {
  companies: ProfileResponse['workspaces'][0]['companies'];
  startingIndex: number;
  tenderPublicId: string;
  loading: boolean;
}

const props = defineProps<Props>();

const itemsPerPage = 16;
const currentPage = ref(1);
const searchQuery = ref('');

const filteredCompanies = computed(() => {
  let companies = props.companies;

  if (selectedFilter.value !== 'all') {
    companies = companies.filter(company => company.category === selectedFilter.value);
  }

  if (searchQuery.value.trim()) {
    const normalizedSearch = normalizeSearch(searchQuery.value);
    companies = companies.filter(company => {
      const normalizedName = normalizeSearch(company.name);
      return normalizedName.includes(normalizedSearch);
    });
  }

  return companies;
});

const pagedCompanies = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  return filteredCompanies.value.slice(start, start + itemsPerPage);
});

const pageOffset = computed(() => (currentPage.value - 1) * itemsPerPage);

const filterOptions = computed<{
  value: 'all' | 'agency' | 'enterprise';
  label: string;
  count: number;
  badge?: { text: string; bgClass: string; textClass: string };
  selectedBg?: string;
}[]>(() => [
  {
    value: 'all',
    label: 'All Companies',
    count: props.companies.length
  },
  {
    value: 'agency',
    label: 'Agency',
    count: props.companies.filter(c => c.category === CompanyCategory.AGENCY).length,
    selectedBg: 'bg-nio-green-text text-white',
    badge: {
      text: 'A',
      bgClass: 'bg-nio-green-text/20',
      textClass: 'text-green-700'
    }
  },
  {
    value: 'enterprise',
    label: 'Enterprise',
    count: props.companies.filter(c => c.category === CompanyCategory.ENTERPRISE).length,
    selectedBg: 'bg-nio-blue-500 text-white',
    badge: {
      text: 'E',
      bgClass: 'bg-nio-blue-500/15',
      textClass: 'text-nio-blue-800'
    }
  },
]);
const selectedFilter = ref<'all' | 'agency' | 'enterprise'>('all');

watch(selectedFilter, () => {
  currentPage.value = 1;
});

watch(searchQuery, () => {
  currentPage.value = 1;
});

</script>

<template>
  <div v-if="loading" class="w-full grid grid-cols-2 xl:grid-cols-4 gap-4">
    <div v-for="row in 5" :key="row" class="flex w-full max-w-full items-center justify-between group relative transition-all duration-300 rounded-20 animate-pulse">
      <div class="flex items-center space-x-1">
        <div class="w-[14px] h-[14px] bg-nio-grey-100 rounded-10 mb-4" />
        <div>
          <div class="w-[42px] h-[13px] bg-nio-grey-100 rounded-5 mb-1" />
          <div class="w-[98px] h-[22px] bg-nio-grey-100 rounded-5 mb-1" />
          <div class="w-[90px] h-[13px] bg-nio-grey-100 rounded-5 " />
        </div>
      </div>
    </div>
  </div>

  <div v-else-if="companies.length === 0">
    <table class="w-full border-collapse">
      <thead>
        <tr class="text-nio-grey-700">
          <th class="py-sm px-md w-[40%] text-left text-sm font-paragraph">
            Vendor
          </th>
          <th class="py-sm px-md w-[20%] text-right text-sm font-paragraph">
            Match
          </th>
        </tr>
      </thead>
    </table>

    <div class="flex justify-center items-center h-full mt-[124px] mb-[127px]">
      <p
        class="text-nio-red-500 text-center font-paragraph text-[24px] px-[14px] py-[7px] rounded-15 border border-nio-blue-outline-stroke-400 shadow-custom-box-shadow-01 inline-block"
      >
        All companies from workspace are already used.
      </p>
    </div>
  </div>

  <div
    v-else
    class="w-full"
  >
    <div class="mb-4" role="group" aria-label="Actions and filters">
      <div class="flex items-center justify-between gap-4 mb-4 flex-wrap">
        <div class="flex items-center gap-2 flex-wrap">
          <span class="text-sm text-gray-600">Filter:</span>
          <div class="flex gap-2 flex-wrap">
            <button
              v-for="option in filterOptions"
              :key="option.value"
              class="px-3 py-1.5 rounded-full text-sm transition-all duration-200 flex items-center gap-1.5 cursor-pointer"
              :class="selectedFilter === option.value
                ? (option.selectedBg ?? 'bg-blue-500 text-white')
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
              @click="selectedFilter = option.value"
            >
              <span v-if="option.badge" class="flex items-center">
                <span
                  class="inline-block px-1 py-px rounded-sm"
                  :class="[option.badge.bgClass, option.badge.textClass]"
                >
                  {{ option.badge.text }}
                </span>
                <span class="inline-block ml-1">{{ option.label }}</span>
              </span>
              <span v-else>{{ option.label }}</span>
              <span class="text-xs">{{ option.count }}</span>
            </button>
          </div>
        </div>

        <div class="relative">
          <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="w-4 h-4 text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search..."
            class="w-64 h-9 pl-10 pr-10 py-2 bg-gray-100 text-gray-700 text-sm rounded-full border border-gray-300/40 focus:outline-none focus:ring-0 focus:bg-white transition-all duration-200 hover:bg-gray-50"
          >
          <button
            v-if="searchQuery"
            class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
            @click="searchQuery = ''"
          >
            <XIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
    <div class="w-full grid grid-cols-2 xl:grid-cols-4 gap-1" role="list" aria-label="Companies list">
      <CompaniesMatchingTableWidgetEntry
        v-for="(company, index) in pagedCompanies"
        :key="company?.id"
        :company="company"
        :is-other-vendors="true"
        :tender-id="tenderPublicId"
        :index="index + pageOffset + startingIndex"
        role="listitem"
      />
    </div>
  </div>

  <div v-if="!loading && companies?.length" class="mt-auto mb-0 mx-auto nio-pagination">
    <NioPagination v-model="currentPage" :items-length="filteredCompanies.length" :items-per-page="itemsPerPage" />
  </div>
</template>

<style scoped>
.shadow-custom-box-shadow-01 {
  box-shadow: 0 71px 20px 0 rgba(207, 207, 207, 0.00),
  0 45px 18px 0 rgba(207, 207, 207, 0.01),
  0 25px 15px 0 rgba(207, 207, 207, 0.05),
  0 11px 11px 0 rgba(207, 207, 207, 0.09),
  0 3px 6px 0 rgba(207, 207, 207, 0.10);
}
</style>
