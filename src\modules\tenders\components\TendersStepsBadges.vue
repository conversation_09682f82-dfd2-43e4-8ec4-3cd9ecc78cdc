<script setup lang="ts">
import { IntroSteps } from '@/modules/intro/types/intro-steps';

interface Props {
  currentStep: IntroSteps
}

defineProps<Props>();
</script>

<template>
  <div class="fixed -translate-y-1/2 top-1/2 right-[7.5rem] w-[86px] flex flex-col items-end gap-[15px] [&>div]:transition-all [&>div]:duration-1000 [&>div]:ease-in-out [&>div]:delay-1000">
    <div
      :class="[
        currentStep === IntroSteps.INTRO ? 'bg-[#F9F9F9]' : '',
        [IntroSteps.UPLOAD_VENDORS, IntroSteps.UPLOADING].includes(currentStep) ? 'bg-[#0071E3]' : '',
        currentStep === IntroSteps.VERIFICATION ? 'bg-[rgba(249,249,249,0.30)] blur-[1px]' : '',
        currentStep === IntroSteps.ADD_VENDORS ? 'bg-[rgba(249,249,249,0.30)] blur-[2px]' : '',
      ]"
      class="w-4 h-[3px] rounded-sm"
    />
    <div
      :class="[
        currentStep === IntroSteps.VERIFICATION ? 'bg-[#0071E3]' : '',
        [IntroSteps.UPLOAD_VENDORS, IntroSteps.UPLOADING, IntroSteps.ADD_VENDORS].includes(currentStep) ? 'bg-[rgba(249,249,249,0.30)] blur-[1px]' : '',
      ]"
      class="w-4 h-[3px] rounded-sm"
    />
    <div
      :class="[
        currentStep === IntroSteps.ADD_VENDORS ? 'bg-[#0071E3]' : '',
        currentStep === IntroSteps.VERIFICATION ? 'bg-[rgba(249,249,249,0.30)] blur-[1px]' : '',
        [IntroSteps.UPLOAD_VENDORS, IntroSteps.UPLOADING].includes(currentStep) ? 'bg-[rgba(249,249,249,0.30)] blur-[2px]' : '',
      ]"
      class="w-4 h-[3px] rounded-sm"
    />
  </div>
</template>
