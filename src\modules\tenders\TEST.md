# E2E Test Cases for Strategic Partners and Invites Functionality

## Overview
This document outlines all test cases for the Strategic Partners section and Invites functionality in the Tender Detail page, specifically focusing on loading states, error handling, and user interactions.

## Test Environment Setup

### Prerequisites
- Playwright test environment configured
- Test user with workspace containing companies
- Test tender with matching functionality

### Test Data Requirements
- Tender with ID that supports matching
- User workspace with multiple companies (agencies, enterprises)
- Companies with different invite statuses (invited, pending, not invited)

## Test Data Setup

### Required Test Companies
- Company A: Agency type, not invited
- Company B: Enterprise type, invited
- Company C: Agency type, pending invite
- Company D: Enterprise type, not invited

### Required Test Tenders
- Tender A: Normal matching, should succeed
- Tender B: No matching vendors scenario
- Tender C: Matching failure scenario


## Test Cases

### 1. Strategic Partners Loading States

#### 1.1 Initial Page Load
**Description**: Verify skeleton loading appears immediately when page loads
**Steps**:
1. Navigate to tender detail page
2. Verify Strategic Partners section shows skeleton loading
3. Verify skeleton has proper animation and styling

**Expected Result**: Skeleton loading visible immediately, no old data shown

#### 1.2 Matching in Progress
**Description**: Verify skeleton continues during matching process
**Steps**:
1. Navigate to tender detail page
2. Ensure matching is in progress (status = 'in_progress')
3. Verify skeleton continues to show
4. Wait for matching to complete

**Expected Result**: Skeleton visible throughout matching process

#### 1.3 Matching Success with Companies
**Description**: Verify skeleton stops and companies display when matching succeeds
**Steps**:
1. Navigate to tender detail page
2. Wait for matching to complete successfully
3. Verify skeleton disappears
4. Verify Strategic Partners companies are displayed
5. Verify invite statuses are correct

**Expected Result**: Companies displayed with correct invite statuses, no skeleton

#### 1.4 Matching Failed (HTTP Error)
**Description**: Verify skeleton stops when HTTP request fails
**Steps**:
1. Navigate to tender detail page
2. Block matching API requests using network blocking
3. Verify skeleton appears initially
4. Wait for request to fail
5. Verify skeleton stops and companies display

**Expected Result**: Skeleton stops, companies displayed (even if invite statuses are loading)

#### 1.5 Matching Failed (API Status Failed)
**Description**: Verify skeleton stops when API returns failed status
**Steps**:
1. Navigate to tender detail page
2. Mock API to return status: 'failed'
3. Verify skeleton appears initially
4. Wait for failed status
5. Verify skeleton stops and companies display

**Expected Result**: Skeleton stops, companies displayed

#### 1.6 No Vendors Matched
**Description**: Verify skeleton stops when no vendors match criteria
**Steps**:
1. Navigate to tender detail page
2. Mock API to return empty companies array with status: 'completed'
3. Verify skeleton appears initially
4. Wait for completed status
5. Verify skeleton stops and companies display

**Expected Result**: Skeleton stops, companies displayed

#### 1.7 Invite Statuses Loading
**Description**: Verify skeleton continues while invite statuses are loading
**Steps**:
1. Navigate to tender detail page
2. Block invite companies API request
3. Let matching complete/fail
4. Verify skeleton continues while invite statuses load
5. Unblock invite request
6. Verify skeleton stops when invite statuses are loaded

**Expected Result**: Skeleton continues until both matching and invite statuses are complete

### 2. Invites Section Visibility

#### 2.1 Invites Visible When Matching Complete
**Description**: Verify invites section shows when matching has companies
**Steps**:
1. Navigate to tender detail page
2. Wait for matching to complete with companies
3. Verify invites section is visible
4. Verify sent/pending counts are displayed

**Expected Result**: Invites section visible with correct counts

#### 2.2 Invites Visible When Companies Already Invited
**Description**: Verify invites section shows when companies are already invited
**Steps**:
1. Navigate to tender detail page with existing invited companies
2. Verify invites section is visible regardless of matching status
3. Verify sent/pending counts are correct

**Expected Result**: Invites section visible with existing invite data

#### 2.3 Invites Visible When Matching Failed
**Description**: Verify invites section shows when matching fails
**Steps**:
1. Navigate to tender detail page
2. Block matching requests to cause failure
3. Verify invites section becomes visible after failure
4. Verify invite button is functional

**Expected Result**: Invites section visible and functional after matching failure

#### 2.4 Invites Visible When No Vendors Matched
**Description**: Verify invites section shows when no vendors match
**Steps**:
1. Navigate to tender detail page
2. Mock API to return no matching vendors
3. Verify invites section becomes visible
4. Verify invite button is functional

**Expected Result**: Invites section visible and functional when no vendors match

#### 2.5 Invites Hidden During Matching
**Description**: Verify invites section hidden during active matching
**Steps**:
1. Navigate to tender detail page
2. Ensure matching is in progress
3. Verify invites section shows "Available once matching is complete"
4. Wait for matching to complete
5. Verify invites section becomes visible

**Expected Result**: Invites section hidden during matching, visible after completion

### 3. User Interactions

#### 3.1 Invite Company from Strategic Partners
**Description**: Verify invite functionality works from Strategic Partners
**Steps**:
1. Navigate to tender detail page
2. Wait for Strategic Partners to load
3. Click invite button on a company
4. Verify invite drawer opens
5. Verify company status changes to pending

**Expected Result**: Invite functionality works correctly

#### 3.2 Cancel Invite from Strategic Partners
**Description**: Verify cancel invite functionality works
**Steps**:
1. Navigate to tender detail page
2. Wait for Strategic Partners to load
3. Invite a company
4. Click cancel invite button
5. Verify company status changes back to not invited

**Expected Result**: Cancel invite functionality works correctly

#### 3.3 Invite Button State Changes
**Description**: Verify invite button states change correctly
**Steps**:
1. Navigate to tender detail page
2. Wait for Strategic Partners to load
3. Verify not invited companies show "Invite" button
4. Invite a company
5. Verify button changes to "Cancel invite"
6. Cancel invite
7. Verify button changes back to "Invite"

**Expected Result**: Button states change correctly based on invite status


