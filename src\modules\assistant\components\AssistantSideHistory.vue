<script setup lang="ts">
import ArrowIcon from '@/assets/icons/arrow-icon-2.svg';
import { DateTime } from 'luxon';
import { ref, computed, useTemplateRef, watch } from 'vue';
import { useInfiniteScroll } from '@vueuse/core';
import { nioAxios } from '@/axios.ts';
import type { DateCategory, GroupedHistoryItems, HistoryItem } from '@/modules/assistant/types/assistant-history.ts';
import { categoryConfig } from '@/config/assistant.ts';
import Loader from '@/common/components/Loader.vue';
import ArrowGreyDown from '@/assets/icons/arrow-grey-down.svg';
import ThreeDots from '@/assets/icons/three-dots.svg';
import NioExpandableSearchInput from '@/common/components/UI/NioExpandableSearchInput.vue';
import BasicPopover from '@/common/components/popover/BasicPopover.vue';
import DeleteIcon from '@/assets/icons/x-icon.svg';

const data = ref<HistoryItem[]>([]);
const searchQuery = ref('');
const debouncedQuery = ref('');
let debounceTimeout: ReturnType<typeof setTimeout> | null = null;
const page = ref(1);
const loading = ref(false);
const hasMore = ref(true);
const scrollAreaEl = useTemplateRef<HTMLDivElement>('scrollAreaEl');
const filters = [
  { label: 'All', value: '' },
  { label: 'Drafts', value: 'draft' },
  { label: 'With Tenders', value: 'with_tender' }
];
const selectedFilter = ref(filters[0]);
const menuVisible = ref(false);
const toggleMenu = () => {
  menuVisible.value = !menuVisible.value;
};
const selectFilter = (filter: { label: string; value: string }) => {
  selectedFilter.value = filter;
  menuVisible.value = false;
};
const fetchData = async() => {
  if (loading.value || !hasMore.value) {
    return;
  }
  loading.value = true;
  try {
    const response = await nioAxios.get('/enterprise/assistant/rfp', {
      params: {
        page: page.value,
        type: selectedFilter.value.value,
        search: debouncedQuery.value
      }
    });
    const newData = response.data.data.map((item: any) => ({
      id: item.id,
      title: item.title,
      date: item.created_at,
      tender_id: item.tender_id
    }));

    data.value.push(...newData);
    data.value.sort(
      (a, b) => DateTime.fromISO(b.date).toMillis() - DateTime.fromISO(a.date).toMillis()
    );
    hasMore.value = !!response.data.links?.next;
    page.value++;
  } catch (error) {
    console.error('Error fetching data:', error);
  } finally {
    loading.value = false;
  }
};
const groupByDateCategory = (items: HistoryItem[]): Partial<GroupedHistoryItems> => {
  const grouped = items.reduce<GroupedHistoryItems>(
    (acc, item) => {
      const itemDate = DateTime.fromISO(item.date).startOf('day');
      const category: DateCategory =
            (Object.entries(categoryConfig) as [DateCategory, (date: DateTime) => boolean][])
              .find(([, isMatch]) => isMatch(itemDate))?.[0] || 'Previous 7 days';
      acc[category] = acc[category] || [];
      acc[category].push(item);
      return acc;
    },
    { Today: [], Yesterday: [], 'Previous 7 days': [] }
  );
  return Object.fromEntries(Object.entries(grouped).filter(([, items]) => items.length > 0));
};
watch(searchQuery, newQuery => {
  if (debounceTimeout) {
    clearTimeout(debounceTimeout);
  }
  debounceTimeout = setTimeout(() => {
    debouncedQuery.value = newQuery.trim().toLowerCase();
  }, 500);
});
watch([() => selectedFilter.value, () => debouncedQuery.value], () => {
  data.value = [];
  page.value = 1;
  hasMore.value = true;
  fetchData();
});
const groupedData = computed(() => groupByDateCategory(data.value));
useInfiniteScroll(scrollAreaEl, fetchData, {
  distance: 10,
  canLoadMore: () => true
});
const deleteItem = async(id: string, tenderId?: string | null) => {
  if (tenderId) {
    return;
  }
  try {
    await nioAxios.delete(`/enterprise/assistant/rfp/${id}`);
    data.value = data.value.filter(item => item.id !== id);
    if (data.value.length === 0) {
      hasMore.value = false;
    }
    await fetchData();
  } catch (error) {
    console.error('Error deleting item:', error);
  }
};
const hasScroll = computed(() => {
  const el = scrollAreaEl.value;
  return el ? el.scrollHeight > el.clientHeight : false;
});
</script>

<template>
  <section class="min-h-[250px] h-[100vh] rounded-15 pb-8 flex flex-col overflow-hidden bg-nio-grey-background py-4 pl-4 pr-1">
    <div class="mb-8 relative flex items-center gap-2">
      <div
        class="flex items-center gap-1 cursor-pointer text-nio-grey-700 text-sm font-medium -tracking-028px leading-4"
        @click="toggleMenu"
      >
        <span>{{ selectedFilter.label }}</span>
        <ArrowGreyDown class="w-3 h-3 text-nio-grey-700" />
      </div>
      <div
        v-if="menuVisible"
        class="absolute left-0 top-full mt-1 bg-white border border-nio-grey-100  rounded-15 w-[9rem] z-50 p-1.5"
      >
        <button
          v-for="(filter) in filters"
          :key="filter.value"
          class="w-full text-left px-1 py-2 hover:bg-gray-100 text-nio-grey-500 hover:rounded-10 font-medium text-sm"
          :class="{ '!text-nio-black': filter.value === selectedFilter.value }"
          @click="selectFilter(filter)"
        >
          {{ filter.label }}
        </button>
      </div>
      <div class="flex gap-2 ml-auto mr-3">
        <NioExpandableSearchInput v-model="searchQuery" />
      </div>
    </div>
    <div ref="scrollAreaEl" class="flex flex-col flex-1 min-h-0 overflow-y-auto custom-scrollbar-01">
      <div v-for="(items, category) in groupedData" :key="category" class="last:[&_.custom-divider]:hidden">
        <h4 class="text-nio-grey-500 font-semibold text-sm mb-4">
          {{ category }}
        </h4>
        <div class="flex flex-col gap-2.5">
          <div
            v-for="(rfp, idx) in items"
            :key="idx"
            class="group flex flex-col p-3 rounded-10 cursor-pointer hover:bg-nio-grey-100 transition-colors relative min-h-[42px] group-hover:min-h-[57px]"
            :class="{ 'mr-4': !hasScroll, 'mr-4.5': hasScroll }"
            @click="$router.push(`/assistant/rfp/${rfp.id}`)"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center flex-1 min-w-0 pr-8">
                <div
                  class="min-w-5 min-h-5 w-5 h-5 p-[5px] rounded-full flex items-center justify-center"
                  :class="{
                    'group-hover:bg-nio-grey-300 bg-nio-grey-100': !rfp.tender_id,
                    'bg-nio-green-text': rfp.tender_id
                  }"
                >
                  <ArrowIcon
                    v-if="rfp.tender_id"
                    class="text-nio-grey-100 [&_path]:fill-nio-grey-100 group-hover:!fill-nio-white"
                  />
                </div>
                <div
                  class="truncate font-medium tracking-[-0.32px] leading-5 flex-1 ml-2"
                  :title="rfp.title ?? $t('misc.no-name')"
                >
                  {{ rfp.title ?? $t('misc.no-name') }}
                </div>
              </div>

              <BasicPopover>
                <div
                  class="w-[28px] h-[28px] flex items-center justify-center cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute right-0 top-1/2 -translate-y-1/2 pt-5"
                >
                  <ThreeDots class="h-5 w-5" />
                </div>
                <template #popover-content>
                  <div class="flex flex-col w-[9rem]">
                    <button
                      class="text-sm font-medium py-1 px-1 flex items-center gap-2 rounded-10 transition-colors"
                      :class="{
                        'text-nio-grey-700 hover:bg-nio-grey-100/20 cursor-pointer': !rfp.tender_id,
                        'text-nio-grey-400 cursor-not-allowed opacity-50': rfp.tender_id
                      }"
                      @click="deleteItem(rfp.id, rfp.tender_id)"
                    >
                      <div
                        class="w-4 h-4 rounded-full border-[1.2px] border-nio-grey-700 flex items-center justify-center"
                        :class="{ 'opacity-50': rfp.tender_id }"
                      >
                        <DeleteIcon />
                      </div>
                      <span>Delete</span>
                    </button>
                  </div>
                </template>
              </BasicPopover>
            </div>
            <span class="text-nio-grey-500 text-sm font-normal leading-[21px] tracking-[-0.28px] mt-[5px]">
              {{ DateTime.fromISO(rfp.date).toFormat('dd.MM. / HH:mm') }}
            </span>
          </div>
        </div>
        <div class="custom-divider h-[1px] bg-nio-grey-100 mb-4 mt-1" />
      </div>
      <div v-if="loading" class="flex flex-col items-center mt-2 text-nio-grey-400">
        <Loader />
      </div>
    </div>
  </section>
</template>
