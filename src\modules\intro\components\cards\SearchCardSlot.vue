<template>
  <div class="relative h-full p-[30px] pt-[15px] pr-[15px]" :class="pendingCompanies ? 'cursor-not-allowed' : (showInput ? 'cursor-default' : 'cursor-pointer')">
    <div
      v-if="!showInput"
      class="absolute left-[30px] bottom-[40px]"
    >
      <h3 class="text-h4 text-[#0071E3]">
        {{ $t('intro.search-companies') }}
      </h3>
      <p class="block text-p-x text-[#A1A1A1]">
        {{ $t('intro.start-writing') }}
      </p>
    </div>
    <div v-if="showInput" class="w-full">
      <div
        class="w-full flex items-center mb-[44px]"
      >
        <div class="flex-1 relative">
          <input
            ref="inputEl"
            v-model="inputText"
            type="search"
            class="remove-x pr-2 w-full h-[24px] bg-transparent outline-none text-xl"
            placeholder="Search..."
            @input="onChangedInputText"
          >
          <div v-if="inputText.length < 3" class="absolute text-sm text-nio-red-500">
            Min. 3 characters*
          </div>
        </div>
        <div
          v-if="inputText || foundVendors?.length"
          role="button"
          aria-label="Reset search"
          class="text-base text-nio-grey-500 cursor-pointer"
          @click="resetSearch"
        >
          Reset
        </div>
        <div class="bg-nio-grey-background w-10 h-10 rounded-full ml-[15px]">
          <SearchIcon />
        </div>
      </div>
      <div class="flex items-center gap-[10px] gap-y-[12px] flex-wrap">
        <FoundCompany
          v-for="(foundVendor, idx) in inputText ? foundVendors : filteredRecommendations"
          :key="foundVendor.id"
          :title="foundVendor.name"
          :region="foundVendor.region"
          @click="addVendorToList(foundVendor, idx)"
        />
      </div>
    </div>
    <div
      v-if="!loadingBackend && foundVendors.length === 0 && inputText?.length > 2"
      class="absolute -translate-y-1/2 top-[177.5px] text-h5 -translate-x-1/2 left-1/2 text-nio-red-500 px-[14px] py-[7px] rounded-15 border border-nio-blue-outline-stroke-400"
    >
      {{ $t('intro.no-result-search') }}
    </div>
    <div
      v-if="!showInput"
      class="absolute left-[30px] top-[30px] w-[40px] h-[40px] rounded-full bg-[#F3F4F4]"
    >
      <SearchIcon />
    </div>
    <div
      v-if="showInput"
      class="absolute x-button -top-[15px] right-[-30px] w-[30px] h-[30px] bg-[rgba(249,249,249,0.60)] border border-nio-blue-400 rounded-full flex items-center justify-center shadow"
      :class="loadingBackend ? 'cursor-not-allowed' : 'cursor-pointer hover:bg-[#F9F9F9]'"
      data-close-btn
      role="button"
      aria-label="Close search"
      @click.stop.prevent="loadingBackend ? undefined : closeSearch()"
    >
      <Loader v-if="loadingBackend" />
      <CloseIcon
        v-else
        class="w-[13px] h-[13px]"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchIcon from '@/assets/icons/search-icon.svg';
import { computed, ref, watch } from 'vue';
import CloseIcon from '@/assets/icons/close-icon.svg';
import FoundCompany from '@/modules/intro/components/FoundCompany.vue';
import { debounce } from 'lodash-es';
import { nioAxios } from '@/axios.ts';
import type { FoundVendorType } from '@/modules/intro/types/search-companies.ts';
import { useVendorsStore } from '@/modules/intro/stores/companiesStore.ts';
import Loader from '@/common/components/Loader.vue';
import { maxVendors } from '@/config/intro.ts';
import { toast } from '@/common/utils/NotificationService.ts';
import { useAuthStore } from '@/modules/auth/stores/auth-store.ts';

interface Props {
  showInput: boolean,
  pendingCompanies: boolean,
}

const vendorsStore = useVendorsStore();
const authStore = useAuthStore();

const props = defineProps<Props>();
const emit = defineEmits(['closeSearch', 'addedVendorToList']);
const inputText = defineModel<string>({ default: '' });
const inputEl = ref<HTMLInputElement>();
const loadingBackend = ref(false);
let controller = new AbortController();
const foundVendors = ref<FoundVendorType[]>([]);
const recommendedVendors = ref<FoundVendorType[]>([]);

const currentVendorStoreIds = computed(() => vendorsStore?.vendors?.map(v => v.id));
const currentProfileCompanyIds = computed(() =>
  authStore?.userProfile?.workspaces
    ?.flatMap(workspace => workspace.companies.map(company => company.id)) || []
);

const filteredRecommendations = computed(() =>
  recommendedVendors.value
    .filter(v => !currentVendorStoreIds.value?.includes(v.id))
    .filter(v => !currentProfileCompanyIds.value?.includes(v.id))
  ?? []
);

const resetSearch = () => {
  inputText.value = '';
  foundVendors.value = [];
  recommendedVendors.value = [];
  inputEl.value?.focus();
};

const closeSearch = () => {
  controller.abort();
  inputText.value = '';
  foundVendors.value = [];
  emit('closeSearch');
};

const fetchRecommendations = async() => {
  const response = await nioAxios.get<{ data: FoundVendorType[] }>('/enterprise/companies/search/recommended', { signal: controller.signal });
  recommendedVendors.value = response.data?.data?.filter(v => !currentVendorStoreIds.value?.includes(v.id)) ?? [];
};

const changedInputText = async(input: Event) => {
  const targetValue = (input.target as HTMLInputElement)?.value;
  controller.abort();
  controller = new AbortController();
  if (targetValue?.length < 3) {
    loadingBackend.value = false;
    return;
  }
  try {
    const response = await nioAxios.get<{ data: FoundVendorType[] }>(`/enterprise/companies/search?payload=${targetValue}`,
      { signal: controller.signal }
    );
    foundVendors.value = response.data?.data
      ?.filter(v => !currentVendorStoreIds.value?.includes(v.id))
      ?.filter(v => !currentProfileCompanyIds.value?.includes(v.id))
    ?? [];
  } catch {
    console.error('Failed to search');
  } finally {
    loadingBackend.value = false;
  }
};

const debouncedChangedInputText = debounce(changedInputText, 440);

const onChangedInputText = (input: Event) => {
  loadingBackend.value = true;
  debouncedChangedInputText(input);
};

const addVendorToList = (vendor: FoundVendorType, idx: number) => {
  if (vendorsStore.vendors.length >= maxVendors) {
    toast.show(
      'Limit reached!',
      `Limit of ${maxVendors} vendors have been reached.`,
      'error'
    );
    return;
  }
  emit('addedVendorToList');
  vendorsStore.vendors.push({
    status: 'verified',
    id: vendor.id,
    importId: vendor.id,
    name: vendor.name,
  });
  if (inputText.value?.length > 0) {
    foundVendors.value.splice(idx, 1);
  }
};

watch(() => inputEl.value, newValue => {
  if (newValue) {
    newValue.focus();
  }
});

watch(() => props.showInput, newVal => {
  if (!newVal) {
    resetSearch();
  } else {
    fetchRecommendations();
  }
});
</script>

<style lang="css" scoped>
.remove-x {
  &::-webkit-search-cancel-button {
    display: none;
  }
}
</style>
