import { type Locator, type Page } from '@playwright/test';
import { AssistantBaseStep } from './AssistantBaseStep';

export class AssistantStep1 extends AssistantBaseStep {
  readonly heading: Locator;
  readonly rfpTitleInput: Locator;
  readonly locationSelect: Locator;
  readonly locationOptions: Locator;
  readonly startDateInput: Locator;
  readonly endDateInput: Locator;
  readonly timezoneSelect: Locator;
  readonly primaryIndustrySelect: Locator;
  readonly descriptionEditButton: Locator;

  constructor(page: Page) {
    super(page);
    this.heading = page.getByRole('heading', { name: 'General Information' });
    this.rfpTitleInput = page.getByLabel('Title');
    this.locationSelect = page.getByRole('button', { name: 'Location' });
    this.locationOptions = this.getFormCellByLabel('Preferred Vendor Location').getByRole('listitem');
    this.startDateInput = this.getFormCellByLabel('Expected Start & End Date').getByRole('textbox').nth(0);
    this.endDateInput = this.getFormCellByLabel('Expected Start & End Date').getByRole('textbox').nth(1);
    this.timezoneSelect = page.getByRole('button', { name: 'Time Zone' });
    this.primaryIndustrySelect = page.getByRole('button', { name: 'Project Industry' });
    this.descriptionEditButton = page.getByRole('button', { name: 'Description' });
  }
}
