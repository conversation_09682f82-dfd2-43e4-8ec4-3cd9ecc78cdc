import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { runShellScript } from './cli-helper.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const apiCloneDir = process.env.NIO_API_GIT_CLONE_DIR || 'api';

try {
  console.log(`➡️ Cleaning up api at: ${apiCloneDir}`);
  await runShellScript(path.join(__dirname, 'api-cleanup.sh'), [apiCloneDir]);
  console.log('✅ Cleanup complete');
} catch (err) {
  console.error('❌ Cleanup failed:', err.message);
  process.exit(1);
}
