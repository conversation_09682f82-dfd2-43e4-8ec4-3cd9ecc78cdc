<template>
  <DetailPopover01 v-model="isOpened">
    <!--        <img-->
    <!--          v-if="projectDetail.image && !imageHidden"-->
    <!--          :src="projectDetail.image"-->
    <!--          class="h-[320px] w-full rounded-20 object-cover object-center"-->
    <!--          alt="Project image"-->
    <!--          @error="imageHidden = true;"-->
    <!--        >-->
    <div v-if="projectDetail">
      <header class="pt-12 pb-14">
        <h1 class="text-h1 text-nio-black-900 text-balance text-center">
          {{ projectDetail.name }}
        </h1>
        <div class="flex items-center justify-center gap-2.5">
          <div class="px-3 py-1.5 bg-nio-grey-background-60 text-nio-grey-700 text-sm rounded-5">
            {{ projectDetail.country }}
          </div>
          <div class="px-3 py-1.5 bg-nio-grey-background-60 text-nio-grey-700 text-sm rounded-5">
            {{ projectDetail.created_at }}
          </div>
        </div>
      </header>

      <section v-if="projectDetail.matching?.score || projectDetail.matching?.tech_score" class="grid grid-cols-2 gap-2.5 bg-nio-grey-background-90 backdrop-blur-3xl rounded-45 most-used-shadow p-4 mb-14">
        <div v-if="projectDetail.matching?.score" class="bg-nio-white rounded-30 relative level-card-shadow flex flex-col p-8">
          <div class="flex w-full justify-between items-center mb-14">
            <div class="py-2 px-3 text-sm text-nio-grey-700 rounded-30 border border-nio-grey-200">
              Total Score
            </div>
            <div
              :style="{
                width: '28px',
                height: '28px',
                backgroundImage: `${projectDetail.matching.score === 100 ? 'conic-gradient(#5DA773 0%, #5DA773 100%)' : `conic-gradient(#5DA773 0%, #5DA773 ${projectDetail.matching.score}%, #E8E8E8 ${projectDetail.matching.score}%)`}`,
                borderRadius: '50%'
              }"
            />
          </div>
          <div class="mt-auto mb-0">
            <AnimatedTextSlot class="text-balance custom-span-text-color text-base">
              The match score reflects how well this vendor aligns with your RFP across all key attributes, including location, technology fit, rate alignment, and other criteria. It provides an overall measure of compatibility to help you evaluate the vendor's suitability for your project.
            </AnimatedTextSlot>
            <div class="text-nio-green-text text-[44px] mt-7">
              {{ projectDetail.matching.score }}%
            </div>
          </div>
        </div>
        <div v-if="projectDetail.matching?.tech_score" class="bg-nio-white rounded-30 relative level-card-shadow flex flex-col p-8">
          <div class="flex w-full justify-between items-center mb-14">
            <div class="py-2 px-3 text-sm text-nio-grey-700 rounded-30 border border-nio-grey-200">
              Technology
            </div>
            <div
              :style="{
                width: '28px',
                height: '28px',
                backgroundImage: `${projectDetail.matching.tech_score === 100 ? 'conic-gradient(#5DA773 0%, #5DA773 100%)' : `conic-gradient(#5DA773 0%, #5DA773 ${projectDetail.matching.tech_score}%, #E8E8E8 ${projectDetail.matching.tech_score}%)`}`,
                borderRadius: '50%'
              }"
            />
          </div>
          <div class="mt-auto mb-0 w-full">
            <div class="flex flex-wrap items-center gap-2 mb-8">
              <div v-for="technology in projectDetail.technologies" :key="technology" class="text-nio-grey-700 px-[6px] py-[3px] border border-nio-grey-000 rounded-5 bg-nio-grey-000">
                {{ technology }}
              </div>
            </div>
            <div class="flex items-center w-full h-14 relative rounded-15 overflow-hidden">
              <div :style="{width: `${projectDetail.matching.tech_score}%`}" class="bg-nio-green-text h-full rounded-l-15" :class="projectDetail.matching.tech_score === 100 ? 'rounded-r-15' : ''" />
              <div class="flex-1 bg-nio-grey-000 h-full rounded-r-15" />
              <div class="absolute -translate-y-1/2 top-1/2 right-2.5 text-2xl text-nio-green-text mix-blend-multiply">
                {{ projectDetail.matching.tech_score }}%
              </div>
            </div>
          </div>
        </div>
      </section>

      <section v-if="aboutArray.length > 0" class="mb-14">
        <h3 class="block py-2 px-3 rounded-30 border border-nio-grey-200 text-sm w-fit text-nio-grey-700 mb-8">
          About project
        </h3>
        <div v-if="aboutArray.length > 1">
          <!-- eslint-disable -->
          <template v-for="(text, idx) in aboutArray" :key="1">
            <p
              :class="[
                  idx === 0 ? 'text-nio-black-900 text-h5' :
                  idx % 2 === 0 ? 'text-nio-grey-500 text-h5' : 'text-nio-grey-500 text-h5 ml-auto mr-0'
                ]"
              class="w-1/2"
              v-html="text"
            />
          </template>
          <!-- eslint-enable -->
        </div>
        <div>
          <!-- eslint-disable -->
          <p
            class="w-full text-nio-black-900 text-h5"
            v-html="aboutArray[0]"
          />
          <!-- eslint-enable -->
        </div>
      </section>

      <section class="grid grid-cols-2 gap-2.5 h-[330px]">
        <div class="bg-black rounded-30 relative level-card-shadow flex flex-col justify-between p-5">
          <div class="flex w-full justify-between items-center">
            <div class="text-nio-grey-200 px-3 py-[6px] text-sm rounded-5 bg-nio-grey-background-15">
              Industries
            </div>
          </div>
          <div class="flex flex-wrap items-center gap-2">
            <div v-for="(industry, idx) in projectDetail.industries" :key="idx" class="text-nio-grey-200 px-3 py-[5px] border border-nio-grey-500 rounded-30 text-sm">
              {{ industry }}
            </div>
          </div>
        </div>
        <div class="bg-black rounded-30 relative level-card-shadow flex flex-col justify-between p-5">
          <div class="flex w-full justify-between items-center">
            <div class="text-nio-grey-200 px-3 py-[6px] text-sm rounded-5 bg-nio-grey-background-15">
              Technologies
            </div>
          </div>
          <div class="flex flex-wrap items-center gap-2">
            <div v-for="technology in projectDetail.technologies" :key="technology" class="text-nio-grey-200 px-3 py-[5px] border border-nio-grey-500 rounded-30 text-sm">
              {{ technology }}
            </div>
          </div>
        </div>
      </section>
    </div>
    <div v-else class="w-[63.75rem] my-16 mx-auto max-w-[calc(100vw-120px)] p-4 relative bg-nio-grey-background-60 backdrop-blur-[50px] rounded-50 border-x border-x-nio-blue-outline-stroke-400 border-t border-t-nio-blue-outline-stroke-400">
      No data
    </div>
  </DetailPopover01>
</template>

<script setup lang="ts">
import AnimatedTextSlot from '@/common/components/AnimatedTextSlot.vue';
import type { ProjectDetailMinimal } from '@/modules/tenders/types/tenders-types.ts';
import { computed } from 'vue';
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';

interface Props {
  projectDetail?: ProjectDetailMinimal
}

const props = defineProps<Props>();
const isOpened = defineModel<boolean>();
// const imageHidden = ref(false);

const splitHtmlByBr = (html: string | undefined): string[] => {
  if (!html) {
    return [];
  }
  // Create a temporary DOM element to parse the HTML string
  const tempElement = document.createElement('div');
  tempElement.innerHTML = html;

  // Replace <br> tags with a unique delimiter
  const delimiter = '__DELIMITER__';
  tempElement.innerHTML = tempElement.innerHTML.replace(/<br\s*\/?>/gi, delimiter);

  // Extract text content, split it by the delimiter, and trim whitespace
  return tempElement.textContent
    ?.split(delimiter)
    .map(text => text.trim())
    .filter(text => text) || [];
};

const aboutArray = computed(() => splitHtmlByBr(props.projectDetail?.about));

</script>

<style scoped lang="css">
.level-card-shadow {
  box-shadow: 0 71px 20px 0 rgba(207, 207, 207, 0.00), 0 45px 18px 0 rgba(207, 207, 207, 0.01), 0 25px 15px 0 rgba(207, 207, 207, 0.05), 0 11px 11px 0 rgba(207, 207, 207, 0.09), 0 3px 6px 0 rgba(207, 207, 207, 0.10);
}

.custom-span-text-color {
  --blur-anim-color: var(--nio-blue-800);
}
</style>
