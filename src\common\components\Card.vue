<template>
  <div
    role="button"
    class="intro-card relative will-change-transform min-w-[255px] card min-h-[190px] bg-[rgba(249,249,249,0.60)] transition-all duration-300 ease-in-out rounded-30!"
    :class="{
      'hover:bg-nio-grey-background-90 cursor-pointer': hoverEnabled,
      'cursor-default': !hoverEnabled
    }"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  hoverScaleActive?: boolean;
  hoverEnabled?: boolean;
}

withDefaults(defineProps<Props>(), {
  hoverScaleActive: true,
  hoverEnabled: true
});
</script>

<style lang="css">
.intro-card:hover:has(.x-button) {
  background-color: rgba(249, 249, 249, 0.60);
}
</style>
