export enum ConsultantTableType {
  AVAILABLE,
  ENGAGED,
}

export type Company = {
  id: string;
  name: string;
  country: string;
  headquarters?: string;
};

export type CandidateBase = {
  id: string;
  name: string;
  profession: string;
  seniority: string;
  years_of_experience: number;
  rate: number;
  country: string;
  company: Company;
};

export type BenchSpecialist = {
  id: string;
  available_from: string;
  available_to: string;
};

export type Assignment = {
  company: Pick<Company, 'id' | 'name'>;
  tender?: {
    id: string;
    name: string;
  };
  tender_position?: {
    id: string;
    name: string;
  };
  start_date?: string;
  end_date?: string;
  manager?: {
    id: number;
    name: string;
    surname: string;
    position: string;
    division: string;
    department: string;
  }
};

type CandidateRowTypeMap = {
  [ConsultantTableType.AVAILABLE]: { bench_specialist: BenchSpecialist; assignment?: never };
  [ConsultantTableType.ENGAGED]: { bench_specialist?: never; assignment: Assignment };
};

export type CandidateRow<T extends ConsultantTableType> = CandidateBase & CandidateRowTypeMap[T];

export type CandidatesResponse<T extends ConsultantTableType> = {
  data: CandidateRow<T>[];
};
