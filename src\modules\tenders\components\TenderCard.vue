<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { DateTime } from 'luxon';
import type { Tender } from '@/modules/tenders/types/tenders-types';
import NioTag from '@/common/components/NioTag.vue';
import type { TenderState } from '@/modules/tenders/types/tenders-types';
import { MapPin } from 'lucide-vue-next';

const props = defineProps<{
  tender: Tender;
}>();
const router = useRouter();

const tenderState = computed<TenderState>(() => props.tender.status);
const formattedDate = computed(() => {
  if (!props.tender.created_at) {
    return '';
  }
  return DateTime.fromISO(props.tender.created_at).toFormat('d LLLL yy');
});

const navigateToDetail = () => {
  router.push({ name: 'detail', params: { id: props.tender.id } });
};
</script>

<template>
  <div
    class="flex flex-col justify-between border border-nio-border-default rounded-20 p-5 bg-nio-white w-full"
  >
    <div class="flex flex-col">
      <div class="text-[14px] text-nio-grey-500 leading-[14px] mb-4 flex items-center">
        <span class="mr-1">By</span>
        <span class="text-[14px] text-nio-blue-800 leading-[14px]">
          @{{ tender.created_by }}
        </span>
        <span class="text-[14px] text-nio-grey-500 leading-[14px] ml-1">
          {{ formattedDate }}
        </span>

        <div class="inline-flex gap-2 ml-auto">
          <div
            v-if="tenderState !== 'ended'"
            class="inline-flex px-2 py-1 text-[12px] font-bold text-nio-blue-800 rounded-5 bg-nio-grey-11 leading-[14px] uppercase"
          >
            {{ tenderState }}
          </div>
          <div class="flex items-center">
            <time
              class="inline-flex px-2 py-1 text-[12px] font-bold text-nio-grey-500 rounded-5 bg-nio-grey-100 leading-[14px] uppercase whitespace-nowrap"
              :datetime="tender.submissions_deadline"
              aria-label="Submission deadline"
            >
              {{ tenderState !== 'ended' ? 'Ends' : 'Ended' }} {{ DateTime.fromISO(tender.submissions_deadline).toFormat('d LLLL yyyy') }}
            </time>
          </div>
        </div>
      </div>

      <router-link
        :to="{name: 'detail', params: {id: tender.id}}"
        class="text-[26px] font-normal text-nio-black-900 leading-[28px] tracking-[0.52px] mb-4 hover:underline"
      >
        {{ tender.name }}
      </router-link>

      <p class="text-[14px] font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-4">
        {{ tender.description }}
      </p>

      <div class="flex flex-wrap gap-2">
        <NioTag size="sm">
          <span class="flex items-center"><MapPin class="w-3 h-3 mr-1 " /> {{ tender.region }}</span>
        </NioTag>
        <NioTag size="sm">
          {{ tender.main_industry }}
        </NioTag>
      </div>
    </div>

    <div class="flex justify-between items-center mt-3">
      <div>
        <div class=" text-nio-grey-900 flex gap-4">
          <div class="text-sm">
            <strong>{{ tender.total_invited_companies ?? 0 }}</strong> Invitations sent
          </div>
          <div class="text-sm border-l border-nio-border-default pl-4">
            <strong>{{ tender.total_viewed_companies ?? 0 }}</strong> Tender views
          </div>
        </div>
      </div>

      <button
        role="link"
        :aria-label="`Open ${tender.name}`"
        class="px-5 py-2 bg-nio-blue-800 text-nio-white text-[14px] rounded-50 hover:bg-nio-blue-600-hover transition-all cursor-pointer active:scale-95"
        @click="navigateToDetail"
      >
        Open
      </button>
    </div>
  </div>
</template>
