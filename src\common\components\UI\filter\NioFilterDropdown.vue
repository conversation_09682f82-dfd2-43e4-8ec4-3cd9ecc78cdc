<script setup lang="ts">
import Checkbox from 'primevue/checkbox';
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { ChevronDownIcon } from 'lucide-vue-next';
import type { FilterDesign } from '@/common/types/filter';
import NioSearch from '@/common/components/UI/NioSearch.vue';
import { normalizeSearch } from '@/common/utils/strings';

const props = withDefaults(defineProps<{
  options: { label: string; value: string }[];
  label: string;
  design?: FilterDesign;
}>(), {
  design: 'light'
});

const modelValue = defineModel<string[]>({ default: () => [] });
const expanded = ref(false);
const root = ref<HTMLElement | null>(null);
const searchText = ref('');

const filteredOptions = computed(() => {
  const q = normalizeSearch(searchText.value);
  if (!q) {
    return props.options;
  }

  return props.options.filter(opt =>
    normalizeSearch(`${opt.label} ${opt.value ?? ''}`).includes(q)
  );
});

const handleChange = (value: string, checked: boolean) => {
  modelValue.value = checked
    ? [...modelValue.value, value]
    : modelValue.value.filter(item => item !== value);
};

const handleClickOutside = (e: MouseEvent) => {
  const target = e.target as Node;
  if (root.value && !root.value.contains(target)) {
    expanded.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside, true);
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside, true);
});
</script>

<template>
  <div
    ref="root"
    :class="`relative px-4 py-1.5 rounded-full text-sm transition-all duration-200 flex items-center gap-1.5 cursor-pointer
    ${design === 'dark'
    ? 'bg-nio-grey-900 text-white hover:bg-nio-grey-700'
    : 'border border-nio-gray-300/40 text-nio-grey-900 hover:border-nio-blue-400'}`"
    @click="expanded = !expanded"
  >
    <div class="flex items-center gap-1.5">
      <slot name="label">
        {{ label }}
      </slot>
      <ChevronDownIcon class="w-4 h-4 transition-transform duration-300" :class="{ '-rotate-180': expanded }" />
    </div>

    <Transition appear>
      <div
        v-show="expanded"
        class="absolute text-nio-grey-900 top-full mt-2 z-4 bg-white p-2 left-0 rounded-10 shadow-lg w-fit min-w-full max-h-80 overflow-y-auto"
        @click.stop
      >
        <NioSearch v-model="searchText" class="mb-2 sticky top-0 shadow-[0_0_0px_10px_#ffffff]" :placeholder="'Search'" />
        <label
          v-for="opt in filteredOptions"
          :key="opt.value"
          :class="`flex items-center whitespace-nowrap cursor-pointer gap-2 select-none transition-all duration-100 py-1 px-2 rounded-5
          ${design === 'dark'
          ? 'hover:bg-nio-grey-100/60'
          : 'hover:bg-nio-blue-100'}`"
          :for="`opt-${opt.value}`"
        >
          <Checkbox
            binary
            :input-id="`opt-${opt.value}`"
            :value="opt.value"
            :pt="{
              input:{ class: 'hidden' },
              box: {
                class:
                  'border border-nio-grey-900/20 rounded-[4px] w-4 h-4 data-[p=checked]:text-white ' +
                  (design === 'dark'
                    ? 'data-[p=checked]:bg-nio-grey-900 data-[p=checked]:border-nio-grey-900'
                    : design === 'light'
                      ? 'data-[p=checked]:bg-nio-blue-500 data-[p=checked]:border-nio-blue-500'
                      : '')
              }
            }"
            @change="handleChange(opt.value, ($event.target as HTMLInputElement).checked)"
          />
          <span>{{ opt.label }}</span>
        </label>
        <label
          v-if="filteredOptions.length === 0"
          class="flex items-center whitespace-nowrap select-none transition-all duration-100 py-1 px-2 rounded-5"
        >
          <span>No option matched your search.</span>
        </label>
      </div>
    </Transition>
  </div>
</template>
