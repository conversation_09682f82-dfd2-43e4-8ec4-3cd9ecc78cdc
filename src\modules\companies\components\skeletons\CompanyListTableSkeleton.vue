<script setup lang="ts">
import PageCard from '@/common/components/PageCard.vue';
import NioTable from '@/common/components/UI/table/NioTable.vue';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';

const columns = [
  { key: 'name', label: 'Vendor', width: 2 },
  { key: 'location', label: 'Location', width: 1 },
  { key: 'category', label: 'Category', width: 1 },
  { key: 'employees', label: 'Employees', width: 1 },
  { key: 'status', label: 'Status', width: 1 },
  { key: 'actions', label: '', width: 1, align: 'right' as const },
];
</script>

<template>
  <PageCard>
    <NioTable :columns="columns">
      <NioTableRow v-for="i in 5" :key="i" :columns="columns">
        <template #name>
          <div class="w-full h-[30px] bg-gray-200 rounded-25" />
        </template>
        <template #location>
          <div class="w-full h-[30px] bg-gray-200 rounded-25" />
        </template>
        <template #category>
          <div class="w-full h-[30px] bg-gray-200 rounded-25" />
        </template>
        <template #employees>
          <div class="w-full h-[30px] bg-gray-200 rounded-25" />
        </template>
        <template #status>
          <div class="w-full h-[30px] bg-gray-200 rounded-25" />
        </template>
        <template #actions>
          <div class="w-full h-[30px] bg-gray-200 rounded-25" />
        </template>
      </NioTableRow>
    </NioTable>
  </PageCard>
</template>
