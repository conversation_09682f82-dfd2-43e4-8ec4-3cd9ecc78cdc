import { type Locator, type Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class TendersPage extends BasePage {
  readonly urlPath: string = '/tenders';
  readonly tendersList: Locator;
  readonly tenderItems: Locator;

  constructor(page: Page) {
    super(page);
    this.tendersList = page.getByRole('list', { name: 'Tenders list' });
    this.tenderItems = this.tendersList.getByRole('listitem');
  }

  async goto() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Tenders' }).click();
    await this.page.waitForURL(this.urlPath);
  }

  async openFirstTender() {
    await this.tenderItems.first().getByRole('button').click();
    await this.page.waitForURL(/tenders\/[^/]+\/detail/);
  }
}
