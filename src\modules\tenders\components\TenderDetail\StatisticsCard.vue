<template>
  <div class="flex gap-[10px] p-[15px] rounded-30 border border-nio-blue-400 bg-nio-grey-background">
    <div
      v-for="(card, index) in cards"
      :key="index"
      class="flex flex-col justify-between w-full p-[15px] bg-white custom-shadow rounded-15 h-[150px] relative"
      :class="index === 0 ? 'flex-[0.5]' : 'flex-[0.25]'"
    >
      <div class="flex justify-between">
        <div
          class="top-[15px] left-[15px] px-2 py-1 text-[14px] rounded-30 text-nio-grey-700 border border-nio-grey-200 leading-4"
        >
          {{ card.label }}
        </div>

        <component :is="card.icon" v-if="card.icon" class="w-6 h-6 text-nio-grey-700" />
      </div>

      <div
        class="text-[42px] font-normal mt-auto"
        :class="{
          'text-nio-grey-700': card.label === 'Invited',
          'text-nio-green-text': card.label === 'Views' || card.label === 'Proposals',
        }"
      >
        {{ card.value }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import GreyArrow from '@/assets/icons/grey-arrow.svg';
import PieThird from '@/assets/icons/pie-third.svg';
import CheckCircle from '@/assets/icons/check-circle.svg';

interface StatisticsProps {
  invited?: number | null;
  views?: number | null;
  proposals?: number | null;
}

const props = defineProps<StatisticsProps>();

const cards = [
  { label: 'Invited', value: isNaN(Number(props.invited)) ? 0 : Number(props.invited), icon: GreyArrow },
  { label: 'Views', value: isNaN(Number(props.views)) ? 0 : Number(props.views), icon: PieThird },
  { label: 'Proposals', value: isNaN(Number(props.proposals)) ? 0 : Number(props.proposals), icon: CheckCircle },
];
</script>

<style scoped>
.custom-shadow {
  box-shadow: 0px 71px 20px 0px rgba(207, 207, 207, 0.00), 0px 45px 18px 0px rgba(207, 207, 207, 0.01), 0px 25px 15px 0px rgba(207, 207, 207, 0.05), 0px 11px 11px 0px rgba(207, 207, 207, 0.09), 0px 3px 6px 0px rgba(207, 207, 207, 0.10);
}
</style>
