<script setup lang="ts">
import NioDateInputSingle from '@/common/components/UI/NioDateInputSingle.vue';
import type { DynamicFormItemData, FormErrors } from '@/common/utils/forms';
import { reactive, ref, watch } from 'vue';
import type { Tender } from '../../types/tenders-types';
import { DateTime } from 'luxon';
import { nioAxios } from '@/axios';
import { toast } from '@/common/utils/NotificationService';
import type { AxiosError } from 'axios';
import { CustomEvents, sendCustomEvent } from '@/common/utils/custom-events';

interface Props {
  tenderData?: Tender;
  externalSubmit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  tenderData: undefined,
  isExternal: false,
});

const emit = defineEmits<{
  (e: 'submit-success'): void;
}>();

const inputData = reactive<DynamicFormItemData>({
  name: 'submission-date',
  component: {
    id: 'date-single',
    payload_key: 'submission-date',
  },
});
const inputError = ref<FormErrors[0]>();
const submitting = ref(false);
let valueHasChanged = false;

const dateValue = ref<string | undefined>(
  props.tenderData?.submissions_deadline
    ? DateTime.fromISO(props.tenderData.submissions_deadline).toFormat("yyyy-LL-dd'T'HH:mm") as string
    : DateTime.now().toFormat("yyyy-LL-dd'T'HH:mm")
);

const onSubmit = async() => {
  if (submitting.value || !valueHasChanged) {
    return;
  }
  inputError.value = undefined;
  submitting.value = true;
  try {
    const response = await nioAxios.put<{data: Tender}>(`/enterprise/tenders/${props.tenderData?.id}`, {
      submissions_deadline: dateValue.value ? dateValue.value : null,
    });
    toast.show('Tender updated', 'Successfully updated submission deadline.', 'success');
    sendCustomEvent({ updatedTender: response?.data?.data }, CustomEvents.TenderDetailUpdated);
    valueHasChanged = false;
    emit('submit-success');
  } catch (e) {
    if ((e as AxiosError<{error: any}>)?.response?.status === 422) {
      inputError.value = {
        payload_key: 'submission-date',
        message: (e as AxiosError<{error: any}>).response!.data?.error?.errors?.submissions_deadline?.join(', ') || 'Unknown validation error',
      };
    }
    toast.show('An error occurred', 'Failed to update tender.', 'error');
    if (props.externalSubmit) {
      throw e;
    }
  } finally {
    submitting.value = false;
  }
};

// Watch for changes in tenderData to update dateValue
watch(() => props.tenderData?.submissions_deadline, newDeadline => {
  if (newDeadline) {
    dateValue.value = DateTime.fromISO(newDeadline).toISODate() as string;
  } else {
    dateValue.value = DateTime.now().toISODate();
  }
});

watch(dateValue, () => {
  valueHasChanged = true;
});

defineExpose({
  submit: onSubmit,
});
</script>

<template>
  <form class="h-full px-4 py-2 overflow-y-visible" @submit.prevent="onSubmit">
    <div class="flex items-center gap-3">
      <label for="submission-date-input" class="block text-sm font-medium text-nio-grey-700 ml-0.5">
        Submission Deadline
      </label>
      <NioDateInputSingle
        v-model="dateValue!"
        :input-data="inputData"
        :error="inputError"
        input-id="submission-date-input"
        class="w-60"
      />
    </div>
    <hr v-if="!externalSubmit" class="mt-6">
    <button
      v-if="!externalSubmit"
      type="submit"
      :disabled="submitting"
      class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px] disabled:opacity-50 disabled:cursor-not-allowed mt-4"
    >
      {{ submitting ? 'Submitting...' : 'Submit' }}
    </button>
  </form>
</template>
