import { defineStore } from 'pinia';
import { ref } from 'vue';
import { nioAxios } from '@/axios';
import type { Candidate } from '@/modules/tenders/types/tenders-types';

export interface Company {
    name: string;
    id: string;
}

export const useQuestionStore = defineStore('question-store', () => {
  const tenderId = ref<string>();
  const companies = ref<Company[]>([]);
  const companyProfile = ref<Company>();
  const candidateProfile = ref<Candidate>();

  const getVendors = async() => {
    const url = '/enterprise/workspace';
    try {
      const response = await nioAxios.get(url);
      companies.value = response.data.data.companies;
      return response.data.data.companies;
    } catch (error) {
      console.error('Error fetching companies: ', error);
      throw error;
    }
  };

  const getCompanyProfile = async(id: string) => {
    const url = `enterprise/companies/${id}`;
    try {
      const response = await nioAxios.get(url);
      companyProfile.value = response.data.data;
      return response.data.data;
    } catch (error) {
      console.error('Error fetching company profile:', error);
      throw error;
    }
  };

  const getCandidateProfile = async(candidateId: string) => {
    try {
      const response = await nioAxios.get<{ data: Candidate }>(`/enterprise/candidates/${candidateId}`);
      candidateProfile.value = response.data.data as Candidate;
      return response.data.data;
    } catch (error) {
      console.error('Error fetching candidate data:', error);
    }
  };

  const resetQuestionsStore = () => {
    companies.value.length = 0;
    tenderId.value = undefined;
  };

  return {
    getVendors,
    tenderId,
    resetQuestionsStore,
    getCompanyProfile,
    getCandidateProfile,
  };
});
