<template>
  <section>
    <h4 class="text-nio-grey-500 -tracking-[0.32px] font-medium leading-[20px] mb-4">
      Source files
    </h4>
    <div class="overflow-x-auto custom-scrollbar-01 max-w-full pb-4">
      <div class="flex items-center gap-2 max-w-full">
        <div
          v-for="file in props.files"
          :key="file.url"
          class="w-[10.7rem] min-w-[10.7rem] max-w-[10.7rem] rounded-10 bg-nio-grey-100 hover:bg-nio-grey-hover-100 h-10 content-center text-center px-3 cursor-pointer hover:"
        >
          <a
            :href="file.url"
            target="_blank"
            class="line-clamp-1 break-all text-sm -tracking-028px font-medium text-nio-grey-700 select-none"
          >{{ file.name }}</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface Props {
  files: Array<{
    name: string;
    url: string;
  }>;
}

const props = defineProps<Props>();
</script>
