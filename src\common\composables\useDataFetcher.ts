import { ref, watch, onBeforeUnmount } from 'vue';
import { nioAxios } from '@/axios';
import type { FilterComponent } from '@/common/types/filter';
import type { Meta } from '@/common/types/data-fetcher';

export function useDataFetcher<T>(
  endpoint: string,
  initialFilters: Record<string, any> = {},
  options: {
    defaultPage?: number;
    lazyLoad?: boolean;
  } = {}
) {
  const data = ref<T[]>([]);
  const isLoading = ref(false);
  const error = ref<Error | null>(null);
  const filterComponents = ref<FilterComponent[]>([]);

  const filters = ref({ ...initialFilters });
  const page = ref(options.defaultPage || 1);
  const meta = ref<Meta | null>(null);
  const lazyLoad = ref(!!options.lazyLoad);

  const isLoadingFirstTime = ref(true);
  const firstLoadHadData = ref(false);

  const dataController = ref<AbortController | null>(null);
  const newDataController = () => {
    dataController.value?.abort();
    dataController.value = new AbortController();
    return dataController.value;
  };

  async function fetchData() {
    const ctrl = newDataController();
    isLoading.value = true;
    error.value = null;

    try {
      // transform to correct format for the api
      const filterParams: Record<string, any> = {};
      for (const [key, val] of Object.entries(filters.value)) {
        if (val !== undefined && val !== null && val !== '') {
          filterParams[`filter[${key}]`] = val;
        }
      }

      const params = {
        ...filterParams,
        page: page.value,
      };

      const response = await nioAxios.get(endpoint, {
        params,
        signal: ctrl.signal,
      });

      if (lazyLoad.value && page.value > 1) {
        data.value = [...data.value, ...response.data.data];
      } else {
        data.value = response.data.data;
      }
      if (data.value.length > 0 && isLoadingFirstTime.value) {
        firstLoadHadData.value = true;
      }
      meta.value = response.data.meta;
    } catch (e: any) {
      if (e?.code !== 'ERR_CANCELED') {
        error.value = e as Error;
      }
    } finally {
      if (dataController.value === ctrl) {
        isLoading.value = false;
      }
      isLoadingFirstTime.value = false;
    }
  }

  async function fetchFilterComponents() {
    try {
      const response = await nioAxios.get(`${endpoint}/filters`);
      filterComponents.value = response.data.data;

      // Initialize filter values to prevent shared array references
      filterComponents.value.forEach(filterComponent => {
        const payloadKey = filterComponent.component.payload_key;
        if (filters.value[payloadKey] === undefined) {
          // Initialize with a new empty array for multi-select filters
          if (filterComponent.component.id === 'multi-select') {
            filters.value[payloadKey] = [];
          } else {
            // Initialize with appropriate default values for other filter types
            filters.value[payloadKey] = null;
          }
        }
      });
    } catch (err) {
      error.value = err as Error;
    }
  };

  // initial fetch
  fetchData();
  fetchFilterComponents();

  watch(filters, () => {
    page.value = 1;
    fetchData();
  }, { deep: true });

  watch(page, () => {
    fetchData();
  });
  onBeforeUnmount(() => {
    dataController.value?.abort();
  });

  return {
    data,
    meta,
    isLoading,
    firstLoadHadData,
    isLoadingFirstTime,
    error,
    filters,
    page,
    filterComponents,
    lazyLoad,
    refresh: fetchData,
  };
}
