<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from 'vue';
import { useRoute, type RouteLocationNormalizedGeneric } from 'vue-router';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import { CompaniesFilter, MatchingStatus } from '@/modules/tenders/types/tenders-enums';
import MatchingWidgetTopCard from './MatchingWidgetTopCard.vue';
import CompaniesMatchingTableWidget from '../CompaniesMatchingTableWidget.vue';
import MatchingWidgetSkeleton from './MatchingWidgetSkeleton.vue';
import { routeMap } from '../../routes';
import { ArrowRight } from 'lucide-vue-next';
import MatchingLoadingText from '../MatchingLoadingText.vue';

interface Props {
  matchingType: CompaniesFilter,
  allowInvite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  allowInvite: true,
});
const emit = defineEmits(['pollingFinished']);

const tenderStore = useTenderStore();
const route = useRoute();

const loading = ref(true);
const matchingResponse = ref<any>(null);
const moreButtonRoute = props.matchingType === CompaniesFilter.Selection ? { name: routeMap.detail.children.tenderMatching.name, params: { id: route.params.id } } : { name: routeMap.detail.children.tenderMatchingMarketplace.name, params: { id: route.params.id } };

const topCompanies = computed(() => matchingResponse.value?.topVendors ?? matchingResponse.value?.companies ?? []);
const otherCompanies = computed(() => matchingResponse.value?.otherVendors ?? []);
const dataReady = computed(() => topCompanies.value.length > 0 || otherCompanies.value.length > 0);
const matchId = computed(() => {
  return props.matchingType === CompaniesFilter.Marketplace
    ? tenderStore.marketplaceMatchingResult?.id ?? ''
    : tenderStore.specificMatchingResult?.id ?? '';
});

const matchingStatus = computed(() => tenderStore.matchingStatus[props.matchingType]);

const fetchMatching = async() => {
  loading.value = true;
  const id = route.params.id as string;
  const filter = props.matchingType === CompaniesFilter.Marketplace ? CompaniesFilter.Marketplace : CompaniesFilter.Selection;
  const matching = await tenderStore.startMatchingPoll(id, 5000, filter);
  matchingResponse.value = { topVendors: Object.values(matching?.topVendors ?? {}), otherVendors: matching?.otherVendors?.slice(0, 6) ?? [] };
  loading.value = false;
  emit('pollingFinished', matchingResponse.value);
};

onBeforeMount(async() => {
  await fetchMatching();
});

watch(() => [route.params.id], async() => {
  tenderStore.resetMatchingResults();
  matchingResponse.value = null;
  loading.value = true;
  await fetchMatching();
});
</script>

<template>
  <div v-if="matchingStatus === MatchingStatus.InProgress" class="m-auto">
    <MatchingLoadingText />
  </div>
  <template v-else>
    <div class="flex flex-col w-full transition-all duration-500 ease-[var(--ease-out-3)] mb-4">
      <div v-if="matchingStatus === MatchingStatus.Failed || matchingStatus === MatchingStatus.NoVendorsMatched">
        <MatchingWidgetSkeleton :error="true" :matching-status="matchingStatus" />
      </div>
      <div v-else-if="!loading && dataReady && matchingResponse" class="grid gap-3 w-full grid-cols-2">
        <MatchingWidgetTopCard
          v-for="(company, index) in topCompanies"
          :key="company?.id"
          :company="company"
          :tender-id="route.params.id as string"
          :matching-type="matchingType"
          :allow-invite="allowInvite"
          :index="index"
        />
      </div>
      <MatchingWidgetSkeleton v-else />

      <div v-if="otherCompanies?.length > 0">
        <CompaniesMatchingTableWidget
          :companies="otherCompanies.slice(0, 4)"
          :starting-index="topCompanies.length + 1"
          :size="5"
          :tender-public-id="route.params.id as string"
          :match-id="matchId"
          :show-more-route="moreButtonRoute as unknown as RouteLocationNormalizedGeneric"
          type="top"
          :loading="loading"
          :allow-invite="allowInvite"
        />
      </div>
    </div>

    <div v-if="!loading && matchingType !== CompaniesFilter.Marketplace && matchingStatus && ![MatchingStatus.NoVendorsMatched, MatchingStatus.Failed].includes(matchingStatus)" class="flex justify-start mt-auto">
      <router-link :to="moreButtonRoute">
        <button
          class="ps-4 pe-3 py-2 bg-nio-blue-800 text-nio-white text-[14px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer flex items-center gap-1 justify-center"
        >
          <span>More</span><ArrowRight class="w-4 h-4" />
        </button>
      </router-link>
    </div>
  </template>
</template>
