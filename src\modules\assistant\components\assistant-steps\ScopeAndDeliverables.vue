<script lang="ts" setup>
import {
  type BlurPromise,
  type DynamicFormStructure,
  type FormErrors,
  type FormStandardData,
  FormTypeMap, transformValidationErrorsForStandardForm
} from '@/common/utils/forms.ts';
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';
import { computed, ref } from 'vue';
import { nioAxios } from '@/axios.ts';
import type { AssistantGuideSteps } from '@/modules/assistant/types/assistant-steps.ts';
import AssistantFormButtons from '@/modules/assistant/components/AssistantFormButtons.vue';
import FormStandardRoot from '@/common/components/forms/FormStandardRoot.vue';
import { routeMap } from '@/modules/assistant/routes';
import { useRouter } from 'vue-router';
import { sleep } from '@/common/utils/sleep.ts';
import { saveForm } from '@/modules/assistant/util/assistant-form';
import SourceFilesList from '@/modules/assistant/components/SourceFilesList.vue';

interface Props {
  generalRfpData: AssistantRfpGeneral,
  currentGuideStep: AssistantGuideSteps,
}

const props = defineProps<Props>();
const emit = defineEmits(['nextStep', 'previousStep']);
const router = useRouter();

const isError = ref(false);
const dynamicFormStruct = ref<DynamicFormStructure>();
const formErrors = ref<FormErrors>([]);
const deliverablesForm = ref<InstanceType<typeof FormStandardRoot>>();
const isFormDisabled = computed(() => Boolean(props.generalRfpData.tender_id));

if (!isFormDisabled.value) {
  nioAxios.patch(`/enterprise/assistant/rfp/${props.generalRfpData.id}/step`, {
    step: 'deliverables',
  });
}

const fetchStepData = async() => {
  try {
    const response = await nioAxios.get<DynamicFormStructure>(`/enterprise/assistant/rfp/${props.generalRfpData.id}/deliverables`);
    dynamicFormStruct.value = response.data;
  } catch {
    isError.value = true;
  } finally {
    await sleep(3000);
  }
};

const onFormSubmit = async() => {
  if (isFormDisabled.value) {
    return;
  }
  formErrors.value = [];
  await saveForm({
    rfpId: props.generalRfpData.id,
    formPayload: { deliverables: deliverablesForm.value!.getFormData() },
    onValidationError422: e => {
      formErrors.value = transformValidationErrorsForStandardForm(e, 'deliverables');
    },
    retryWithoutValidation: true,
  });
  if (formErrors.value.length === 0) {
    emit('nextStep');
  }
};

const handleSubmitForm = () => {
  deliverablesForm.value?.submit();
};

const onNioBlur = async(blurPromise?: BlurPromise) => {
  formErrors.value = [];
  await saveForm({
    showToast: false,
    blurPromise,
    rfpId: props.generalRfpData.id,
    formPayload: { deliverables: deliverablesForm.value!.getFormData() },
    onValidationError422: e => {
      formErrors.value = transformValidationErrorsForStandardForm(e, 'deliverables');
    },
    retryWithoutValidation: true,
  });
};

const saveForLater = async() => {
  await saveForm({
    showToast: false,
    shouldValidate: false,
    rfpId: props.generalRfpData.id,
    formPayload: { deliverables: deliverablesForm.value!.getFormData() },
  });
  await router.push({ name: routeMap.assistant.name });
};

await fetchStepData();
</script>

<template>
  <div class="w-full">
    <div v-if="!isError && dynamicFormStruct" class="w-full">
      <div class="py-1 px-2 rounded-5 bg-nio-grey-100 backdrop-blur-sm font-medium text-sm -tracking-028px text-nio-blue-800 w-fit mb-8">
        {{ $t('assistant.request-proposal') }}
      </div>
      <header class="flex justify-between items-start mb-11">
        <div>
          <h2 class="text-h3 text-nio-black">
            {{ $t('assistant.s-and-d') }}
          </h2>
          <h3 class="text-nio-grey-500 text-h5 font-medium -tracking-[0.4px] leading-[22px]">
            Set expectations – outline key milestones and outcomes.
          </h3>
        </div>
        <div class="text-h1 font-medium leading-[38px] inline-flex gap-1">
          <span class="text-nio-black-900">3</span>
          <span class="text-nio-grey-500">/</span>
          <span class="text-nio-grey-500">3</span>
        </div>
      </header>
      <SourceFilesList :files="generalRfpData.files" />
      <component
        :is="FormTypeMap[dynamicFormStruct.form]"
        ref="deliverablesForm"
        :errors="formErrors"
        :disabled="isFormDisabled"
        :form-structure="dynamicFormStruct as DynamicFormStructure<FormStandardData>"
        @nio-blur="onNioBlur"
        @form-submit="onFormSubmit"
      >
        <template #form-bottom>
          <AssistantFormButtons
            :tender-id="generalRfpData.tender_id"
            :current-guide-step="currentGuideStep"
            @previous-step="emit('previousStep')"
            @save-for-later="saveForLater"
            @submit-form="handleSubmitForm"
          />
        </template>
      </component>
    </div>
  </div>
</template>
