<script setup lang="ts">
import ChipsList from '@/modules/companies/components/companyDetail/ChipsList.vue';

const props = withDefaults(defineProps<{
  title: string;
  data?: string | string[];
  variant?: 'black' | 'white' | 'transparent';
  align?: 'top' | 'bottom';
  loading?: boolean;
}>(), {
  variant: 'white',
  loading: false,
  data: '',
  align: 'bottom',
});

const variant = props.variant;
const HEIGHT = 300;

</script>

<template>
  <div
    :class="[
      ` relative w-full flex flex-col  min-h-[${HEIGHT}px] overflow-y-auto`,
      variant === 'white' ? 'bg-nio-white text-nio-black-900' : '',
      variant === 'black' ? 'bg-nio-black-900 text-nio-white' : '',
      variant !== 'transparent' ? 'p-[30px] rounded-30' : 'p-[15px]',
      Array.isArray(data) ? 'justify-start' : 'justify-end'
    ]"
  >
    <div
      class="z-1 flex mb-2"
    >
      <div
        class="self-start px-[12px] py-[6px] text-[18px] font-normal rounded-[5px] leading-[18px]"
        :class="{
          ' border rounded-full border-nio-grey-200 text-nio-grey-700': variant === 'white' || variant === 'transparent',
          'bg-nio-grey-background-15 text-nio-grey-200': variant === 'black'
        }"
      >
        {{ title }}
      </div>
    </div>

    <Suspense>
      <template #default>
        <Transition
          appear
          enter-active-class="transition-opacity duration-150 ease-in"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="transition-opacity duration-150 ease-out"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <div v-if="!loading" :class="{ 'mt-auto': align === 'bottom', 'mb-auto': align === 'top' }">
            <div
              v-if="Array.isArray(data)"
            >
              <ChipsList :chips="data" />
            </div>
            <h1
              v-else
              class="text-[44px] font-normal leading-normal"
              :class="{ 'text-nio-blue-800': variant === 'white' || variant === 'transparent', 'text-nio-white': variant === 'black' }"
            >
              <slot name="data">
                {{ data }}
              </slot>
            </h1>
          </div>
        </Transition>
      </template>
      <template #fallback>
        <!-- empty for now -->
      </template>
    </Suspense>
  </div>
</template>
