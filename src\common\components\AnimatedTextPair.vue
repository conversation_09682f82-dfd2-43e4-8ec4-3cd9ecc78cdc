<template>
  <div ref="textDiv">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { transformText } from '@/common/utils/text';

interface Props {
  speedMultiplier?: number,
}

const props = withDefaults(defineProps<Props>(), {
  speedMultiplier: 1,
});

const textDiv = ref<HTMLDivElement>();

onMounted(() => {
  transformText(textDiv.value!, props.speedMultiplier);
  const handleAnimationEnd = () => {
    setTimeout(() => {
      textDiv.value!.textContent = 'New text after animation';
    }, 2000);
    textDiv.value?.removeEventListener('animationend', handleAnimationEnd);
  };
  textDiv.value?.addEventListener('animationend', handleAnimationEnd);
});

watch(() => props.speedMultiplier, () => {
  transformText(textDiv.value!, props.speedMultiplier);
});
</script>
