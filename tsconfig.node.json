{"extends": "@tsconfig/node22/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*"], "compilerOptions": {"composite": true, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "target": "ESNext", "module": "ESNext", "lib": ["ES2022", "ESNext.Promise"], "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"]}}