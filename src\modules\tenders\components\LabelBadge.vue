<template>
  <span :class="['rounded-5 whitespace-nowrap', badgeClass, sizeClasses[size]]">
    <slot />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = withDefaults(defineProps<{
  type?: 'success' | 'warning' | 'danger' | 'default'
  size?: 'sm' | 'md' | 'lg'
}>(), {
  type: 'default',
  size: 'md',
});

const badgeClass = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-green-100 text-green-800';
    case 'warning':
      return 'bg-yellow-100 text-yellow-800';
    case 'danger':
      return 'bg-red-100 text-red-800';
    case 'default':
      return 'bg-nio-grey-100 text-nio-grey-800';
    default:
      return '';
  }
});
const sizeClasses = {
  sm: 'px-2 py-0.5 text-[12px]',
  md: 'px-2.5 py-1 text-[14px]',
  lg: 'px-3.5 py-1.5 text-[16px]',
};
</script>