<script lang="ts" setup>
import ArrowIcon from '@/assets/icons/arrow-icon-2.svg';
import { computed } from 'vue';
import { useAuthStore } from '@/modules/auth/stores/auth-store.ts';

const authStore = useAuthStore();
const userProfile = computed(() => authStore.userProfile);

const fullName = computed(() => {
  const name = userProfile.value?.name || '';
  const surname = userProfile.value?.surname || '';
  return `${name} ${surname}`.trim();
});

const logout = () => {
  authStore.logout();
};
</script>

<template>
  <div
    class="absolute bottom-0 left-9 bg-nio-white rounded-15 shadow-lg border border-nio-blue-outline-stroke-400 p-3"
  >
    <div>
      <h3 class="text-p-x font-normal leading-normal text-nio-grey-700 whitespace-nowrap">
        {{ fullName }}
      </h3>
      <p class="text-p-m font-normal leading-paragraph text-nio-grey-500">
        {{ userProfile?.email }}
      </p>
    </div>

    <div class="mt-2">
      <button
        class="relative group p-1 text-p-l font-normal leading-paragraph text-nio-black-900 cursor-pointer w-full flex items-center justify-center rounded-5 hover:rounded-[5px] bg-nio-grey-100 hover:bg-nio-grey-200 transition-all duration-200"
        @click="logout"
      >
        <span class="text-center text-p-l font-normal leading-normal tracking-[0.56px]">
          {{ $t('header.logout') }}
        </span>

        <ArrowIcon
          class="absolute -translate-y-1/2 top-1/2 right-2.5 fill-current w-[9px] h-[9px] opacity-0 group-hover:opacity-100 transition-opacity duration-100 text-left text-p-l font-normal leading-normal tracking-[0.56px]"
        />
      </button>
    </div>
  </div>
</template>
