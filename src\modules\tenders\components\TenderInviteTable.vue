<script setup lang="ts">
import { reactive, computed } from 'vue';
import { CircleX } from 'lucide-vue-next';
import { tenderScoreToPoints, tenderScoreToText } from '@/common/utils/score';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import { CompanyCategory } from '@/modules/auth/types/auth-types';

const props = defineProps<{
  vendors: Array<{
    id: string;
    name: string;
    country: string;
    city: string;
    match: number;
    employee_emails?: string[];
    sent_at?: string | null;
    category: CompanyCategory;
  }>;
  startingIndex: number;
  actionLabel?: string;
  loading?: boolean;
  placeholderText?: string;
}>();

const emit = defineEmits<{
  (e: 'remove', id: string): void;
}>();

const remove = (id: string) => emit('remove', id);

const expanded = reactive<Record<string, boolean>>({});
const toggleExpand = (id: string) => {
  expanded[id] = !expanded[id];
};

const vendorsList = computed(() => props.vendors ?? []);
const startingIndex = computed(() => props.startingIndex);
const actionLabel = computed(() => props.actionLabel ?? 'Remove');
const loading = computed(() => props.loading);
const placeholderText = computed(() => props.placeholderText ?? 'No data available.');

const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) {return '';}
  const normalized = dateString.length === 10 ? `${dateString}T00:00:00` : dateString;
  const date = new Date(normalized);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'long',
    year: '2-digit',
  });
};
</script>

<template>
  <template v-if="loading">
    <div class="flex flex-col gap-2">
      <div v-for="i in 5" :key="i" class="flex w-full items-center justify-betweenrounded-15 animate-pulse">
        <div class="w-full h-[70px] bg-nio-grey-100 rounded-15 " />
      </div>
    </div>
  </template>

  <template v-else>
    <template v-if="vendorsList.length === 0">
      <div class="text-center text-nio-grey-400 py-8">
        {{ placeholderText }}
      </div>
    </template>

    <template v-else>
      <div
        v-for="(vendor, index) in vendorsList"
        :key="vendor.id"
        class="flex flex-col sm:flex-row sm:items-center justify-between rounded-15 p-3 hover:bg-[#f1f6ff] gap-3"
        role="listitem"
      >
        <div class="flex items-start sm:items-start w-full sm:w-auto min-w-0">
          <span class="text-[12px] font-paragraph text-nio-grey-300 leading-normal mr-2 pt-1">
            {{ startingIndex + index }}
          </span>
          <div class="min-w-0">
            <div class="flex items-center gap-1 mb-1">
              <div
                v-if="[CompanyCategory.AGENCY, CompanyCategory.ENTERPRISE].includes(vendor.category)"
                class="text-xs w-fit px-1.5 py-0.5 rounded-sm flex-shrink-0"
                :class="{
                  'bg-nio-green-text/20 text-green-700': vendor.category === CompanyCategory.AGENCY,
                  'bg-nio-blue-500/15 text-nio-blue-800': vendor.category === CompanyCategory.ENTERPRISE
                }"
              >
                {{ vendor.category === CompanyCategory.ENTERPRISE ? 'E' : 'A' }}
              </div>
              <p
                class="font-paragraph text-[18px] leading-normal text-nio-grey-900 truncate"
                :title="vendor.name"
              >
                {{ vendor.name }}
              </p>
            </div>
            <template v-if="actionLabel !== 'Invite'">
              <p class="text-[13px] text-nio-grey-400 leading-normal break-words">
                <template v-if="!vendor.employee_emails || vendor.employee_emails.length === 0">
                  No employee registered.
                </template>
                <template v-else-if="vendor.employee_emails.length > 1">
                  <span v-if="!expanded[vendor.id]">
                    <p>
                      Will be sent to {{ vendor.employee_emails.length }} employees.
                      <a
                        href="#"
                        class="text-nio-blue-800 cursor-pointer hover:underline"
                        @click.prevent="() => toggleExpand(vendor.id)"
                      >
                        Show emails
                      </a>
                    </p>
                  </span>
                  <span v-else class="flex flex-col gap-1 mt-1 break-all">
                    {{ vendor.employee_emails.join(', ') }}
                  </span>
                </template>
                <template v-else>
                  {{ vendor.employee_emails.join(', ') }}
                </template>
              </p>
            </template>
            <template v-else>
              <p class="font-paragraph text-sm leading-normal text-nio-grey-500">
                {{ vendor.country }}, {{ vendor.city }}
              </p>
            </template>
          </div>
        </div>

        <div class="flex items-center gap-4 sm:ml-auto">
          <template v-if="vendor.sent_at">
            <div class="flex items-center justify-center rounded-[5px] text-[12px] text-nio-green-text bg-nio-green-bg px-2 py-1">
              {{ formatDate(vendor.sent_at) }}
            </div>
          </template>
          <template v-else>
            <template v-if="vendor.match > 0">
              <NioScoreProgress
                :points="tenderScoreToPoints(vendor.match, 'total')"
                size="sm"
                :title="tenderScoreToText(vendor.match)"
              />
            </template>

            <template v-if="actionLabel === 'Invite'">
              <button
                class="px-3 py-1.5  bg-nio-green-text hover:bg-nio-green-text/80 text-nio-white text-[12px] rounded-full  transition-colors cursor-pointer"
                tabindex="-1"
                @click="() => remove(vendor.id)"
              >
                {{ actionLabel }}
              </button>
            </template>
            <template v-else-if="actionLabel === 'Remove'">
              <button
                class="cursor-pointer hover:text-red-600 transition-colors text-nio-grey-500"
                tabindex="-1"
                @click="() => remove(vendor.id)"
              >
                <CircleX stroke-width="1.25" />
              </button>
            </template>
          </template>
        </div>
      </div>
    </template>
  </template>
</template>
