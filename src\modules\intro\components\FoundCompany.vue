<script setup lang="ts">
import PlusIcon from '@/assets/icons/plus-icon-respo.svg';

interface Props {
  title: string;
  imgSrc?: string;
  region: string;
}

defineProps<Props>();
</script>

<template>
  <div class="group flex relative" role="option" aria-label="Found vendor">
    <div
      class="flex gap-[10px] items-center bg-nio-grey-background-90 custom-bs cursor-pointer rounded-15 py-[10px] pl-[10px] pr-[15px] max-w-[185px] w-fit border border-transparent group-hover:border group-hover:border-nio-blue-outline-stroke-400 group-hover:bg-nio-blue-800"
    >
      <img class="block w-10 h-10 object-cover" src="@/assets/tmp/company-tmp.png" alt="Vendor logo">
      <div class="grid w-full">
        <h4 class="text-p-xl text-nio-grey-900 line-clamp-1 max-w-full break-all group-hover:text-white -mb-1">
          {{ title }}
        </h4>
        <p class="text-nio-grey-400 text-p-x line-clamp-1 max-w-full break-all group-hover:text-nio-grey-200">
          {{ region }}
        </p>
      </div>
    </div>
    <div class="hidden group-hover:block w-5 h-0" />
    <div class="cursor-pointer hidden group-hover:flex justify-center items-center absolute w-5 h-5 rounded-full group-hover:bg-nio-blue-800 group-hover:border group-hover:border-nio-blue-outline-stroke-400 top-0 -translate-y-1/2 right-0">
      <PlusIcon class="w-full h-full text-white" />
    </div>
  </div>
</template>

<style scoped lang="css">
.custom-bs {
  box-shadow: 0 169px 47px 0 rgba(0, 0, 0, 0.00), 0 108px 43px 0 rgba(0, 0, 0, 0.00), 0 61px 36px 0 rgba(0, 0, 0, 0.01), 0 27px 27px 0 rgba(0, 0, 0, 0.02), 0 7px 15px 0 rgba(0, 0, 0, 0.02);
}
</style>
