<template>
  <div
    class="flex flex-col items-center justify-top"
    :class="{
      'h-[40px]': size === 'sm',
      'h-[57px]': size === 'md',
      'h-[74px]': size === 'lg',
      'h-[91px]': size === 'xl',
    }"
  >
    <div
      class="relative"
      :class="{
        'size-16': size === 'sm',
        'size-24': size === 'md',
        'size-32': size === 'lg',
        'size-40': size === 'xl',
      }"
    >
      <svg class="size-full rotate-180 " viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
        <circle
          cx="18"
          cy="18"
          r="16"
          fill="none"
          :class="points >= 1 ? 'stroke-nio-green-text' : (size === 'xl' ? 'stroke-nio-grey-100' : 'stroke-nio-grey-200')"
          :stroke-width="strokeWidth"
          stroke-dashoffset="0"
          stroke-dasharray="13 100"
          stroke-linecap="round"
        />

        <circle
          cx="18"
          cy="18"
          r="16"
          fill="none"
          :class="points >= 2 ? 'stroke-nio-green-text' : (size === 'xl' ? 'stroke-nio-grey-100' : 'stroke-nio-grey-200')"
          :stroke-width="strokeWidth"
          stroke-dashoffset="103"
          stroke-dasharray="19 100"
          stroke-linecap="round"
        />

        <circle
          cx="18"
          cy="18"
          r="16"
          fill="none"
          :class="points >= 3 ? 'stroke-nio-green-text' : (size === 'xl' ? 'stroke-nio-grey-100' : 'stroke-nio-grey-200')"
          :stroke-width="strokeWidth"
          stroke-dashoffset="75"
          stroke-dasharray="13 100"
          stroke-linecap="round"
        />

      </svg>

      <div
        class="absolute start-1/2 transform -translate-x-1/2 text-center"
        :class="{
          'top-4.5': size === 'sm',
          'top-8': size === 'md',
          'top-11': size === 'lg',
          'top-9': size === 'xl',
        }"
      >
        <div
          class=" font-semibold text-nio-green-text"
          :class="{
            'text-[12px] leading-[16px] mb-1': size === 'sm',
            'text-[16px] leading-[16px] mb-1': size === 'md',
            'text-[22px] leading-[16px] mb-1': size === 'lg',
            'text-2xl mb-1': (size === 'xl' && !title) || (size === 'xl' && title && title.length <= 10),
            'text-[20px] leading-[16px] mb-3': (size === 'xl' && !title) || (size === 'xl' && title && title.length >= 8 && title.length <= 10),
            'text-1xl leading-[16px] mb-2': size === 'xl' && title && title.length > 10,
          }"
        >
          {{ title }}
        </div>
        <div class="text-sm block whitespace-nowrap">
          {{ subtitle }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  points: number | 0;
  title?: string;
  subtitle?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}>();
const size = computed(() => props.size ?? 'md');

const strokeWidth = computed(() => {
  switch (size.value) {
    case 'sm':
      return 2;
    case 'md':
      return 2;
    case 'lg':
      return 1.5;
    case 'xl':
      return 1.5;
    default:
      return 2;
  }
});

</script>
