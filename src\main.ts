import '@/assets/css/main.css';
import '@/assets/css/custom-anims.css';
import '@/assets/css/vue-anims.css';
import '@/assets/css/general.css';

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import i18n from './plugins/i18n';
import PrimeVue from 'primevue/config';
import router from './router';
import * as Sentry from '@sentry/vue';
import { getAccessToken, startLoginFlow } from '@/modules/auth/facades/token';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import App from './App.vue';
import { routeMap as introRouteMap } from '@/modules/intro/routes';
import { createHead } from '@unhead/vue/client';
import { routeMap as tendersRouteMap } from '@/modules/tenders/routes';
import { toast } from '@/common/utils/NotificationService';
import Vue3LottieLib from 'vue3-lottie';
import { resetWorkspaces } from '@/modules/auth/facades/reset.ts';
import VueGtag from 'vue-gtag';
import VueAwesomePaginate from 'vue-awesome-paginate';
import Clarity from '@microsoft/clarity';
import { Settings } from 'luxon';

Settings.defaultLocale = 'en-US';
window.eventBus = new EventTarget();

const checkTokenAndInitializeApp = async() => {
  const token = getAccessToken();

  if (!token) {
    return;
  }

  const authStore = useAuthStore();
  try {
    return await authStore.fetchUserProfile();
  } catch {
    //
  }
};

const initGtag = (app: any) => {
  const gtagId = import.meta.env.VITE_GTAG_ID;
  if (!gtagId) {
    return;
  }

  app.use(VueGtag, {
    config: { id: gtagId },
    appName: 'Enterprise MVP',
    pageTrackerScreenviewEnabled: true,
  });
};

const createAndMountApp = async() => {
  const head = createHead();
  const app = createApp(App);

  if (import.meta.env.VITE_SENTRY_DSN) {
    Sentry.init({
      app,
      dsn: import.meta.env.VITE_SENTRY_DSN,
      tracePropagationTargets: [/^https:\/\/api\.mvp\.nordics\.io(\/.*)?$/],
      tracesSampleRate: 1.0,
    });
  }
  initGtag(app);

  app.use(createPinia());
  app.use(i18n);
  app.use(head);
  app.use(VueAwesomePaginate);
  app.use(PrimeVue, {
    unstyled: true,
    // theme: {
    //   preset: 'none',
    //   options: {
    //     cssLayer: {
    //       name: 'primevue',
    //       order: 'base, primevue'
    //     }
    //   }
    // },
  });
  app.use(Vue3LottieLib, { name: 'LottieAnimation' });
  app.config.globalProperties.t = i18n.global.t;
  app.config.globalProperties.toast = toast;
  app.provide('toast', toast);

  const currentPath = window.location.pathname;
  if (currentPath === '/authorize') {
    // Mount the app immediately for `/authorize`
    app.use(router);
    app.mount('#app');
    return;
  }

  if (import.meta.env.VITE_CLARITY_ID) {
    Clarity.init(import.meta.env.VITE_CLARITY_ID);
  }

  const userData = await checkTokenAndInitializeApp();
  if (!userData) {
    await startLoginFlow();
    return;
  }
  if (!userData?.workspaces?.length) {
    await router.replace({ name: introRouteMap.intro.name });
  }
  const resetQuery = (new URL(window.location.href).searchParams).get('reset');
  if (resetQuery) {
    await resetWorkspaces();
  }
  if (currentPath === '/') {
    await router.replace({ name: tendersRouteMap.tenders.name });
  }
  app.use(router);
  app.mount('#app');
};

createAndMountApp()
  .catch(error => console.error('Failed to initialize Vue app:', error));
