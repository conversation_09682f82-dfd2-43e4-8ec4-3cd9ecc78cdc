import { type Locator, type Page } from '@playwright/test';

export abstract class BasePage {
  readonly page: Page;
  readonly mainMenuToggleButton: Locator;
  readonly navigation: Locator;

  constructor(page: Page) {
    this.page = page;
    this.mainMenuToggleButton = page.getByRole('button', { name: 'Main Menu' });
    this.navigation = page.getByRole('navigation', { name: 'Main menu' });
  }

  getFormCellByLabel(label: string): Locator {
    return this.page.locator('div').filter({ hasText: new RegExp('^' + label) });
  }

  async fillTextarea(editButton: Locator, value) {
    await editButton.click();
    await this.page.getByRole('dialog').getByRole('textbox').fill(value);
    await this.page.getByRole('button', { name: 'Save', exact: true }).click();
  }

  async selectSingleOption(selectButton: Locator, option: string) {
    await selectButton.click();
    await this.page.getByRole('listitem').filter({ hasText: option }).click();
  }

  async deselectAll(selectButton: Locator, selectOptions: Locator, { clickSelectButton = true } = {}) {
    if (clickSelectButton) {
      await selectButton.click();
    }
    const deleteButtons = selectOptions.getByRole('button', { name: 'Delete' });
    const selectedCount = await deleteButtons.count();
    for (let i = 0; i < selectedCount; i++) {
      // Keep unchecking first checked item.
      await deleteButtons.nth(0).click();
    }
    if (clickSelectButton) {
      await selectButton.click();
    }
  }

  async selectMultiOption(selectButton: Locator, selectOptions: Locator, optionsToSelect: string[], { useCheckbox = false, deselectAllFirst = true } = {}) {
    await selectButton.click();
    if (deselectAllFirst) {
      await this.deselectAll(selectButton, selectOptions, { clickSelectButton: false });
    }
    for (let i = 0; i < optionsToSelect.length; i++) {
      const option = optionsToSelect[i];
      if (useCheckbox) {
        await selectOptions.getByRole('checkbox', { name: option }).check();
      } else {
        await selectOptions.filter({ hasText: option }).click();
      }
    }
    await selectButton.click();
  }

  async increaseNumber(numberInput: Locator, count: number, resetToZeroFirst: boolean = true) {
    if (resetToZeroFirst) {
      // TODO(Marian Rusnak): Replace with clear() once bug is fixed.
      await numberInput.fill('0');
    }
    for (let i = 0; i < count; i++) {
      await numberInput.locator('+ div').click();
    }
  }

  async moveSlider(sliderTrack: Locator, sliderHandle: Locator, percentage: number) {
    const sliderTrackBox = await sliderTrack.boundingBox();
    const sliderThumbBox = await sliderHandle.boundingBox();

    await sliderHandle.hover({ force: true, position: { x: sliderThumbBox.width / 2 - 5, y: sliderThumbBox.height / 2 } });
    await this.page.mouse.down();
    await this.page.mouse.move(sliderTrackBox.x + sliderTrackBox.width * percentage, sliderTrackBox.y);
    await this.page.mouse.up();
  }

  async moveSliderRange(sliderTrack: Locator, sliderLowerHandle: Locator, sliderUpperHandle: Locator, [percentageMin, percentageMax]: number[]) {
    await this.moveSlider(sliderTrack, sliderLowerHandle, percentageMin);
    await this.moveSlider(sliderTrack, sliderUpperHandle, percentageMax);
  }
}
