<script setup lang="ts">
import { AlertTriangle } from 'lucide-vue-next';
import { MatchingStatus } from '@/modules/tenders/types/tenders-enums';

const props = defineProps<{ error?: boolean; matchingStatus?: MatchingStatus }>();
</script>

<template>
  <div
    v-if="props.error || props.matchingStatus === MatchingStatus.Failed || props.matchingStatus === MatchingStatus.NoVendorsMatched"
    class="flex items-center justify-center w-full min-h-[200px]"
  >
    <div class="text-[20px] font-normal text-nio-black-900 leading-[28px] tracking-[0.52px] mb-2 text-center">
      <template v-if="props.matchingStatus === MatchingStatus.NoVendorsMatched">
        <span class="text-[16px]">
          No vendors matched your current request.
        </span>
        <p class="text-xs">
          This may be due to narrowly defined criteria, limited vendor availability in your network, or a low match
          score across key capabilities. Matching is based on verified data — if no options appear, it means none
          currently meet the requirements.
        </p>
      </template>
      <template v-else>
        <span class="text-[16px] flex items-center gap-2"> <AlertTriangle
          class="w-4 h-4 text-nio-red-500 inline-block"
        /> Matching failed.</span>
      </template>
    </div>
  </div>
  <div v-else class="flex flex-col justify-between ">
    <div class="grid grid-cols-2 w-full gap-4 animate-pulse">
      <div v-for="i in 4" :key="i" class="h-[80px] bg-nio-grey-100 rounded-20" />
    </div>
    <div class="grid grid-cols-2 gap-4 animate-pulse animate-delay-400 mt-4">
      <div class="flex flex-col gap-3">
        <div v-for="i in 2" :key="i" class="w-full h-[60px] bg-nio-grey-100 rounded-10" />
      </div>
      <div class="flex flex-col gap-3">
        <div v-for="i in 2" :key="i" class="w-full h-[60px] bg-nio-grey-100 rounded-10" />
      </div>
    </div>
    <div class="w-20 h-[34px] bg-nio-grey-100 rounded-full mt-4 animate-pulse" />
  </div>
</template>

<style lang="css" scoped>

</style>
