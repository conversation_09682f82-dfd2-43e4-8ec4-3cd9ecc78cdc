import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { DynamicFormStructure } from '@/common/utils/forms.ts';

type RfpFormCacheEntry = {
  info?: DynamicFormStructure;
  resources?: DynamicFormStructure;
  timeout: ReturnType<typeof setTimeout>;
};

export const useRfpFormCacheStore = defineStore('rfp-form-cache', () => {
  const cache = ref<Record<string, RfpFormCacheEntry>>({});
  const CACHE_DURATION = 5000; // 5 seconds

  function insert(data: DynamicFormStructure, rfpId: string, step: keyof Omit<RfpFormCacheEntry, 'timeout'>) {
    if (cache.value[rfpId]) {
      clearTimeout(cache.value[rfpId].timeout);
    }

    cache.value[rfpId] = {
      timeout: setTimeout(() => {
        delete cache.value[rfpId];
      }, CACHE_DURATION),
      [step]: data,
    };
  }

  function get(rfpId: string, step: keyof Omit<RfpFormCacheEntry, 'timeout'>): DynamicFormStructure | undefined {
    return cache.value[rfpId]?.[step];
  }

  return { insert, get };
});
