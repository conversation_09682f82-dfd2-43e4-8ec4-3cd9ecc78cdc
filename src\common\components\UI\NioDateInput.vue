<script setup lang="ts">
import type { DynamicFormItemData, FormErrors } from '@/common/utils/forms.ts';
import { watch } from 'vue';

const model = defineModel<{ start_date: string, end_date: string }>({ required: true });
const emit = defineEmits(['nio-blur']);
withDefaults(defineProps<{
  error?: FormErrors[0],
  errorPosition?: 'left' | 'right',
  inputData: DynamicFormItemData,
  inputId?: string,
  disabled?: boolean
}>(), {
  error: undefined,
  errorPosition: 'right',
  inputId: undefined,
  disabled: false,
});

watch([() => model.value.start_date, () => model.value.end_date], () => {
  emit('nio-blur');
});
</script>

<template>
  <div class="@container">
    <div class="flex relative gap-3 flex-col @[21.5rem]:flex-row">
      <div
        :class="[
          'w-fit relative inline-flex items-center px-4 py-2 rounded-full border focus-within:border-nio-blue-400 hover:bg-nio-white hover:text-nio-black-900',
          error?.subKeys?.start_date ? 'border-nio-red-background' : 'border-gray-300',
          model.start_date ? 'bg-nio-white text-nio-black-900' : 'bg-nio-grey-background text-gray-500',
          disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : ''
        ]"
      >
        <input
          :id="inputId"
          v-model="model.start_date"
          type="date"
          class="appearance-none bg-transparent focus:outline-none focus:ring-0 focus:border-nio-blue-400 w-fit @[21.5rem]:w-[132px]"
          :class="[
            disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : 'cursor-pointer'
          ]"
          :disabled="disabled"
          :aria-describedby="`${inputData.component.payload_key}-start-error`"
        >
        <div
          v-if="error?.subKeys?.start_date"
          :id="`${inputData.component.payload_key}-start-error`"
          class="absolute -bottom-5 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
          :class="errorPosition === 'right' ? 'right-0' : 'left-0'"
        >
          {{ error?.subKeys?.start_date }}
        </div>
      </div>
      <div
        :class="[
          'w-fit relative inline-flex items-center px-4 py-2 rounded-full border focus-within:border-nio-blue-400 hover:bg-nio-white hover:text-nio-black-900',
          error?.subKeys?.end_date ? 'border-nio-red-background' : 'border-gray-300',
          model.end_date ? 'bg-nio-white text-nio-black-900' : 'bg-nio-grey-background text-gray-500',
          disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : ''
        ]"
      >
        <input
          v-model="model.end_date"
          type="date"
          class="appearance-none bg-transparent focus:outline-none focus:ring-0 focus:border-nio-blue-400 w-fit @[21.5rem]:w-[132px]"
          :class="[
            disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : 'cursor-pointer'
          ]"
          :disabled="disabled"
          :aria-describedby="`${inputData.component.payload_key}-end-error`"
        >
        <div
          v-if="error?.subKeys?.end_date"
          :id="`${inputData.component.payload_key}-end-error`"
          class="absolute -bottom-5 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
          :class="errorPosition === 'right' ? 'right-0' : 'left-0'"
        >
          {{ error?.subKeys?.end_date }}
        </div>
      </div>
    </div>
  </div>
</template>
