<script setup lang="ts">
import { computed, watch } from 'vue';
import CompanyCard from '@/modules/companies/components/cards/CompanyCard.vue';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import NioTable from '@/common/components/UI/table/NioTable.vue';
import PageCard from '@/common/components/PageCard.vue';
import CompanyTableRow from '@/modules/companies/components/cards/CompanyTableRow.vue';
import NioTablePagination from '@/common/components/UI/table/NioTablePagination.vue';
import type { Company } from '@/modules/companies/types/company';

const authStore = useAuthStore();

const props = defineProps<{
  listView?: boolean;
  meta: any;
}>();

const page = defineModel<number>('page', { required: true });
const companies = defineModel<Company[]>('companies', { required: true });

const columns = computed(() => {
  return [
    { key: 'name', label: 'Vendor', width: 2 },
    { key: 'location', label: 'Location', width: 1 },
    { key: 'category', label: 'Category', width: 1 },
    { key: 'status', label: 'Status', width: 1 },
    { key: 'actions', label: '', width: 0.5, align: 'right' as const },
  ];
});

const onDeleteCompany = async(companyId: any) => {
  try {
    await authStore.removeCompany(companyId);
    companies.value = companies.value.filter(company => company.id !== companyId);
  } catch (error) {
    console.error(error);
  }
};

watch(() => props.listView, async() => {
  page.value = 1;
});
</script>

<template>
  <Transition
    appear
    enter-active-class="transition-opacity duration-150 ease-in"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition-opacity duration-0 ease-in"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-show="!listView">
      <div class="grid gap-x-3 gap-y-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-5">
        <CompanyCard
          v-for="(company) in companies"
          :key="company.id"
          :company="company"
          @delete-company="onDeleteCompany"
        />
        <div v-show="companies.length === 0" class="rounded-3xl bg-nio-grey-background py-4 pl-4 pr-1 col-span-full text-center">
          <div>
            No vendors found
          </div>
        </div>
      </div>
      <div v-if="meta && page < meta.last_page" class="w-full flex justify-center text-center mt-3">
        <button
          data-testid="load-more-button"
          class="px-5 py-2 rounded-full text-sm transition-all duration-200 flex items-center gap-1.5 cursor-pointer bg-nio-grey-900 text-white transform hover:opacity-90 active:scale-95"
          @click="page = page + 1"
        >
          Load more
        </button>
      </div>
    </div>
  </Transition>
  <PageCard v-show="listView">
    <NioTable data-testid="company-table" :data="companies" :columns="columns">
      <CompanyTableRow
        v-for="(company) in companies"
        :key="company.id"
        :company="company"
        :columns="columns"
        @delete-company="onDeleteCompany"
      />
    </NioTable>
    <div class="text-nio-grey-700 text-sm py-5 text-center w-full">
      <div v-if="companies.length === 0" class="rounded-3xl bg-nio-grey-background py-4 pl-4 pr-1 col-span-full text-center">
        <div>
          No vendors found
        </div>
      </div>
      <NioTablePagination
        v-else-if="meta"
        v-model:page="page"
        class="mt-5"
        :meta="meta"
      />
    </div>
  </PageCard>
</template>
