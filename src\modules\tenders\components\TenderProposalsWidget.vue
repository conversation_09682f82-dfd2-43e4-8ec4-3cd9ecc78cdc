<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3">
    <div v-for="company in props.invitedCompanies" :key="company.id" class="bg-[#F2F2F7] p-3 rounded-10">
      <div class="flex items-center gap-2">
        <div>
          <span class="block">
            {{ company.name }}
          </span>
          <span class="text-nio-grey-500 text-[12px] leading-normal block">
            {{ company.headquarters }}, {{ company.country }}
          </span>
        </div>
      </div>
      <div class="grid grid-cols-2 gap-2 mt-3 border-t border-nio-grey-100 pt-3">
        <div class="font-medium text-[14px] leading-normal">
          <span class="text-nio-grey-700"> Invitation sent: </span>
          <LabelBadge v-if="company.sent_at" type="success" size="sm">
            {{ formatDateSK(company.sent_at) }}
          </LabelBadge>
          <LabelBadge v-else type="default" size="sm">
            Not sent
          </LabelBadge>
        </div>

        <div class="font-medium text-[14px] leading-normal">
          <span class="text-nio-grey-700"> Tender viewed: </span>
          <LabelBadge v-if="company.first_viewed_at" type="success" size="sm">
            {{ formatDateSK(company.first_viewed_at) }}
          </LabelBadge>
          <LabelBadge v-else-if="company.sent_at" type="warning" size="sm">
            Pending
          </LabelBadge>
          <LabelBadge v-else type="default" size="sm">
            Not sent
          </LabelBadge>
        </div>
        <div class="font-medium text-[14px] leading-normal">
          <span class="text-nio-grey-700"> Candidates:</span> {{ company.candidates_count }} <UserIcon class="w-3.5 h-3.5 inline-block" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { formatDateSK } from '@/common/utils/date';
import type { CompanyWithProposalStats } from '@/modules/tenders/types/tenders-types';
import LabelBadge from './LabelBadge.vue';

const props = defineProps<{
  invitedCompanies: CompanyWithProposalStats[];
}>();
</script>
