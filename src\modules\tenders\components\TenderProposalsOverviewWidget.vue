<template>
  <div>
    <div class="grid grid-cols-3 lg:grid-cols-2 gap-2 mb-4 h-[120px]">
      <div class="flex flex-col justify-end h-full">
        <div class="font-bold text-h5 mt-1">
          {{ totalInvited }}
        </div>
        <div class="text-nio-grey-900 capitalize">
          Invitations sent
        </div>
      </div>
      <div class="flex flex-col justify-end h-full">
        <div class="font-bold text-h5 mt-1">
          {{ totalViewed }}
        </div>
        <div class="text-nio-grey-900 capitalize">
          Tender views
        </div>
      </div>
      <div class="flex flex-col justify-end h-full">
        <div class="font-bold text-h5 mt-1">
          {{ totalCandidates }}
        </div>
        <div class="text-nio-grey-900 capitalize">
          Candidates
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import type { CompanyWithProposalStats } from '../types/tenders-types';

const tenderStore = useTenderStore();
const invitedCompanies = computed(() => tenderStore.invitedVendors as CompanyWithProposalStats[] ?? []);

const totalInvited = computed(() =>
  invitedCompanies.value.filter(c => !!c.sent_at).length
);
const totalViewed = computed(() =>
  invitedCompanies.value.filter(c => !!c.first_viewed_at).length
);
const totalCandidates = computed(() =>
  invitedCompanies.value.reduce((sum, c) => sum + (c.candidates_count ?? 0), 0)
);
</script>