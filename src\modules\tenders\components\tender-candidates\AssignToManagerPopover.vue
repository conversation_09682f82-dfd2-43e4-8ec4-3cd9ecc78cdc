<script setup lang="ts">
import { nioAxios } from '@/axios';
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';
import NioDropdown from '@/common/components/UI/NioDropdown.vue';
import type { DynamicFormItemData } from '@/common/utils/forms';
import { toast } from '@/common/utils/NotificationService';
import type { Assignment, CandidateRow, ConsultantTableType } from '@/modules/consultants/types/consultants-types';
import { reactive, ref, watch } from 'vue';

interface Props {
  candidate?: CandidateRow<ConsultantTableType.ENGAGED>;
}

const props = defineProps<Props>();
const emit = defineEmits(['updated-candidate']);

const submitting = ref(false);

const inputData = reactive<DynamicFormItemData>({
  name: 'assign-to-manager',
  component: {
    id: 'select',
    payload_key: 'assign-to-manager',
  }
});

const isPopoverOpen = defineModel<boolean>();
const selectedValue = ref<number | undefined>(props.candidate?.assignment?.manager?.id);

try {
  const optionsResponse = await nioAxios.get<{data: Assignment['manager'][]}>('/enterprise/company/users');
  inputData.component.options = optionsResponse.data?.data?.map(manager => ({
    label: `${manager!.name} ${manager!.surname}`,
    description: `<span class="font-medium">${manager?.position}</span><br>${[manager?.division, manager?.department].filter(Boolean).join(', ')}`,
    value: manager!.id,
  }));
} catch {
  toast.show('An error occurred', 'Failed to load data.', 'error');
}

const onSubmit = async() => {
  if (submitting.value) {
    return;
  }
  submitting.value = true;
  try {
    const response = await nioAxios.put<{data: CandidateRow<ConsultantTableType.ENGAGED>}>(`/enterprise/candidates/${props.candidate?.id}/assignment`, {
      manager_id: selectedValue.value ? selectedValue.value : null,
    });
    toast.show('Manager assigned', 'Successfully assigned manager to candidate.', 'success');
    emit('updated-candidate', response?.data?.data);
    isPopoverOpen.value = false;
  } catch (e) {
    console.error(e);
    toast.show('An error occurred', 'Failed to assign manager.', 'error');
  } finally {
    submitting.value = false;
  }
};

watch(() => props.candidate?.id, () => {
  selectedValue.value = props.candidate?.assignment?.manager?.id;
});

</script>

<template>
  <DetailPopover01
    v-model="isPopoverOpen"
    width="1100px"
    min-height="auto"
    height="fit-content"
    title="Assign to manager"
  >
    <form class="h-full px-4 py-2 overflow-y-visible" @submit.prevent="onSubmit">
      <div class="flex items-center gap-3">
        <label for="assign-to-manager-input" class="block text-sm font-medium text-nio-grey-700 ml-0.5">
          Assigned manager
        </label>
        <NioDropdown
          v-model="selectedValue"
          :input-data="inputData"
          input-id="assign-to-manager-input"
          class="w-60"
        />
      </div>
      <hr class="mt-4">
      <button
        type="submit"
        :disabled="false"
        class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px]  disabled:opacity-50 disabled:cursor-not-allowed mt-4"
      >
        Submit
      </button>
    </form>
  </DetailPopover01>
</template>

<style lang="css" scoped>

</style>
