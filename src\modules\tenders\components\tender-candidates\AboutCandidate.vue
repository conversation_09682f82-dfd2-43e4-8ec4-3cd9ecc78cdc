<script setup lang="ts">
import type { Candidate } from '@/modules/tenders/types/tenders-types';
import { Briefcase, Building2, ExternalLink, MapPin, User } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import NioTag from '@/common/components/NioTag.vue';
import { useWorkspaceRate } from '@/common/composables/useHourlyRate';

const { candidate } = defineProps<{ candidate: Candidate }>();
const { getValidRate } = useWorkspaceRate();

const experienceFields = computed(() => [
  { label: 'Rate', value: getValidRate(candidate.rate) },
  ...(candidate.profession ? [{ label: 'Profession', value: candidate.profession }] : []),
  ...(candidate.seniority ? [{ label: 'Seniority', value: candidate.seniority }] : []),
  ...(candidate.lastJobTitle ? [{ label: 'Last Job Title', value: candidate.lastJobTitle }] : []),
  ...(candidate.yearsOfExperience ? [{ label: 'Years of Experience', value: candidate.yearsOfExperience }] : []),
  ...(candidate.highestEducation ? [{ label: 'Highest Education', value: candidate.highestEducation }] : []),
  ...(candidate.fieldOfStudy ? [{ label: 'Field of Study', value: candidate.fieldOfStudy }] : [])
]);

const experiences = (candidate.experiences || []).map(exp => ({ ...exp, collapsed: ref(false) }));
const skills = computed(() => (candidate.skills || []).map(skill => ({ ...skill, collapsed: ref(false) })));
const skillsCollapsed = ref(true);
const visibleSkills = computed(() => (skillsCollapsed.value ? skills.value.slice(0, SKILLS_MAX_LENGTH) : skills.value));

const DESCRIPTION_MAX_LENGTH = 250;
const SKILLS_MAX_LENGTH = 10;
</script>

<template>
  <div class="mt-3 px-2">
    <!-- Personal -->
    <div class="flex flex-col text-center items-center justify-center pb-5">
      <span class="inline-flex items-center justify-center bg-blue-500 shadow-md w-12 h-12 rounded-full mb-2">
        <User class="w-6 h-6 text-white" />
      </span>
      <div>
        <div class="text-[22px] text-center font-paragraph text-grey-900 leading-normal">
          {{ candidate.name }}
        </div>
        <div class="text-sm text-nio-grey-700 flex items-center justify-center">
          <span v-if="candidate.city" class="flex items-center"><MapPin class="w-3 h-3 mr-1 " />  <span class="border-r pr-2 mr-2">{{ candidate.city }}, {{ candidate.country }}</span></span>
          <span v-if="candidate.vendor" class="flex items-center"><Building2 class="w-3 h-3 mr-1 " />  {{ candidate.vendor.name }}</span>
          <span v-if="candidate.cv?.url" class="border-l pl-2 ml-2">
            <a
              :href="candidate.cv.url"
              target="_blank"
              rel="noopener noreferrer"
              class="flex items-center cursor-pointer text-nio-blue-500 hover:underline"
            >
              <ExternalLink class="w-3 h-3 mr-1" />
              View CV
            </a>
          </span>
        </div>
      </div>
    </div>

    <hr class=" border-nio-grey-100">

    <!-- Professional Info -->
    <div class="grid grid-cols-2 gap-4 pt-5 pb-5">
      <div>
        <div class="text-[20px] font-paragraph text-grey-900 leading-normal mb-3">
          Professional Info
        </div>

        <div class="grid grid-cols-2 gap-2 ">
          <div
            v-for="field in experienceFields"
            :key="field.label"
            class="text-[14px] py-1.5 px-3 rounded-10 hover:bg-nio-blue-200"
          >
            <span class="text-nio-grey-500 block">{{ field.label }}</span>
            <span class="capitalize block  font-semibold text-nio-black">{{ field.value ?? '-' }}</span>
          </div>
        </div>
      </div>

      <!-- Skills -->
      <div>
        <div class="text-[20px] font-paragraph text-grey-900 leading-normal mb-3">
          Skills
        </div>

        <div v-if="skills.length > 0" class="flex flex-wrap gap-2">
          <template v-for="skill in visibleSkills" :key="skill.technology">
            <NioTag
              class="relative overflow-hidden"
              size="sm"
            >
              {{ skill.technology }}
              <template v-if="skill.years_of_experience">
                <span class="opacity-0">{{ skill.years_of_experience }}y</span>
                <span class="text-white bg-nio-grey-700 text-xs flex items-center justify-center absolute right-0 top-0 h-full px-[5px] rounded-none -mr-[2px]">{{ skill.years_of_experience }}y</span>
              </template>
            </NioTag>
          </template>

          <div v-if="skills.length > SKILLS_MAX_LENGTH" class="w-full">
            <button class="text-blue-500 hover:underline hover:cursor-pointer text-[12px]" @click="skillsCollapsed = !skillsCollapsed">
              {{ skillsCollapsed ? 'Show more' : 'Show less' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <hr class=" border-nio-grey-100">

    <!-- Experience -->
    <div v-if="candidate.experiences && candidate.experiences.length > 0 " class="pt-5 ">
      <div class="text-[20px] font-paragraph text-black leading-normal mb-3">
        Experience
      </div>
      <div v-for="experience in experiences" :key="experience.id" class="flex items-top experience-item relative pb-5">
        <span class="inline-block w-8 min-w-8 mr-2 z-1">
          <span class="inline-flex mr-2 items-center justify-center bg-blue-500  w-8 h-8 rounded-full ">
            <Briefcase class="w-4 h-4 text-white" />
          </span>
        </span>

        <div class="grow">
          <div class="flex items-center w-full">
            <span class="text-md font-semibold mb-1 text-nio-black"> {{ experience.name }}</span>

            <span class="text-nio-grey-500  text-xs ml-auto"> {{ experience.length }}</span>
          </div>
          <div class="text-nio-grey-500 text-sm">
            <template v-if="experience.description.length > DESCRIPTION_MAX_LENGTH">
              <span v-if="experience.description.length > DESCRIPTION_MAX_LENGTH" v-show="!experience.collapsed.value">{{ experience.description.slice(0, DESCRIPTION_MAX_LENGTH) }}...</span>
              <span v-if="experience.description.length > DESCRIPTION_MAX_LENGTH" v-show="experience.collapsed.value">{{ experience.description }}</span>
              <div v-if="experience.description.length > DESCRIPTION_MAX_LENGTH">
                <button class="text-blue-500 hover:underline hover:cursor-pointer" @click="experience.collapsed.value = !experience.collapsed.value">
                  {{ experience.collapsed.value ? 'Show Less' : 'Show More' }}
                </button>
              </div>
            </template>
            <template v-else>
              {{ experience.description }}
            </template>
          </div>
        </div>
      </div>
      <div class="flex items-top relative">
        <span class="inline-block w-8 min-w-8 -mt-[10px] z-1">
          <span class="inline-flex  items-center justify-center border-1 bg-nio-grey-100 border-nio-grey-100   ml-[10.5px] w-3 h-3 rounded-full " />
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.experience-item:before{
  content: '';
  position: absolute;
  top: 0;
  left: 15.5px;
  width: 1.5px;
  height: 100%;
  background-color: #e8e8e8;
}
</style>
