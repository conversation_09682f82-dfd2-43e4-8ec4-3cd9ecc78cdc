:root {
    --nio-grey-background: #F9F9F9;
    --nio-grey-background-15: #F9F9F926;
    --nio-grey-background-30: #F9F9F94D;
    --nio-grey-background-60: #F9F9F999;
    --nio-grey-background-90: #F9F9F9E5;
    --nio-red-background: #F2D2D3;
    --nio-black-900: #131313;
    --nio-white: #FFFFFF;
    --nio-black: #000000;
    --nio-blue-100: #F2F6FE;
    --nio-blue-200: #E5ECFE;
    --nio-blue-300: #D9E3FD;
    --nio-blue-400: #BBD0FB;
    --nio-blue-500: #5598FF;
    --nio-blue-600-hover: #348DE9;
    --nio-blue-800: #0071E3;
    --nio-blue-outline-stroke-400: #BBD0FB;
    --nio-green-bg: #C8F3D6;
    --nio-green-text: #5DA773;
    --nio-grey-200: #D0D0D0;
    --nio-grey-300: #B9B9B9;
    --nio-grey-400: #A1A1A1;
    --nio-grey-500: #8A8A8A;
    --nio-grey-700: #535353;
    --nio-grey-900: #292929;
    --nio-red-500: #BC2020;
}

.cta-button-border-gradient-rounded {
    transition: all 200ms ease;
    border: 1.2px solid transparent;
    border-radius: 30px;
    background:
            linear-gradient(to right, var(--nio-blue-800), var(--nio-blue-800)),
            linear-gradient(89deg, var(--nio-blue-800) 1.27%, var(--nio-blue-outline-stroke-400) 98.73%);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    font-weight: 400;
    &:hover {
        background:
                linear-gradient(to right, var(--nio-blue-600-hover), var(--nio-blue-600-hover)),
                linear-gradient(89deg, var(--nio-blue-800) 1.27%, var(--nio-blue-outline-stroke-400) 98.73%);
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
    }
}

.most-used-shadow {
    box-shadow: 0px 149px 42px 0px rgba(222, 222, 222, 0.00), 0px 95px 38px 0px rgba(222, 222, 222, 0.01), 0px 54px 32px 0px rgba(222, 222, 222, 0.05), 0px 24px 24px 0px rgba(222, 222, 222, 0.09), 0px 6px 13px 0px rgba(222, 222, 222, 0.10);
}


.custom-scrollbar-01 {
    --sb-track-color: transparent;
    --sb-thumb-color: #d0d0d0;
    --sb-size: 6px;
}

.custom-scrollbar-01::-webkit-scrollbar {
    width: var(--sb-size)
}

.custom-scrollbar-01::-webkit-scrollbar-track {
    background: var(--sb-track-color);
    border-radius: 3px;
}

.custom-scrollbar-01::-webkit-scrollbar-thumb {
    background: var(--sb-thumb-color);
    border-radius: 3px;

}

@supports not selector(::-webkit-scrollbar) {
    .custom-scrollbar-01 {
        scrollbar-color: var(--sb-thumb-color)
        var(--sb-track-color);
    }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse 1.5s infinite ease-in-out;
}