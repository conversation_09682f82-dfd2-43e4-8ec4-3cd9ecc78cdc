<template>
  <button
    id="f-cta-anim"
    type="button"
    class="background cta-button-border-gradient-rounded cursor-pointer"
    :class="[{ 'is-invisible': !isVisible }, { 'is-loading': isLoading }]"
    :disabled="isLoading"
  >
    <div class="content-no-anim" v-bind="$attrs">
      <slot />
    </div>
  </button>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

interface Props {
  bottomCss: string;
  isLoading?: boolean;
}

const { bottomCss, isLoading = false } = defineProps<Props>();

const isVisible = ref(false);
setTimeout(() => {
  isVisible.value = true;
}, 500);

let lastScrollTop = 0;
const threshold = 200; // px
let lastVisibleScrollPosition = 0;

const handleScroll = () => {
  const scrollTop = window.scrollY;

  if (scrollTop > lastScrollTop) {
    isVisible.value = true;
    lastVisibleScrollPosition = scrollTop;
  } else if (lastVisibleScrollPosition - scrollTop > threshold) {
    isVisible.value = false;
  }

  lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped lang="css">
#f-cta-anim.background {
  --bottom: v-bind(bottomCss);
  position: fixed;
  bottom: var(--bottom, 4rem);
  translate: -50% 0;
  scale: 1;
  opacity: 1;
  left: 50%;
  transition: translate 1150ms linear(0.00, 0.00792, 0.0624, 0.153, 0.270, 0.403, 0.544, 0.684, 0.817, 0.937, 1.04, 1.13, 1.19, 1.24, 1.27, 1.28, 1.27, 1.25, 1.23, 1.19, 1.16, 1.12, 1.08, 1.04, 1.01, 0.984, 0.961, 0.944, 0.933, 0.926, 0.924, 0.926, 0.932, 0.939, 0.949, 0.959, 0.970, 0.980, 0.990, 0.998, 1.01, 1.01, 1.02, 1.02, 1.02, 1.02, 1.02, 1.02, 1.02, 1.01, 1.01, 1.01, 1.00, 1.00, 1.00, 0.998, 0.997, 0.995, 0.995, 0.994, 0.994, 0.995, 0.995, 0.996, 0.996, 0.997, 0.998, 0.999, 0.999, 1.00), scale 250ms ease, opacity 250ms ease;

  .content {
    translate: 0 0;
    scale: 1;
    opacity: 1;
    transition: translate 1250ms linear(0.00, 0.00792, 0.0624, 0.153, 0.270, 0.403, 0.544, 0.684, 0.817, 0.937, 1.04, 1.13, 1.19, 1.24, 1.27, 1.28, 1.27, 1.25, 1.23, 1.19, 1.16, 1.12, 1.08, 1.04, 1.01, 0.984, 0.961, 0.944, 0.933, 0.926, 0.924, 0.926, 0.932, 0.939, 0.949, 0.959, 0.970, 0.980, 0.990, 0.998, 1.01, 1.01, 1.02, 1.02, 1.02, 1.02, 1.02, 1.02, 1.02, 1.01, 1.01, 1.01, 1.00, 1.00, 1.00, 0.998, 0.997, 0.995, 0.995, 0.994, 0.994, 0.995, 0.995, 0.996, 0.996, 0.997, 0.998, 0.999, 0.999, 1.00) 70ms, scale 350ms ease 200ms, opacity 250ms ease 200ms;
  }

  &.is-invisible {
    translate: -50% 220px;
    scale: 0.5;

    .content {
      translate: 0 220px;
      scale: 0.4;
      opacity: 0;
    }
  }

  &.is-loading {
    background: linear-gradient(to right, var(--nio-blue-800), var(--nio-blue-800)),
    linear-gradient(89deg, var(--nio-blue-800) 1.27%, var(--nio-blue-outline-stroke-400) 98.73%);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
  }

}
</style>
