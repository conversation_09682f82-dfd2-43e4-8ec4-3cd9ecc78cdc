<template>
  <div class="relative group w-full h-full">
    <transition
      mode="out-in"
      name="slide-up-04"
      :duration="400"
      appear
    >
      <div
        class="absolute left-[30px] top-[30px] w-[40px] h-[40px] rounded-full bg-[#F3F4F4] flex items-center justify-center"
      >
        <PenIcon />
      </div>
    </transition>

    <transition
      mode="out-in"
      name="slide-up-04"
      :duration="400"
      appear
    >
      <div
        class="absolute right-[30px] top-[30px] w-[40px] h-[40px] rounded-full bg-[#0071E3] flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      >
        <PlusIcon class="text-white" />
      </div>
    </transition>

    <transition
      mode="out-in"
      name="slide-up-04"
      :duration="400"
      appear
    >
      <div class="absolute left-[30px] bottom-[40px]">
        <h3 class="text-[24px] text-[#0071E3]">
          {{ $t('companies.upload-companies-title') }}
        </h3>
        <p class="block  text-[#A1A1A1] text-[20px]">
          {{ $t('companies.upload-companies-description') }}
        </p>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import PenIcon from '@/assets/icons/pen-icon.svg';
import PlusIcon from '@/assets/icons/plus-icon.svg';
</script>

<style scoped>
.group:hover {
  cursor: pointer;
}
</style>
