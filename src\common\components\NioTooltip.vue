<!-- Tooltip.vue -->
<template>
  <div class="relative flex items-center justify-center" @mouseenter="show = true" @mouseleave="show = false">
    <slot />
    <div v-show="show" class="absolute bg-nio-grey-900 text-nio-grey-100 text-xs rounded py-1 px-2 bottom-full mb-1">
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineProps<{ text: string }>();
const show = ref(false);
</script>