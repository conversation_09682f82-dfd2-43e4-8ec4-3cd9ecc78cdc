import { nioAxios } from '@/axios.ts';

export const resetWorkspaces = async() => {
  try {
    await nioAxios.delete('/enterprise/reset');
  } catch {
    //
  } finally {
    const url = new URL(window.location.href);
    const params = url.searchParams;
    params.delete('reset'); // Replace 'paramName' with the parameter you want to remove
    window.history.replaceState({}, '', url.toString());
    window.location.reload();
  }
};
