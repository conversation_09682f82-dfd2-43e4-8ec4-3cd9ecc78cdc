import { useAuthStore } from '@/modules/auth/stores/auth-store';
import { computed } from 'vue';

export enum RateType {
  HOURLY = 'hourly',
  MAN_DAY = 'man-day',
}

export function useWorkspaceRate() {
  const authStore = useAuthStore();
  const rateType = computed<RateType>(() => authStore.userProfile?.workspaces?.[0]?.rate_type ?? RateType.HOURLY);
  const rateSuffix = computed(() => (rateType.value === RateType.MAN_DAY ? '/day' : '/hr'));

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(amount);
  };

  const getValidRate = (hourlyRate: number | undefined, formatToText = true) => {
    if (typeof hourlyRate !== 'number') {
      return formatToText ? '–' : undefined;
    }
    if (rateType.value === RateType.MAN_DAY) {
      return formatToText ? `${formatCurrency(hourlyRate * 8)}/day` : hourlyRate * 8;
    }
    return formatToText ? `${formatCurrency(hourlyRate)}/hr` : hourlyRate;
  };

  const getValidRateRange = (hourlyRates: number[], formatToText = true) => {
    if (!hourlyRates.length) {
      return formatToText ? '–' : undefined;
    }
    if (hourlyRates.length === 1) {
      const rate = getValidRate(hourlyRates[0], false);
      return formatToText && typeof rate === 'number' ? `${formatCurrency(rate)}${rateSuffix.value}` : rate;
    }
    const sortedRates = hourlyRates.toSorted((a, b) => a - b);
    if (rateType.value === RateType.MAN_DAY) {
      return formatToText ? `${formatCurrency(sortedRates[0] * 8)} – ${formatCurrency(sortedRates.at(-1)! * 8)}${rateSuffix.value}` : sortedRates.map(rate => rate * 8);
    }
    return formatToText ? `${formatCurrency(sortedRates[0])} – ${formatCurrency(sortedRates.at(-1)! * 1)}${rateSuffix.value}` : sortedRates;
  };

  return { getValidRate, getValidRateRange, rateType };
}
