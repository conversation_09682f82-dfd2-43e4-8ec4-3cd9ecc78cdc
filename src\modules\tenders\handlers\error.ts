import { toast } from '@/common/utils/NotificationService';
import router from '@/router';

export function handleTenderError(err: any) {
  const status = err?.response?.status ?? err?.status;

  if (!err?.response) {
    toast.show('Network error', 'Please check your connection and try again.', 'error');
    return;
  }

  if (status === 404 || status === 410) {
    toast.show('Tender not found', 'This tender doesn’t exist.', 'error');
    if (router.currentRoute.value.path !== '/tenders') {
      router.replace('/tenders');
    }
    return;
  }

  toast.show('Error', 'Something went wrong.', 'error');
};
