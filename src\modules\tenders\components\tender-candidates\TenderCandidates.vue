<script setup lang="ts">
import { nioAxios } from '@/axios';
import CandidatesTableEntry from '@/modules/tenders/components/tender-candidates/CandidatesTableEntry.vue';
import { ref, onMounted } from 'vue';
import type { TenderCandidate } from '@/modules/tenders/types/tenders-types';
import NioTable from '@/common/components/UI/table/NioTable.vue';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';
import type { NioTableColumn } from '@/common/types/components';

const props = defineProps<{
  tenderId: string;
}>();

const loading = ref(true);
// TODO: Add type for candidates
const candidates = ref<TenderCandidate[]>([]);

onMounted(async() => {
  loading.value = true;
  try {
    const response = await nioAxios.get(`/enterprise/tenders/${props.tenderId}/candidates`);
    candidates.value = response.data?.data ?? [];
  } catch {
    candidates.value = [];
  } finally {
    loading.value = false;
  }
});

const candidatesTableColumns: NioTableColumn[] = [
  { key: 'name', label: 'Name & Vendor', width: 1, align: 'left' as const },
  { key: 'position', label: 'Position', width: 1, align: 'left' as const },
  { key: 'seniority', label: 'Seniority', width: 1, align: 'left' as const },
  { key: 'rate', label: 'Rate', width: 1, align: 'left' as const },
  { key: 'match', label: 'Match', width: 1, align: 'right' as const }
];
</script>

<template>
  <div class="w-full grid gap-1 bg-nio-grey-background">
    <div class="">
      <template v-if="loading">
        <NioTable :columns="candidatesTableColumns">
          <NioTableRow v-for="i in 5" :key="i" :columns="candidatesTableColumns">
            <template #name>
              <div class="w-[98px] h-[22px] bg-nio-grey-100 rounded-10 mb-1" />
            </template>
            <template #position>
              <div class="w-[98px] h-[22px] bg-nio-grey-100 rounded-10 mb-1" />
            </template>
            <template #seniority>
              <div class="w-[98px] h-[22px] bg-nio-grey-100 rounded-10 mb-1" />
            </template>
            <template #rate>
              <div class="w-[98px] h-[22px] bg-nio-grey-100 rounded-10 mb-1" />
            </template>
            <template #match>
              <div class="w-[60px] h-[22px] bg-nio-grey-100 rounded-10 ml-auto" />
            </template>
          </NioTableRow>
        </NioTable>
      </template>
      <template v-else-if="candidates.length === 0">
        <div class="flex flex-col items-center justify-center min-h-[300px] w-full">
          <div class="text-[20px] font-normal text-nio-black-900 mb-6 text-center">
            To see candidates, you need to invite at least one vendor and they must accept the tender.
          </div>
          <router-link :to="{ name: 'detail', params: { id: tenderId } }">
            <button class="ps-4 pe-3 py-2 bg-nio-blue-800 text-nio-white text-[14px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer flex items-center gap-1 justify-center">
              Back to tender
            </button>
          </router-link>
        </div>
      </template>
      <template v-else>
        <NioTable :columns="candidatesTableColumns">
          <CandidatesTableEntry
            v-for="(candidate, index) in candidates"
            :key="candidate?.id"
            :candidate="candidate"
            :columns="candidatesTableColumns"
            :index="index"
          />
        </NioTable>
      </template>
    </div>
  </div>
</template>

<style scoped>
.shadow-custom-box-shadow-01 {
  box-shadow: 0 71px 20px 0 rgba(207, 207, 207, 0.00),
  0 45px 18px 0 rgba(207, 207, 207, 0.01),
  0 25px 15px 0 rgba(207, 207, 207, 0.05),
  0 11px 11px 0 rgba(207, 207, 207, 0.09),
  0 3px 6px 0 rgba(207, 207, 207, 0.10);
}
</style>
