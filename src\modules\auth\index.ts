import { useI18n } from 'vue-i18n';
import sk from './locales/sk.json';  // Slovenský preklad modulu
import en from './locales/en.json';

export function registerAuthTranslations() {
  const { locale, messages } = useI18n();

  //Dyna add translation to  global i18n configuration
  messages.value[locale.value] = {
    ...messages.value[locale.value],
    auth: locale.value === 'sk' ? sk.auth : en.auth,
  };
}
