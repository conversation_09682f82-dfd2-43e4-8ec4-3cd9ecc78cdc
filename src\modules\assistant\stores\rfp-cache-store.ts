import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';

type RfpCacheEntry = {
  data: AssistantRfpGeneral;
  timeout: ReturnType<typeof setTimeout>;
};

export const useRfpCacheStore = defineStore('rfp-cache', () => {
  const cache = ref<Record<string, RfpCacheEntry>>({});
  const CACHE_DURATION = 5000; // 5 seconds

  function insert(rfp: AssistantRfpGeneral) {
    if (cache.value[rfp.id]) {
      clearTimeout(cache.value[rfp.id].timeout);
    }

    const timeout = setTimeout(() => {
      delete cache.value[rfp.id];
    }, CACHE_DURATION);

    cache.value[rfp.id] = { data: rfp, timeout };
  }

  function get(id: string): AssistantRfpGeneral | undefined {
    return cache.value[id]?.data;
  }

  return { insert, get };
});
