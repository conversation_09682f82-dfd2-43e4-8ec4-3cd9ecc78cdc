<template>
  <div class="flex flex-col items-center justify-center h-full w-full">
    <LoaderSmall />
    <div class="mt-[20px] text-[18px] font-medium leading-[24px] tracking-[-0.36px] text-nio-black text-center max-w-[80%] h-12">
      <AnimatedTextSlot :key="primaryKey" :speed-multiplier="1.2">
        {{ texts[currentTextIndex][0] }}
        <br>
        {{ texts[currentTextIndex][1] }}
      </AnimatedTextSlot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LoaderSmall from '@/common/components/Loader-small.vue';
import AnimatedTextSlot from '@/common/components/AnimatedTextSlot.vue';

const texts = [
  ['Analyzing technical requirements...'],
  ['Evaluating vendors’ project histories...'],
  ['Identifying overlaps in technologies and skillsets...'],
  ['Checking industry and regional experience patterns...'],
  ['Comparing vendor capabilities across key criteria...'],
];

const currentTextIndex = ref<number>(Math.floor(Math.random() * texts.length));
const primaryKey = ref(0);
const secondaryKey = ref(1);

const startAnimation = () => {
  setTimeout(() => {
    let usedIndexes: number[] = [currentTextIndex.value];
    const nextText = () => {
      let newIndex;
      do {
        newIndex = Math.floor(Math.random() * texts.length);
      } while (usedIndexes.includes(newIndex));

      currentTextIndex.value = newIndex;
      usedIndexes.push(newIndex);

      primaryKey.value++;
      secondaryKey.value++;

      if (usedIndexes.length === texts.length) {
        usedIndexes = [];
      }

      setTimeout(nextText, 5000);
    };

    nextText();
  }, 5000);
};

onMounted(startAnimation);
</script>
