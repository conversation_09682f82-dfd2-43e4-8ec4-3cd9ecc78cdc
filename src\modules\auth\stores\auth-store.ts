import { defineStore } from 'pinia';
import { startLoginFlow, clearTokens } from '@/modules/auth/facades/token';
import { nioAxios } from '@/axios';
import type { ProfileResponse } from '@/modules/auth/types/auth-types';
import { ref } from 'vue';

export const useAuthStore = defineStore('auth-store', () => {
  const userProfile = ref<ProfileResponse>();

  const login = async() => {
    await startLoginFlow();
  };

  const logout = () => {
    nioAxios.post(`${import.meta.env.VITE_AUTH_API_URL}/logout`)
      .catch()
      .finally(() => {
        clearTokens();
        window.location.href = '/';
      });
  };

  const fetchUserProfile = async(): Promise<ProfileResponse> => {
    const profileResponse = await nioAxios.get<{ data: ProfileResponse }>('profile');
    userProfile.value = profileResponse.data.data;
    return profileResponse.data.data;
  };

  const removeCompany = async(companyId: any) => {
    await nioAxios.delete(`/enterprise/workspace/companies/${companyId}`);
    userProfile?.value?.workspaces.forEach(workspace => {
      workspace.companies = workspace.companies.filter(company => company.id !== companyId);
    });
  };

  const getUserName = () => {
    return userProfile.value?.name;
  };

  return {
    login,
    logout,
    fetchUserProfile,
    getUserName,
    userProfile,
    removeCompany,
  };
});
