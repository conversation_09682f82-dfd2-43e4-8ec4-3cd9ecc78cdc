<template>
  <div class="w-full h-[190px] relative">
    <transition
      mode="out-in"
      name="slide-up-04"
      :duration="400"
      appear
    >
      <div
        v-if="!fileDetail"
        class="absolute top-[30px] left-[30px] w-[50px] h-[50px] rounded-full bg-[#F3F4F4]"
      >
        <PlusIcon />
      </div>
      <div
        v-else
        class="absolute flex items-center justify-center left-[30px] top-[30px] w-10 h-10 rounded-full bg-[#F3F4F4]"
      >
        <FileIcon />
      </div>
    </transition>
    <transition
      mode="out-in"
      name="slide-up-04"
      :duration="400"
      appear
    >
      <div
        v-if="!fileDetail"
        class="absolute bottom-[40px] left-[30px] w-full"
      >
        <h3 class="text-h4 text-[#0071E3]">
          {{ $t('intro.upload-companies') }}
        </h3>
        <p class="block text-p-x text-[#A1A1A1]">
          {{ $t('intro.drag-drop-upload') }}
        </p>
      </div>
      <div
        v-else
        class="absolute bottom-[20px] left-[30px] w-full"
      >
        <div class="w-full h-full relative">
          <p class="text-p-l text-[#A1A1A1]">
            {{ fileDetail.name.split('.').pop() }}, {{ (fileDetail.size * 0.001).toFixed(1) }} KB
          </p>
          <h3 class="text-h5 text-[#292929] line-clamp-1 w-full whitespace-nowrap">
            {{ fileDetail.name }}
          </h3>
        </div>
      </div>
    </transition>
    <div
      v-if="fileDetail"
      class="cancel-btn absolute z-10 -translate-y-1/2 translate-x-1/2 top-[-10px] right-[-10px] w-[30px] h-[30px] bg-[rgba(249,249,249,0.60)] border border-[#BBD0FB] rounded-full flex items-center justify-center"
    >
      <Loader />
    </div>
  </div>
</template>

<script setup lang="ts">
import PlusIcon from '@/assets/icons/plus-icon.svg';
import FileIcon from '@/assets/icons/file-icon.svg';
import { IntroSteps } from '@/modules/intro/types/intro-steps';
import Loader from '@/common/components/Loader.vue';

interface Props {
  currentStep?: IntroSteps;
  fileDetail?: File;
}

defineProps<Props>();
// const emit = defineEmits(['removeFile']);
</script>
