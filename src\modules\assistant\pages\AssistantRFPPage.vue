<template>
  <div class="w-full transition-all duration-500 ease-[var(--ease-out-3)] mb-[120px]">
    <AssistantRFPMain>
      <template v-if="currentGuideStep === AssistantGuideSteps.REVIEW && rfpGeneralData && isReviewLoaded" #header>
        <AssistantReviewHeader
          :general-rfp-data="rfpGeneralData"
          @save-to-tenders="onSaveToTenders"
          @save-for-later="onSaveForLater"
        />
      </template>
      <div class="w-full">
        <suspense v-if="currentGuideStep === AssistantGuideSteps.GENERAL">
          <GeneralInformation
            :general-rfp-data="rfpGeneralData!"
            :current-guide-step="currentGuideStep"
            @rfp-title-changed="onRfpTitleChanged"
            @next-step="nextStep"
          />
          <template #fallback>
            <div class="flex justify-center items-center h-40">
              <LoadingText />
            </div>
          </template>
        </suspense>
        <suspense v-if="currentGuideStep === AssistantGuideSteps.RESOURCE_AND_TECH">
          <ResourceAndTechnicalDetails
            :general-rfp-data="rfpGeneralData!"
            :current-guide-step="currentGuideStep"
            @next-step="nextStep"
            @previous-step="previousStep"
          />
          <template #fallback>
            <div class="flex justify-center items-center h-40">
              <LoadingText />
            </div>
          </template>
        </suspense>
        <suspense v-if="currentGuideStep === AssistantGuideSteps.REVIEW" @resolve="isReviewLoaded = true">
          <Review
            ref="reviewRef"
            :general-rfp-data="rfpGeneralData!"
            :current-guide-step="currentGuideStep"
          />
          <template #fallback>
            <div class="flex justify-center items-center h-40">
              <LoadingText />
            </div>
          </template>
        </suspense>
      </div>
    </AssistantRFPMain>
    <Drawer
      v-model:visible="historyVisible"
      position="left"
      :show-close-icon="false"
      :block-scroll="true"
      class="w-[536px] h-full bg-nio-grey-background transition-transform duration-300 ease-in-out"
    >
      <div ref="scrollAreaEl" class="min-h-0 flex-[1_1_0] overflow-y-auto custom-scrollbar-01 h-full">
        <AssistantSideHistory />
      </div>
    </Drawer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { AssistantGuideSteps, AssistantStepMap } from '../types/assistant-steps.ts';
import AssistantRFPMain from '@/modules/assistant/components/AssistantRFPMain.vue';
import GeneralInformation from '@/modules/assistant/components/assistant-steps/GeneralInformation.vue';
import Drawer from 'primevue/drawer';
import ResourceAndTechnicalDetails
  from '@/modules/assistant/components/assistant-steps/ResourceAndTechnicalDetails.vue';
import { nioAxios } from '@/axios.ts';
import { useRoute } from 'vue-router';
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';
import { useRfpCacheStore } from '@/modules/assistant/stores/rfp-cache-store.ts';
import Review from '@/modules/assistant/components/assistant-steps/Review.vue';
import AssistantSideHistory from '@/modules/assistant/components/AssistantSideHistory.vue';
import LoadingText from '@/modules/assistant/components/LoadingText.vue';
import { useHead } from '@unhead/vue';
import AssistantReviewHeader from '@/modules/assistant/components/AssistantReviewHeader.vue';

const historyVisible = ref(false);

const route = useRoute();
const { id: rfpId } = route.params as { id: string };
const rfpCacheStore = useRfpCacheStore();
const rfpGeneralData = ref<AssistantRfpGeneral | undefined>(rfpCacheStore.get(rfpId));
const currentGuideStep = ref(AssistantGuideSteps.GENERAL);
const isError = ref(false);
const reviewRef = ref();
const isReviewLoaded = ref(false);
const title = computed(() => `${rfpGeneralData.value?.title ?? 'New RFP'} | Assistant | Nordics Platform`);

const fetchRfpData = async() => {

  if (rfpGeneralData.value) {
    currentGuideStep.value = AssistantStepMap[rfpGeneralData.value.step] ?? AssistantGuideSteps.GENERAL;
    return;
  }
  try {
    const response = await nioAxios.get<{ data: AssistantRfpGeneral }>(`/enterprise/assistant/rfp/${rfpId}`);
    rfpGeneralData.value = response.data.data;
    currentGuideStep.value = AssistantStepMap[rfpGeneralData.value.step] ?? AssistantGuideSteps.GENERAL;
  } catch {
    isError.value = true;
    console.error('Failed to get data');
  }
};

const nextStep = () => {
  currentGuideStep.value += 1;
};

const previousStep = () => {
  currentGuideStep.value -= 1;
};

const onRfpTitleChanged = (title: string) => {
  if (title) {
    rfpGeneralData.value!.title = title;
  }
};

const onSaveToTenders = (loadingPromise?: PromiseWithResolvers<void>) => {
  reviewRef.value?.saveToTenders(loadingPromise);
};
const onSaveForLater = () => {
  reviewRef.value?.saveForLater();
};

await fetchRfpData();

useHead({
  title: title,
});
</script>

<style lang="css">
.p-drawer {
  width: 33rem;
  background: var(--nio-grey-background);
  box-shadow: 284px 0px 80px 0px rgba(97, 97, 97, 0.00), 182px 0px 73px 0px rgba(97, 97, 97, 0.01), 102px 0px 61px 0px rgba(97, 97, 97, 0.03), 46px 0px 46px 0px rgba(97, 97, 97, 0.05), 11px 0px 25px 0px rgba(97, 97, 97, 0.06);
}

.p-drawer-mask {
  background: rgba(0, 0, 0, 0.06);
}

@keyframes drawer-mask-anim {
  0% {
    background: transparent;
  }
  100% {
    background: rgba(0, 0, 0, 0.06);
  }
}

.p-overlay-mask-enter {
  animation: drawer-mask-anim 150ms forwards;
}

.p-drawer-header {
  display: none;
}
</style>
