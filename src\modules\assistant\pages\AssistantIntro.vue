<template>
  <div class="flex flex-1 justify-center items-center min-h-full">
    <div class="flex flex-col items-start text-left">
      <h1 class="text-[32px] font-semibold leading-[36px] tracking-[-0.64px] text-nio-grey-10 px-4">
        Hello {{ userName }},
      </h1>

      <p class="text-[28px] leading-tight font-bold text-black  px-4">
        shape your project vision into a powerful RFP,<br>
        your tender starts here.
      </p>

      <div class="my-8">
        <AssistantInputCard />
      </div>

      <p class="max-w-[700px] text-nio-grey-10 text-[14px] leading-[20px] px-4">
        <strong>Secure. Confidential. Purpose-built.</strong>
        <br>
        All data uploaded to the Assistant is processed securely within our Microsoft Azure environment. Your content is used solely to support your RFP creation – it is not stored beyond the session, reused, or used to train any AI models.
        <br>
        We do not leverage customer data to enrich Nordics or any external systems.
      </p>

      <p class="mt-[10px] max-w-[700px] text-nio-grey-10 text-[14px] leading-[20px] px-4">
        For full details, see our <a href="https://nordics.io/privacy" target="_blank" class="text-nio-grey-9 hover:underline">Privacy Policy</a> and <a href="https://nordics.io/terms-of-service" class="text-nio-grey-9 hover:underline" target="_blank">Terms of Service</a>.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import AssistantInputCard from '@/modules/assistant/components/AssistantInputCard.vue';
import { useAuthStore } from '@/modules/auth/stores/auth-store.ts';

const authStore = useAuthStore();
const userName = computed(() => authStore.userProfile?.name || '');
</script>
