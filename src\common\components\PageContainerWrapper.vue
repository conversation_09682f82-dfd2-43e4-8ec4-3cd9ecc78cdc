<script lang="ts" setup>
import { computed } from 'vue';

const props = defineProps<{
  transparent?: boolean;
}>();

const sectionClasses = computed(() => {
  return {
    'w-full h-fit rounded-4xl p-3': true,
    'bg-nio-grey-background-30 border border-nio-blue-outline-stroke-400': !props.transparent,
  };
});
</script>

<template>
  <section :class="sectionClasses">
    <slot />
  </section>
</template>
