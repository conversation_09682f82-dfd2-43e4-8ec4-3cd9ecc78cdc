import { nioAxios } from '@/axios';
import { ref } from 'vue';

export interface PollingOptions<T> {
  url: string;
  timeout: number;
  onSuccess: (data: T) => void;
  onError?: (error: unknown) => void;
  immediate?: boolean; // optional enhancement
}

export interface PollingInstance<T> {
  stop: () => void;
  lastResponse: T | null;
}

export const startPolling = <T>({
  url,
  timeout,
  onSuccess,
  onError,
  immediate = true,
}: PollingOptions<T>): PollingInstance<T> => {
  let timerId: number;
  let isPolling = false;
  const lastResponseRef = ref<T | null>(null);

  const poll = async() => {
    if (isPolling) {
      return;
    }
    isPolling = true;
    try {
      const { data } = await nioAxios.get<T>(url);
      lastResponseRef.value = data;
      onSuccess(data);
    } catch (err) {
      onError?.(err);
    } finally {
      isPolling = false;
    }
  };

  if (immediate) {
    poll();
  }
  // eslint-disable-next-line prefer-const
  timerId = window.setInterval(poll, timeout);

  return {
    stop: () => window.clearInterval(timerId),
    get lastResponse() {
      return lastResponseRef.value;
    },
  };
};
