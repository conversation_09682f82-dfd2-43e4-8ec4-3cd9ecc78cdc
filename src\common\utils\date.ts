import { DateTime } from 'luxon';

export const isToday = (date: DateTime): boolean => date.hasSame(DateTime.now(), 'day');

export const formatDateSK = (dateStr?: string): string => {
  if (!dateStr) { return '-'; }
  const date = DateTime.fromISO(dateStr);
  if (!date.isValid) { return '-'; }
  return date.toFormat('dd.MM.yyyy');
};

export const getTimeUntil = (isoDate: string): string => {
  const date = DateTime.fromISO(isoDate);
  if (!date.isValid) { return '-'; }
  return `From ${date.toFormat('MMMM d, yyyy')}`;
};

export const getTimeLeft = (isoDate: string): string => {
  const date = DateTime.fromISO(isoDate);
  if (!date.isValid) { return '-'; }
  return `Until ${date.toFormat('MMMM d, yyyy')}`;
};
