<template>
  <div class="flex gap-2 flex-wrap mt-4">
    <div
      v-for="chip in chips"
      :key="chip"
      class="px-[12px] py-[6px] border border-nio-grey-700 text-nio-grey-200 text-[18px] font-normal rounded-[30px] leading-[18px] bg-transparent"
    >
      {{ chip }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  chips: string[];
}

defineProps<Props>();
</script>
