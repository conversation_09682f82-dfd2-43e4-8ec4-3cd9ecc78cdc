<template>
  <header class="flex flex-col" role="group" aria-label="Tender header">
    <div class="flex justify-between items-center mb-4">
      <div class="text-[14px] text-nio-grey-500 leading-[14px]">
        By
        <span class="text-[14px] text-nio-blue-800 leading-[14px]">
          @{{ tender.created_by }}
        </span>
        <span class="text-[14px] text-nio-grey-500 leading-[14px] ml-1">
          {{ formattedDate }}
        </span>
      </div>

      <div class="inline-flex gap-2">
        <div
          v-if="tenderState !== 'ended'"
          class="inline-flex px-2 py-1 text-[12px] font-bold text-nio-blue-800 rounded-5 bg-nio-grey-11 leading-[14px] uppercase"
        >
          {{ tenderState }}
        </div>
        <div class="flex items-center">
          <time
            class="inline-flex px-2 py-1 text-[12px] font-bold text-nio-grey-500 rounded-l-5 bg-nio-grey-100 leading-[14px] uppercase whitespace-nowrap"
            :datetime="tender.submissions_deadline"
            aria-label="Submission deadline"
          >
            {{ tenderState !== 'ended' ? 'Ends' : 'Ended' }} {{ DateTime.fromISO(tender.submissions_deadline).toFormat('d LLLL yyyy') }}
          </time>
          <button
            class="inline-flex h-full justify-center items-center aspect-square rounded-r-5 bg-nio-blue-800 hover:bg-nio-blue-600-hover cursor-pointer"
            title="Edit submission deadline"
            @click="isEditPopoverOpen = true"
          >
            <PencilIcon class="w-4 h-4 [&_path]:fill-white" />
          </button>
        </div>
      </div>
    </div>

    <h1 class="text-[26px] font-normal text-nio-black-900 leading-[28px] tracking-[0.52px]">
      {{ tender.name }}
    </h1>

    <div class="flex items-center gap-2 mb-2">
      <h2 class="text-base font-normal text-nio-grey-500 leading-[28px] tracking-[0.52px]">
        ID: {{ tender.id }}
      </h2>
      <div title="Copy ID to clipboard" @click="copyToClipboard">
        <CopyIcon v-if="!isCopied" class="w-4 h-4 text-gray-600 hover:text-gray-700 cursor-pointer active:scale-95 transition-transform" />
        <CheckCheckIcon v-else class="w-5 h-5 text-nio-green-text" />
      </div>
    </div>

    <p class="text-[14px] font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-4">
      {{ tender.description }}
    </p>

    <div class="flex flex-wrap gap-2">
      <NioTag size="sm">
        <span class="flex items-center"><MapPin class="w-3 h-3 mr-1 " /> {{ tender.region }}</span>
      </NioTag>
      <NioTag size="sm">
        {{ tender.main_industry }}
      </NioTag>
    </div>

    <TenderSubmissionDatePopover
      v-model="isEditPopoverOpen"
      :tender-data="tender"
    />
  </header>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, ref } from 'vue';
import NioTag from '@/common/components/NioTag.vue';
import { DateTime } from 'luxon';
import type { Tender, TenderState } from '../../types/tenders-types';
import PencilIcon from '@/assets/icons/pencil-icon.svg';
import TenderSubmissionDatePopover from './TenderSubmissionDatePopover.vue';
import { CustomEvents } from '@/common/utils/custom-events';
import { CopyIcon, CheckCheckIcon, MapPin } from 'lucide-vue-next';

const { tender } = defineProps<{
  tender: Tender;
  isMatching?: boolean;
}>();
const emit = defineEmits(['updated-tender']);

const tenderState = computed<TenderState>(() => tender.status);

const onUpdatedTender = (tender: Event) => {
  const { detail } = tender as CustomEvent<{ updatedTender: Tender }>;
  emit('updated-tender', detail.updatedTender);
};

const isCopied = ref(false);

const copyToClipboard = () => {
  navigator.clipboard.writeText(tender.id);
  isCopied.value = true;
  setTimeout(() => {
    isCopied.value = false;
  }, 3000);
};

window.eventBus.addEventListener(CustomEvents.TenderDetailUpdated, onUpdatedTender);

onBeforeUnmount(() => {
  window.eventBus.removeEventListener(CustomEvents.TenderDetailUpdated, onUpdatedTender);
});

const isEditPopoverOpen = ref(false);

const formattedDate = computed(() => {
  if (!tender.created_at) {
    return '';
  }
  return DateTime.fromISO(tender.created_at).toFormat('d LLLL yy');
});
</script>
