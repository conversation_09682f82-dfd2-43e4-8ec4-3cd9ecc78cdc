<template>
  <div class="mb-6 rounded-3xl bg-white px-4 pt-4 pb-[30px] w-full">
    <div class="flex gap-[6px] mb-[30px]">
      <div
        class="py-1 px-2 rounded-5 bg-nio-grey-100 backdrop-blur-sm font-medium text-sm -tracking-028px text-nio-blue-800 w-fit"
      >
        {{ $t('assistant.request-proposal') }}: {{ props.generalRfpData.title }}
      </div>
      <div
        v-if="props.generalRfpData?.tender_id"
        class="py-1 px-2 rounded-5 bg-nio-grey-100 backdrop-blur-sm font-medium text-sm -tracking-028px text-nio-red-500 w-fit"
      >
        {{ $t('assistant.read-only') }}
      </div>
    </div>

    <header class="flex justify-start items-start mb-[10px]">
      <div>
        <h2 class="text-h4 sm:text-h3 md:text-h2 text-nio-black mb-[10px]">
          {{ props.generalRfpData.title }}
        </h2>
        <h3 class="text-nio-grey-500 text-p-base sm:text-p-l md:text-p-xl font-medium -tracking-[0.4px] leading-[22px] mb-4">
          {{ props.generalRfpData.description }}
        </h3>
        <div class="text-p-l mt-4 text-nio-grey-400">
          By <span class="text-nio-blue-800 font-medium">@{{ props.generalRfpData.created_by }}</span> ·
          {{ formattedDate }}
        </div>
      </div>
    </header>

    <div class="border-t border-nio-grey-100" />
    <div class="mt-[30px] flex justify-end">
      <button
        v-if="!props.generalRfpData?.tender_id"
        type="button"
        class="cursor-pointer py-3 px-[14px] text-center rounded-full border border-nio-grey-200 text-sm text-nio-grey-700 hover:text-nio-blue-800 font-medium -tracking-028px leading-[16px] disabled:opacity-50 disabled:cursor-not-allowed mr-2"
        @click="emit('saveForLater')"
      >
        Save For Later
      </button>
      <button
        v-if="!props.generalRfpData?.tender_id"
        type="button"
        class="relative py-3 px-[14px] bg-nio-blue-800 text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px] disabled:cursor-not-allowed disabled:opacity-50"
        :class="buttonsLoading.saveToTenders ? 'cursor-not-allowed' : 'hover:bg-nio-blue-600-hover cursor-pointer'"
        @click="onSaveToTenders"
      >
        <div v-if="buttonsLoading.saveToTenders" class="absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
          <Loader class="light" />
        </div>
        <span :class="buttonsLoading.saveToTenders ? 'opacity-10' : ''">Create New Tender</span>
      </button>
      <router-link
        v-else-if="props.generalRfpData.tender_id"
        :to="{ name: routeMap.detail.children.tenderDetail.name, params: { id: props.generalRfpData.tender_id }}"
        class="cursor-pointer py-3 px-[14px] bg-nio-blue-800 hover:bg-nio-blue-600-hover text-center rounded-full border border-nio-blue-800 text-sm text-nio-grey-background font-medium -tracking-028px leading-[16px] disabled:cursor-not-allowed disabled:opacity-50"
      >
        Open Tender
      </router-link>
    </div>
  </div>

  <div
    v-if="props.generalRfpData?.tender_id && props.generalRfpData.files.length"
    class="rounded-3xl bg-white px-4 pt-4 w-full"
  >
    <SourceFilesList :files="props.generalRfpData.files" />
  </div>
</template>

<script setup lang="ts">
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';
import { routeMap } from '@/modules/tenders/routes';
import { computed, reactive } from 'vue';
import SourceFilesList from '@/modules/assistant/components/SourceFilesList.vue';
import Loader from '@/common/components/Loader.vue';
import { DateTime } from 'luxon';

interface Props {
  generalRfpData: AssistantRfpGeneral,
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'saveToTenders', loadingPromise: PromiseWithResolvers<void>): void;
  (e: 'saveForLater'): void;
}>();

const buttonsLoading = reactive({
  saveToTenders: false,
});

const onSaveToTenders = async() => {
  buttonsLoading.saveToTenders = true;
  const buttonPromise = Promise.withResolvers<void>();
  emit('saveToTenders', buttonPromise);
  await buttonPromise.promise;
  buttonsLoading.saveToTenders = false;
};

const formattedDate = computed(() => {
  if (!props.generalRfpData.created_at) {return '';}
  return DateTime.fromISO(props.generalRfpData.created_at).toFormat('d LLLL yy / hh:mm a');
});

</script>

<style scoped lang="css">
</style>
