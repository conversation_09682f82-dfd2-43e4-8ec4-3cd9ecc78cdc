<script setup lang="ts">
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import PageCard from '@/common/components/PageCard.vue';
import ConsultantsTable from '../components/ConsultantsTable.vue';
import { ConsultantTableType } from '../types/consultants-types';

type Props = {
  tableType: ConsultantTableType;
};

defineProps<Props>();
</script>

<template>
  <PageContainerWrapper>
    <PageCard class="overflow-visible" :title="tableType === ConsultantTableType.AVAILABLE ? 'Available Consultants' : 'Engaged Consultants'">
      <KeepAlive>
        <ConsultantsTable :key="tableType" :table-type="tableType" />
      </KeepAlive>
    </PageCard>
  </PageContainerWrapper>
</template>