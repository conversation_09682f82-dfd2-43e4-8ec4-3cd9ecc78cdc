<script lang="ts" setup>
import {
  type BlurPromise,
  type DynamicFormStructure,
  type FormCollectionData,
  type FormErrors,
  FormTypeMap, transformValidationErrorsForCollection
} from '@/common/utils/forms.ts';
import { computed, reactive, ref } from 'vue';
import { nioAxios } from '@/axios.ts';
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';
import type { AssistantGuideSteps } from '@/modules/assistant/types/assistant-steps.ts';
import type { SuggestedResourcesData, SuggestedResourcesObject } from '@/common/utils/forms.ts';
import AssistantFormButtons from '@/modules/assistant/components/AssistantFormButtons.vue';
import type FormCollectionRoot from '@/common/components/forms/FormCollectionRoot.vue';
import { routeMap } from '@/modules/assistant/routes';
import { useRouter } from 'vue-router';
import { sleep } from '@/common/utils/sleep.ts';
import { saveForm } from '@/modules/assistant/util/assistant-form';

interface Props {
  generalRfpData: AssistantRfpGeneral,
  currentGuideStep: AssistantGuideSteps,
}

const props = defineProps<Props>();
const emit = defineEmits(['nextStep', 'previousStep']);
const router = useRouter();

const isError = ref(false);
const dynamicFormStruct = ref<DynamicFormStructure>();
const resourcesForm = ref<InstanceType<typeof FormCollectionRoot>>();
const formErrors = ref<Record<number, FormErrors>>({});
const isFormDisabled = computed(() => Boolean(props.generalRfpData.tender_id));
const suggestedData = reactive<SuggestedResourcesObject>({
  data: [],
  isLoading: true,
});

if (!isFormDisabled.value) {
  nioAxios.patch(`/enterprise/assistant/rfp/${props.generalRfpData.id}/step`, {
    step: 'resources',
  });
}

const fetchStepData = async() => {
  try {
    const response = await nioAxios.get<DynamicFormStructure<FormCollectionData>>(`/enterprise/assistant/rfp/${props.generalRfpData.id}/resources`);
    dynamicFormStruct.value = response.data;
  } catch {
    isError.value = true;
  } finally {
    await sleep(3000);
  }
};

const fetchSuggestedResourcesData = async() => {
  suggestedData.isLoading = true;
  try {
    const response = await nioAxios.get<{data: SuggestedResourcesData}>(`/enterprise/assistant/rfp/${props.generalRfpData.id}/resources/suggested`);
    suggestedData.data = response.data.data;
  } catch {
    isError.value = true;
  } finally {
    suggestedData.isLoading = false;
  }
};

const onFormSubmit = async() => {
  if (isFormDisabled.value) {
    return;
  }
  formErrors.value = {};
  await saveForm({
    rfpId: props.generalRfpData.id,
    formPayload: { resources: resourcesForm.value!.getFormData(), },
    onValidationError422: e => {
      formErrors.value = transformValidationErrorsForCollection(e, 'resources');
    },
    retryWithoutValidation: true,
  });
  if (Object.values(formErrors.value).length === 0) {
    emit('nextStep');
  }
};

const handleSubmitForm = () => {
  resourcesForm.value?.submit();
};

const onFormBlur = async(blurPromise?: BlurPromise) => {
  formErrors.value = {};
  await saveForm({
    rfpId: props.generalRfpData.id,
    showToast: false,
    blurPromise,
    formPayload: { resources: resourcesForm.value!.getFormData(), },
    onValidationError422: e => {
      formErrors.value = transformValidationErrorsForCollection(e, 'resources');
    },
    retryWithoutValidation: true,
  });
};

const saveForLater = async() => {
  await saveForm({
    showToast: false,
    shouldValidate: false,
    rfpId: props.generalRfpData.id,
    formPayload: { resources: resourcesForm.value!.getFormData(), },
  });
  await router.push({ name: routeMap.assistant.name });
};

await fetchStepData();
fetchSuggestedResourcesData();
</script>

<template>
  <div class="w-full">
    <div v-if="!isError && dynamicFormStruct" class="w-full">
      <div class="py-1 px-2 rounded-5 bg-nio-grey-100 backdrop-blur-sm font-medium text-sm -tracking-028px text-nio-blue-800 w-fit mb-8">
        {{ $t('assistant.request-proposal') }}: {{ props.generalRfpData.title }}
      </div>
      <header class="flex justify-between items-start mb-11">
        <div>
          <h2 class="text-h28 text-nio-black font-semibold">
            {{ $t('assistant.r-and-technical-details') }}
          </h2>
          <h3 class="text-nio-grey-500 text-h5 font-semibold -tracking-[0.4px] leading-[22px]">
            Specify the skills, tools, and expertise your project needs.
          </h3>
        </div>
        <div class="text-h1 font-medium leading-[38px] inline-flex gap-1">
          <span class="text-nio-black-900">2</span>
          <span class="text-nio-grey-500">/</span>
          <span class="text-nio-grey-500">2</span>
        </div>
      </header>
      <component
        :is="FormTypeMap[dynamicFormStruct.form]"
        ref="resourcesForm"
        :errors="formErrors"
        :disabled="isFormDisabled"
        :form-structure="dynamicFormStruct as DynamicFormStructure<FormCollectionData>"
        :error-position="'right'"
        :suggested-data="suggestedData"
        @form-submit="onFormSubmit"
        @nio-blur="onFormBlur"
      >
        <template #form-bottom>
          <AssistantFormButtons
            :current-guide-step="currentGuideStep"
            :tender-id="generalRfpData.tender_id"
            @previous-step="emit('previousStep')"
            @save-for-later="saveForLater"
            @submit-form="handleSubmitForm"
          />
        </template>
      </component>
    </div>
    <div v-else>
      ERROR
    </div>
  </div>
</template>
