<template>
  <div>
    <div class="flex justify-between items-center mb-[36px] mt-[45px]">
      <h1 class="text-lg text-black text-p-xl font-normal leading-normal tracking-[0.72px]">
        {{ $t('navigation.companies') }}
      </h1>
      <span class="font-normal text-gray-600 text-[16px] font-['Inter'] leading-normal">
        <span class="text-nio-blue-800 font-normal">{{ verifiedVendors.length }}</span> of {{ store.filteredVendors.value.length }}
      </span>
    </div>
    <div
      class="grid gap-4"
      style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); grid-auto-rows: auto;"
    >
      <div
        v-for="vendor in reorderedCompanies"
        :key="vendor.id || vendor.importId"
        :class="{ 'col-span-2': vendor.status === 'unverified' }"
      >
        <Card
          :id="vendor.id"
          :type="vendor.status"
          :import-id="vendor.importId"
          :name="vendor.name"
          :similar-vendors="vendor.similarVendors"
        />
      </div>
    </div>
    <CTAButtonAnimatedScroll
      v-if="shouldShowAddToListBtn"
      class="w-[300px] h-[100px] text-h5 flex items-center justify-center gap-2 text-white"
      :bottom-css="'100px'"
      :is-loading="showLoading"
      role="button"
      aria-label="Add to list"
      @click="onClickAddToList"
    >
      <div
        class="flex items-center justify-center rounded-full bg-white w-10 h-10"
      >
        <Loader v-if="showLoading" />
        <img
          v-else
          src="@/assets/icons/add-to-list.png"
          alt="Add to list"
          style="width: 16px; height: 16px;"
        >
      </div>
      <div class="text-h5 font-normal">
        {{ $t('intro.add-to-list') }}
      </div>
    </CTAButtonAnimatedScroll>
    <div class="h-[230px] w-full bg-none" />
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue';
import { useVendorsStore } from '@/modules/intro/stores/companiesStore.ts';
import Card from './VerificationCard.vue';
import { useRouter } from 'vue-router';
import { routeMap } from '@/modules/companies/routes';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import { toast } from '@/common/utils/NotificationService';
import CTAButtonAnimatedScroll from '@/common/components/CTAButtonAnimatedScroll.vue';
import Loader from '@/common/components/Loader.vue';

interface Vendor {
  id?: string;
  importId: string;
  name: string;
  status: 'pending' | 'verified' | 'unverified';
  similarVendors: { companyId: string; name: string }[];
}

const emit = defineEmits(['allVendorsVerified']);

const store = useVendorsStore();
const authStore = useAuthStore();
const currentProfileCompanyIds = authStore?.userProfile?.workspaces
  ?.flatMap(workspace => workspace.companies.map(company => company.id)) || [];
const router = useRouter();
const showLoading = ref(false);
const toastShown = ref(false);

const verifiedVendors = computed(() => {
  return store.filteredVendors.value.filter(vendor => vendor.status === 'verified');
});

const unverifiedVendors = computed(() => {
  return store.filteredVendors.value.filter(vendor => vendor.status === 'unverified');
});

const allVendorsVerified = computed(() => store.filteredVendors.value.every(v => v.status === 'verified'));
const newVendorCount = computed(() => store.filteredVendors.value?.length ?? 0);
const existingVendorCount = computed(() => currentProfileCompanyIds?.length ?? 0);
const shouldShowAddToListBtn = computed(() => allVendorsVerified.value && newVendorCount.value > 0 && (newVendorCount.value + existingVendorCount.value > 1));

watch(
  unverifiedVendors,
  newVal => {
    if (newVal.length > 0 && !toastShown.value) {
      toast.show(
        'Unverified Vendors',
        'Before adding Vendors to your list, you need to check the Unverified.',
        'error'
      );
      toastShown.value = true;
    }
  },
  { immediate: true, deep: true }
);

watch(allVendorsVerified, (newVal, oldValue) => {
  if (!oldValue && newVal) {
    emit('allVendorsVerified');
  }
});

const reorderedCompanies = computed((): Vendor[] => {
  const singleWidth = 1;
  const doubleWidth = 2;
  const maxColumns = 4;

  const result: Vendor[] = [];
  let currentRowWidth = 0;

  store.filteredVendors.value.forEach(vendor => {
    const itemWidth = vendor.status === 'unverified' ? doubleWidth : singleWidth;

    if (currentRowWidth + itemWidth > maxColumns) {
      currentRowWidth = 0;
    }

    result.push(vendor as Vendor);
    currentRowWidth += itemWidth;
  });

  return result;
});

const onClickAddToList = async(event: Event) => {
  event.stopPropagation();
  showLoading.value = true;

  try {
    await new Promise(resolve => setTimeout(resolve, 2000));
    await store.addToList();
    await authStore.fetchUserProfile();
    store.resetStore();
    await router.replace({ name: routeMap.companies.name });
  } catch (e) {
    console.error(e);
  } finally {
    showLoading.value = false;
  }
};
</script>

<style scoped lang="css">
.grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.col-span-2 {
  grid-column: span 2;
}

.sticky-add-to-list {
  opacity: 0;
  animation: var(--animation-fade-in) forwards, bounce var(--ease-squish-2) forwards;
  animation-duration: 2s;
}
</style>
