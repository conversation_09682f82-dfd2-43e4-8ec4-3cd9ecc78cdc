<template>
  <div class="flex flex-col items-center justify-center h-full w-full">
    <LoaderSmall />
    <div class="mt-[20px] text-[18px] font-medium leading-[24px] tracking-[-0.36px] text-nio-black text-center max-w-[80%] h-12">
      <AnimatedTextSlot :key="primaryKey" :speed-multiplier="1.2">
        {{ texts[currentTextIndex][0] }}
        <br>
        {{ texts[currentTextIndex][1] }}
      </AnimatedTextSlot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LoaderSmall from '@/common/components/Loader-small.vue';
import AnimatedTextSlot from '@/common/components/AnimatedTextSlot.vue';

const texts = [
  ['Did you know? Clearly defined RFPs can lead to significant savings—both time and money.', "Hold tight, we're shaping your RFP."],
  ['Did you know? A well-defined project scope drives better vendor alignment and results.', 'Your request is coming together.'],
  ['Did you know? The Assistant helps refine your input to save time and focus not only for project but also engineering teams.', 'Final touches underway.'],
  ['The Assistant is working to extract key details and align them with your project goals.', 'Nearly ready to unlock your next great project.'],
  ['Effortlessly transforming your input into a smarter, structured RFP.', 'We’re refining your input for maximum clarity.'],
  ['Your request is becoming actionable intelligence—because the right data makes all the difference.', 'Hold tight, we’re preparing something great for you.'],
  ['Empowering smarter vendor decisions, right from the start.', 'This is where your project begins to take shape.'],
  ['Your RFP is more than a document—it’s a roadmap to success.', 'Almost there…'],
  ['Every detail matters. The Assistant is preparing your RFP to deliver results.', 'We are on it–success is just a step away.']
];

const currentTextIndex = ref<number>(Math.floor(Math.random() * texts.length));
const primaryKey = ref(0);
const secondaryKey = ref(1);

const startAnimation = () => {
  setTimeout(() => {
    let usedIndexes: number[] = [currentTextIndex.value];
    const nextText = () => {
      let newIndex;
      do {
        newIndex = Math.floor(Math.random() * texts.length);
      } while (usedIndexes.includes(newIndex));

      currentTextIndex.value = newIndex;
      usedIndexes.push(newIndex);

      primaryKey.value++;
      secondaryKey.value++;

      if (usedIndexes.length === texts.length) {
        usedIndexes = [];
      }

      setTimeout(nextText, 5000);
    };

    nextText();
  }, 5000);
};

onMounted(startAnimation);
</script>
