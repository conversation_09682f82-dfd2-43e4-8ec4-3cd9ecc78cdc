<script setup lang="ts">
import { ref, watch } from 'vue';
import { debounce } from 'lodash-es';
import { XIcon } from 'lucide-vue-next';

const modelValue = ref<number|undefined>(undefined);
const emit = defineEmits<{ (e: 'update:modelValue', value: any): void }>();

defineProps<{
  type?: 'text' | 'number';
  name?: string;
  filter: {
    name: string;
    component: { id: string; options?: any; props?: Record<string, any>, min?: number, max?: number };
  };
}>();

const debouncedEmit = debounce((value: any) => {
  emit('update:modelValue', value);
}, 300);

watch(modelValue, newValue => {
  debouncedEmit(newValue);
});
</script>

<template>
  <div class="relative show-arrows">
    <input
      v-model="modelValue"
      type="number"
      :placeholder="name"
      :min="filter.component.min"
      :max="filter.component.max"
      class="w-32 h-9 px-3 py-2 placeholder:text-nio-grey-900 text-gray-700 text-sm rounded-full border border-gray-300/40 hover:border-nio-blue-400 focus:outline-none focus:ring-0 focus:bg-white transition-all duration-200 hover:bg-gray-50"
    >
    <button
      v-if="typeof modelValue === 'number'"
      class="absolute inset-y-0 right-7 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
      @click="modelValue = undefined"
    >
      <XIcon class="w-4 h-4 cursor-pointer" />
    </button>
  </div>
</template>

<style scoped>
.show-arrows {
  input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
   opacity: 1;
  }
}
</style>