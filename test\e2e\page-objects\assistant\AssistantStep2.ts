import { type Locator, type Page } from '@playwright/test';
import { AssistantBaseStep } from './AssistantBaseStep';

export class AssistantStep2 extends AssistantBaseStep {
  readonly heading: Locator;
  readonly positionHeaders: Locator;
  readonly createRfpButton: Locator;

  constructor(page: Page) {
    super(page);
    this.heading = page.getByRole('heading', { name: 'Resource & Technical Details' });
    this.positionHeaders = page.getByRole('heading', { name: 'Position' });
    this.createRfpButton = page.getByRole('button').filter({ hasText: 'Create RFP' });
  }

  async getPositionCount() {
    return this.positionHeaders.count();
  }

  async addPosition(positionIndex) {
    await this.page.getByRole('button', { name: 'Add Position' }).nth(positionIndex).click();
  }

  async deletePosition(positionIndex) {
    // TODO(<PERSON>): Use button locators once the elements are fixed.this.positionOptionsButton = this.page.getByRole('listbox').nth(this.positionIndex);
    await this.page.getByRole('listbox').nth(positionIndex).click();
    await this.page.getByRole('option', { name: 'Delete' }).click();
  }
}
