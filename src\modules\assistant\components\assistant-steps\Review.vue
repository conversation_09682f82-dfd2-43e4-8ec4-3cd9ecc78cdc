<script lang="ts" setup>
import {
  type BlurPromise,
  type DynamicFormStructure,
  type FormCollectionData,
  type FormErrors,
  type FormStandardData,
  FormTypeMap,
  transformValidationErrorsForCollection,
  transformValidationErrorsForStandardForm
} from '@/common/utils/forms.ts';
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';
import { computed, ref } from 'vue';
import { nioAxios } from '@/axios.ts';
import type { AssistantGuideSteps } from '@/modules/assistant/types/assistant-steps.ts';
import FormStandardRoot from '@/common/components/forms/FormStandardRoot.vue';
import FormCollectionRoot from '@/common/components/forms/FormCollectionRoot.vue';
import { routeMap } from '@/modules/assistant/routes';
import { routeMap as tendersRouteMap } from '@/modules/tenders/routes';
import { useRouter } from 'vue-router';
import { saveForm } from '@/modules/assistant/util/assistant-form';
import { toast } from '@/common/utils/NotificationService';

interface Props {
  generalRfpData: AssistantRfpGeneral,
  currentGuideStep: AssistantGuideSteps,
}

const props = defineProps<Props>();
const router = useRouter();

const isError = ref(false);
const savingTender = ref(false);
const isFormDisabled = computed(() => Boolean(props.generalRfpData.tender_id) || savingTender.value);

const dynamicFormStructInfo = ref<DynamicFormStructure>();
const formErrorsInfo = ref<FormErrors>([]);
const infoForm = ref<InstanceType<typeof FormStandardRoot>>();

const dynamicFormStructResources = ref<DynamicFormStructure>();
const formErrorsResources = ref<Record<number, FormErrors>>([]);
const resourcesForm = ref<InstanceType<typeof FormCollectionRoot>>();

if (!isFormDisabled.value) {
  nioAxios.patch(`/enterprise/assistant/rfp/${props.generalRfpData.id}/step`, {
    step: 'final',
  });
}

const fetchStepData = async() => {
  try {
    const [responseInfo, responseResources] = await Promise.all([
      nioAxios.get<DynamicFormStructure>(`/enterprise/assistant/rfp/${props.generalRfpData.id}/info`),
      nioAxios.get<DynamicFormStructure>(`/enterprise/assistant/rfp/${props.generalRfpData.id}/resources`),
    ]);
    dynamicFormStructResources.value = responseResources.data;
    dynamicFormStructInfo.value = responseInfo.data;
  } catch {
    isError.value = true;
  }
};

const getFormData = () => {
  return {
    info: infoForm.value!.getFormData(),
    resources: resourcesForm.value!.getFormData(),
  };
};

const saveToTenders = async(loadingPromise: PromiseWithResolvers<void>) => {
  if (isFormDisabled.value) {
    loadingPromise.resolve();
    return;
  }
  savingTender.value = true;
  formErrorsInfo.value = [];
  formErrorsResources.value = [];
  const formSuccess = await saveForm({
    rfpId: props.generalRfpData.id,
    formPayload: getFormData(),
    onValidationError422: e => {
      formErrorsResources.value = transformValidationErrorsForCollection(e, 'resources');
      formErrorsInfo.value = transformValidationErrorsForStandardForm(e, 'info');
    },
    retryWithoutValidation: true,
  });
  if (!formSuccess) {
    savingTender.value = false;
    loadingPromise.resolve();
    return;
  }
  const formData = getFormData();
  try {
    toast.show('Creating tender', 'The creation of the tender is underway. You will be automatically redirected when it is complete.', 'info');
    const response = await nioAxios.post<{
      data: { id: string }
    }>(`/enterprise/assistant/rfp/${props.generalRfpData.id}/tender`, formData);
    await router.replace({ name: tendersRouteMap.detail.children.tenderDetail.name, params: { id: response.data.data.id } });
    loadingPromise.resolve();
  } catch (e: any) {
    if (e.status === 422) {
      formErrorsResources.value = transformValidationErrorsForCollection(e, 'resources');
      formErrorsInfo.value = transformValidationErrorsForStandardForm(e, 'info');
    }
  } finally {
    savingTender.value = false;
    loadingPromise.resolve();
  }
};

const saveForLater = async() => {
  await saveForm({
    shouldValidate: false,
    showToast: false,
    rfpId: props.generalRfpData.id,
    formPayload: getFormData(),
  });
  await router.push({ name: routeMap.assistant.name });
};
defineExpose({ saveToTenders, saveForLater });

const onFormBlur = async(blurPromise?: BlurPromise) => {
  formErrorsInfo.value = [];
  formErrorsResources.value = [];
  await saveForm({
    showToast: false,
    blurPromise,
    rfpId: props.generalRfpData.id,
    formPayload: getFormData(),
    onValidationError422: e => {
      formErrorsResources.value = transformValidationErrorsForCollection(e, 'resources');
      formErrorsInfo.value = transformValidationErrorsForStandardForm(e, 'info');
    },
    retryWithoutValidation: true,
  });
};

await fetchStepData();
</script>

<template>
  <div class="w-full">
    <div v-if="!isError" class="w-full">
      <div class="text-sm text-nio-grey-500 font-semibold -tracking-028px mb-7">
        General Information
      </div>
      <component
        :is="FormTypeMap[dynamicFormStructInfo!.form]"
        ref="infoForm"
        :errors="formErrorsInfo"
        :disabled="isFormDisabled"
        :form-structure="dynamicFormStructInfo as DynamicFormStructure<FormStandardData>"
        @nio-blur="onFormBlur"
      />
      <div class="text-sm text-nio-grey-500 font-semibold -tracking-028px mt-11 mb-14">
        Resource & Technical Details
      </div>
      <component
        :is="FormTypeMap[dynamicFormStructResources!.form]"
        ref="resourcesForm"
        :errors="formErrorsResources"
        :disabled="isFormDisabled"
        :form-structure="dynamicFormStructResources as DynamicFormStructure<FormCollectionData>"
        :error-position="'right'"
        :default-collapse="true"
        @nio-blur="onFormBlur"
      />
      <!-- <div class="text-sm text-nio-grey-500 font-semibold -tracking-028px mt-11 mb-14">
        Scope & Deliverables
      </div>
      <component
        :is="FormTypeMap[dynamicFormStructDeliverables!.form]"
        ref="deliverablesForm"
        :errors="formErrorsDeliverables"
        :disabled="isFormDisabled"
        :form-structure="dynamicFormStructDeliverables as DynamicFormStructure<FormStandardData>"
        @nio-blur="onFormBlur"
      /> -->
    </div>
  </div>
</template>
