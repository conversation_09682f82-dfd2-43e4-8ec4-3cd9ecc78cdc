<script setup lang="ts">
import type { TenderCandidate } from '@/modules/tenders/types/tenders-types';
import LabelBadge from '@/modules/tenders/components/LabelBadge.vue';
import { CircleCheck, AlertTriangle, Info } from 'lucide-vue-next';
import { candidateScoreToPoints, candidateScoreToText, isScoreAvailable } from '@/common/utils/score';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import { useWorkspaceRate } from '@/common/composables/useHourlyRate';
import { computed } from 'vue';
import NioTooltip from '@/common/components/NioTooltip.vue';

const { candidate } = defineProps<{ candidate: TenderCandidate }>();
const { getValidRate } = useWorkspaceRate();

const technologies = computed(() => candidate.matching?.technologies || []);
const matchedTechnologies = computed(() =>
  technologies.value.filter(t => t.score === 100)
);
const unmatchedTechnologies = computed(() =>
  technologies.value.filter(t => t.score === 0)
);
const alternativeTechnologies = computed(() =>
  technologies.value.filter(t => t.score > 0 && t.score < 100)
);
const alternativeTechnologiesNames = computed(() =>
  alternativeTechnologies.value.map(t => t.name)
);
const alternativeTechnologiesAlternatives = computed(() => {
  const found = alternativeTechnologies.value.flatMap(t => t.found_by);
  // remove duplicates
  return [...new Set(found)];
});

const experiencesScore = computed(() => {
  return isScoreAvailable(candidate.matching?.experiences_score)
    ? candidateScoreToPoints(candidate.matching?.experiences_score, 'experience') :
    0;
});

const experiencesScoreDescription = computed(() => {
  const count = (candidate.matching?.experiences_count ?? 0) > 100 ? '100+' : candidate.matching?.experiences_count ?? '';
  const plural = (candidate.matching?.experiences_count ?? 0) === 1 ? 'position' : 'positions';

  switch (experiencesScore.value) {
    case 0:
      if (candidate.matching?.experiences_count && candidate.matching?.experiences_count > 0) {
        return `${candidate.name} previously held ${count} similar ${plural}, but none closely match the applied position.`;
      } else {
        return `${candidate.name} has no prior experience in similar positions.`;
      }
    case 1:
      return `${candidate.name} held ${count} similar ${plural}, with limited relevance to the applied position.`;
    case 2:
      return `${candidate.name} held ${count} similar ${plural}, showing good alignment with the applied position.`;
    case 3:
      return `${candidate.name} held ${count} highly relevant ${plural}, strongly matching the applied position.`;
  }

  return '';
});

const candidateRate = computed(():string => (getValidRate(candidate.rate) ?? '-').toString());

const RATE_STROKE_MIN = 155;
const RATE_STROKE_MAX = 205;

const rateStrokeOffset = computed(() => {
  const lowValue = candidate.position.price;
  const highValue = candidate.position.price_to;
  const rate = candidate.rate ?? 0;

  switch (true) {
    case rate <= lowValue:
      return RATE_STROKE_MAX;
    case rate >= highValue:
      return RATE_STROKE_MIN;
    case lowValue === highValue && rate === lowValue:
      return (RATE_STROKE_MAX + RATE_STROKE_MIN) / 2;
    default:
      return RATE_STROKE_MAX - (RATE_STROKE_MAX - RATE_STROKE_MIN) * (rate - lowValue) / (highValue - lowValue);
  }
});

</script>

<template>
  <div class="h-full px-4 pb-2">
    <div class="flex flex-wrap justify-center gap-2 mt-5 -mb-[30px]">
      <NioScoreProgress
        v-if="isScoreAvailable(candidate.matching?.technologies_score)"
        :points="candidateScoreToPoints(candidate.matching?.technologies_score, 'technologies')"
        size="xl"
        :title="candidateScoreToText(candidate.matching?.technologies_score, 'technologies')"
        subtitle="Technologies"
      />

      <NioScoreProgress
        v-if="isScoreAvailable(candidate.matching?.experiences_score)"
        :points="candidateScoreToPoints(candidate.matching?.experiences_score, 'experience')"
        size="xl"
        :title="candidateScoreToText(candidate.matching?.experiences_score, 'experience')"
        subtitle="Experience"
      />

      <div v-if="candidate.matching?.seniority_match" class="flex flex-col items-center justify-center">
        <div class=" size-40 flex flex-col items-center justify-center pb-[30px]">
          <div class="text-md font-bold mb-2 flex items-center justify-center gap-1">
            <template v-if="candidate.matching.seniority_match.toLowerCase() === 'qualified' || candidate.matching.seniority_match.toLowerCase() === 'overqualified'">
              <CircleCheck class="text-nio-green-text inline w-3.5 h-3.5" /> <span class="text-nio-green-text"> {{ candidate.matching.seniority_match }} </span>
            </template>
            <template v-else-if="candidate.matching.seniority_match.toLowerCase() === 'unqualified' || candidate.matching.seniority_match.toLowerCase() === 'underqualified'">
              <span class="text-nio-green-text "> {{ candidate.matching.seniority_match }} </span>
            </template>
          </div>
          <div class="text-sm block whitespace-nowrap">
            Seniority
          </div>
        </div>
      </div>

      <!-- TODO: Refactor to reusable component -->
      <div
        v-if="typeof candidate.matching?.rate_difference_percentage === 'number'"
        class="flex flex-col items-center justify-center"
      >
        <div class="relative size-40 ">
          <svg class="size-full rotate-180 " viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">

            <defs>
              <linearGradient
                id="successGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="90%" style="stop-color:#e8e8e8;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#d0e5d6;stop-opacity:1" />
              </linearGradient>
            </defs>

            <circle
              cx="18"
              cy="18"
              r="16"
              fill="none"
              stroke="url(#successGradient)"
              stroke-width="1.5"
              stroke-dashoffset="0"
              stroke-dasharray="13 100"
              stroke-linecap="round"
            />

            <circle
              cx="18"
              cy="18"
              r="16"
              fill="none"
              class="stroke-current text-nio-grey-100"
              stroke-width="1.5"
              stroke-dashoffset="103"
              stroke-dasharray="19 100"
              stroke-linecap="round"
            />

            <defs>
              <linearGradient
                id="dangerGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" style="stop-color:#ffe2e2;stop-opacity:1" />
                <stop offset="10%" style="stop-color:#e8e8e8;stop-opacity:1" />
              </linearGradient>
            </defs>
            <circle
              cx="18"
              cy="18"
              r="16"
              fill="none"
              stroke="url(#dangerGradient)"
              stroke-width="1.5"
              stroke-dashoffset="75"
              stroke-dasharray="13 100"
              stroke-linecap="round"
            />

            <!-- Current rate dot -->
            <circle
              cx="18"
              cy="18"
              r="16"
              fill="none"
              class="stroke-current "
              style="color: #f9f9f9"
              stroke-width="4"
              :stroke-dashoffset="rateStrokeOffset"
              stroke-dasharray="0 103"
              stroke-linecap="round"
            />
            <circle
              cx="18"
              cy="18"
              r="16"
              fill="none"
              class="stroke-current text-nio-green-text"
              stroke-width="3"
              :stroke-dashoffset="rateStrokeOffset"
              stroke-dasharray="0 103"
              stroke-linecap="round"
            />
            <!-- end of current rate dot -->

          </svg>

          <div class="absolute top-21 start-1 transform -translate-x-1 text-center">
            <div class="text-[10px] block whitespace-nowrap text-nio-green-text">
              {{ getValidRate(candidate.position.price) }}
            </div>
          </div>
          <div class="absolute top-21 end-1 transform translate-x-1 text-center">
            <div class="text-[10px] block whitespace-nowrap text-nio-red-500">
              {{ getValidRate(candidate.position.price_to) }}
            </div>
          </div>

          <div class="absolute top-11 start-1/2 transform -translate-x-1/2 text-center">
            <!-- Conditions are taken from NioScoreProgress.vue -->
            <div
              class=" font-semibold text-nio-green-text"
              :class="{
                'text-2xl mb-1': (!candidateRate) || (candidateRate && candidateRate.length <= 10),
                'text-[20px] leading-[16px] mb-3': (!candidateRate) || (candidateRate && candidateRate.length >= 8 && candidateRate.length <= 10),
                'text-1xl leading-[16px] mb-3': (candidateRate && candidateRate.length > 10),
              }"
            >
              {{ candidateRate }}
            </div>
            <div class="text-sm block whitespace-nowrap">
              Rate
            </div>
          </div>
        </div>
      </div>
    </div>

    <template v-if="candidate.matching?.technologies?.length">
      <!-- Technologies -->
      <hr class=" border-nio-grey-100">

      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 ">
          Technologies
        </h4>

        <div class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] ">
          <!-- Matched technologies -->
          <div class="flex flex-wrap gap-2 justify-center mb-3">
            <LabelBadge
              v-for="tech in matchedTechnologies"
              :key="tech.name"
              type="success"
              size="sm"
            >
              <span class="flex items-center gap-0.5">{{ tech.name }}</span>
            </LabelBadge>

            <LabelBadge
              v-for="tech in unmatchedTechnologies"
              :key="tech.name"
              type="danger"
              size="sm"
            >
              <span class="flex items-center gap-0.5 line-through">{{ tech.name }}</span>
            </LabelBadge>
          </div>

          <!-- Alternative solution for technologies -->
          <p
            v-if="alternativeTechnologies.length > 0 && alternativeTechnologiesAlternatives.length > 0"
            class="text-[14px] text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-3 text-center"
          >
            <AlertTriangle class="text-yellow-500 inline w-4 h-4 mr-1" />
            {{ candidate.name }} doesn't use
            <strong>{{ alternativeTechnologiesNames.join(', ') }}</strong>,
            but have relevant experience with similar technologies such as
            <strong>{{ alternativeTechnologiesAlternatives.join(', ') }}</strong>.
          </p>
        </div>
      </div>
    </template>

    <template v-if="isScoreAvailable(candidate.matching?.experiences_score)">
      <!-- Experience -->
      <hr class=" border-nio-grey-100">

      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 ">
          Experience
        </h4>

        <p class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px]">
          <CircleCheck v-if="candidate.matching?.experiences_score === 3" class="text-nio-green-text inline w-4 h-4" />
          <CircleCheck v-else-if="candidate.matching?.experiences_score === 2" class="text-yellow-500 inline w-4 h-4" />
          <AlertTriangle v-else-if="candidate.matching?.experiences_score === 1" class="text-yellow-500 inline w-4 h-4" />
          <CircleX v-else class="text-red-500 inline w-4 h-4" />
          {{ experiencesScoreDescription }}
        </p>
      </div>
    </template>

    <template v-if="candidate.matching?.seniority_match">
      <!-- Seniority -->
      <hr class=" border-nio-grey-100">

      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 flex items-center justify-center gap-1">
          <span>Seniority</span>
        </h4>
        <p class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-4">
          <template v-if="candidate.matching.seniority_match === 'Underqualified'">
            <AlertTriangle class="text-yellow-500 inline w-4 h-4" />
            {{ candidate.name }}’s current seniority level - <span class="font-bold">{{ candidate.seniority }}</span> - is lower than the required seniority for this position.
          </template>

          <template v-else>
            <CircleCheck class="text-nio-green-text inline w-4 h-4" />
            {{ candidate.name }}’s seniority - <span class="font-bold">{{ candidate.seniority }}</span> - is
            <strong class="text-nio-green-text">{{ candidate.matching.seniority_match.toLowerCase() }}</strong>
            compared to the required level.
          </template>
        </p>
      </div>
    </template>

    <template v-if="typeof candidate.matching?.rate_difference_percentage === 'number'">
      <!-- Rate -->
      <hr class=" border-nio-grey-100">
      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 flex items-center justify-center gap-1">
          <span>Rate</span>
          <NioTooltip class="inline-flex" text="Rate does not influence match score.">
            <Info class="text-nio-grey-500 inline w-3.5 h-3.5 mt-[1px] cursor-pointer hover:opacity-70" />
          </NioTooltip>
        </h4>
        <p class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-4">
          <span v-if="candidate.matching.rate_difference_percentage >= 1">
            <CircleCheck class="text-nio-green-text inline w-4 h-4" />
            {{ candidate.name }}’s rate {{ getValidRate(candidate.rate) }} is lower by <strong class="text-nio-green-text">{{ candidate.matching.rate_difference_percentage }}%</strong> compared to the required position's rate.
          </span>
          <span v-else-if="candidate.matching.rate_difference_percentage < 1 && candidate.matching.rate_difference_percentage > -1">
            <CircleCheck class="text-nio-green-text inline w-4 h-4" />
            {{ candidate.name }}’s rate {{ getValidRate(candidate.rate) }} is in range of the required position's rate.
          </span>
          <span v-else-if="candidate.matching.rate_difference_percentage <= -1">
            <AlertTriangle class="text-yellow-500 inline w-4 h-4" />
            {{ candidate.name }}’s rate {{ getValidRate(candidate.rate) }} is higher by <strong class="text-yellow-500">{{ candidate.matching.rate_difference_percentage }}%</strong> compared to the required position's rate.
          </span>
        </p>
      </div>
    </template>
  </div>
</template>
