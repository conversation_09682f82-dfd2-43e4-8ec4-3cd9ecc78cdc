<template>
  <nav ref="menuRef" class="relative" aria-label="Main menu">
    <button
      class="p-4 cursor-pointer"
      type="button"
      title="Main Menu"
      @click="toggleMenu"
    >
      <MenuIcon class="transition-transform duration-300" :class="{ 'rotate-45': isOpen }" />
    </button>

    <transition>
      <div
        v-if="isOpen"
        class="absolute top-5 left-7 mt-2 w-60 bg-nio-grey-5 border border-nio-grey-7 text-nio-text-white rounded-2xl overflow-hidden z-50 px-2 pt-2 pb-2"
      >
        <div
          class="px-2 py-3 text-[12px] font-medium text-nio-grey-6 leading-none tracking-[-0.24px] overflow-hidden text-ellipsis whitespace-nowrap"
        >
          {{ userEmail }}
        </div>

        <div class="border-t border-nio-grey-7 my-1" />

        <!-- MAIN NAV -->
        <ul>
          <li v-for="item in menuLinks" :key="item.label">
            <router-link
              :to="{name: item.routeName}"
              class="w-full text-left p-[10px] flex items-center gap-3 rounded-lg group hover:bg-nio-grey-4 transition"
              @click="isOpen = false"
            >
              <span
                class="w-4 h-4 rounded-full bg-nio-grey-8 group-hover:bg-nio-grey-3 transition"
              />
              <span
                class="text-[12px] font-medium text-nio-text-white leading-none tracking-[-0.24px] overflow-hidden text-ellipsis whitespace-nowrap"
              >
                {{ item.label }}
              </span>
            </router-link>
          </li>
        </ul>

        <div class="border-t border-nio-grey-7 my-1" />

        <!-- LOGOUT -->
        <ul>
          <li>
            <button
              class="w-full text-left p-[10px] flex items-center gap-3 rounded-lg group hover:bg-nio-grey-4 transition cursor-pointer"
              @click="navigateTo('/logout')"
            >
              <span
                class="w-4 h-4 rounded-full bg-nio-grey-8 group-hover:bg-nio-grey-3 transition"
              />
              <span
                class="text-[12px] font-medium text-nio-text-white leading-none tracking-[-0.24px] overflow-hidden text-ellipsis whitespace-nowrap"
              >
                Log out
              </span>
            </button>
          </li>
        </ul>
      </div>
    </transition>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import MenuIcon from '@/assets/icons/menu-icon.svg';
import { useAuthStore } from '@/modules/auth/stores/auth-store.ts';
import { menuLinks } from '@/config/menu';

const isOpen = ref(false);
const menuRef = ref<HTMLElement | null>(null);
const router = useRouter();
const authStore = useAuthStore();

const userEmail = computed(() => authStore.userProfile?.email || '');

const toggleMenu = () => {
  isOpen.value = !isOpen.value;
};

const navigateTo = (path: string) => {
  isOpen.value = false;

  if (path === '/logout') {
    authStore.logout();
    return;
  }

  router.push(path);
};

const handleClickOutside = (event: MouseEvent) => {
  if (menuRef.value && !menuRef.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style>
.v-enter-active,
.v-leave-active {
  transition: opacity 0.15s ease, transform 0.15s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
  transform: translateY(5px);
}
</style>
