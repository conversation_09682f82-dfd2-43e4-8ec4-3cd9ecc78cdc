<template>
  <div class="flex h-full w-fit items-center gap-2 select-none relative">
    <div
      class="group w-8 h-8 rounded-full flex items-center justify-center bg-nio-grey-background"
      :class="disabled ? '' : 'cursor-pointer'"
      @click="subtract"
    >
      <MinusIcon
        :class="[
          '**:fill-nio-grey-500',
          !disabled && 'group-hover:**:fill-nio-black-900'
        ]"
      />
    </div>
    <input
      :id="inputId"
      v-model="modelValue"
      type="number"
      class="block font-medium w-[2cap] text-center outline-none focus:outline-none focus:border-nio-blue-400"
      :class="[
        modelValue === 0 ? 'text-nio-grey-500' : 'text-nio-black-900',
        disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : ''
      ]"
      :disabled="disabled"
      :aria-describedby="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
    >
    <div
      class="group w-8 h-8 rounded-full flex items-center justify-center bg-nio-grey-background"
      :class="disabled ? '' : 'cursor-pointer'"
      @click="add"
    >
      <PlusIcon
        :class="[
          '**:fill-nio-grey-500',
          !disabled && 'group-hover:**:fill-nio-black-900'
        ]"
      />
    </div>
    <div
      v-if="error?.message"
      :id="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-6 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ error.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import MinusIcon from '@/assets/icons/minus-icon.svg';
import PlusIcon from '@/assets/icons/plus-icon-2.svg';
import { watch } from 'vue';
import type { DynamicFormItemData, FormErrors } from '@/common/utils/forms.ts';

const emit = defineEmits(['nio-blur']);
const modelValue = defineModel<number>({ required: true });

type Props = {
  disabled?: boolean,
  error?: FormErrors[0],
  errorPosition?: 'left' | 'right',
  inputId?: string,
  collectionIdentifier?: number,
  inputData: DynamicFormItemData,
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  error: undefined,
  errorPosition: 'left',
  inputId: undefined,
  collectionIdentifier: undefined,
});

const subtract = () => {
  if (props.disabled) {
    return;
  }
  if (modelValue.value <= 0) {
    return;
  }
  modelValue.value -= 1;
};

const add = () => {
  if (props.disabled) {
    return;
  }
  modelValue.value += 1;
};

watch(() => modelValue.value, () => {
  emit('nio-blur');
});
</script>

<style lang="css" scoped>
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}
</style>
