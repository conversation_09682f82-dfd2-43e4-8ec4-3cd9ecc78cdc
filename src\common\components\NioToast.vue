<template>
  <transition name="fade">
    <div
      v-if="toast.visible.value"
      :class="[
        'fixed top-[20px] right-[40px] w-[400px] rounded-[20px] px-[20px] py-[15px] shadow-xl z-9999',
        notificationClass
      ]"
    >
      <div class="flex-1">
        <h3 :class="[titleClass, 'mt-[30px] mx-[30px]']">
          {{ toast.title.value }}
        </h3>
        <p :class="[descriptionClass, 'mt-[10px] mb-[42px] leading-relaxed mx-[30px]']">
          {{ toast.message.value }}
        </p>
      </div>
    </div>
  </transition>

  <!-- Fixné tlačidlo zatvorenia mimo toast -->
  <div
    v-if="toast.visible.value"
    class="fixed top-[10px] right-[10px] w-[30px] h-[30px] flex items-center justify-center rounded-full bg-[rgba(249,249,249,0.60)] border border-[#BBD0FB] cursor-pointer hover:bg-gray-200 z-100"
    @click="toast.hide()"
  >
    <span class="w-full h-full flex items-center justify-center text-[#A1A1A1] hover:text-gray-600">
      &#10005;
    </span>
  </div>
</template>

<script setup lang="ts">
import { toast } from '@/common/utils/NotificationService';
import { computed } from 'vue';

const notificationClass = computed(() => {
  switch (toast.type.value) {
    case 'error':
      return 'bg-nio-red-background border border-[#FCA5A5]';
    case 'success':
      return 'bg-[#D1FAE5] border border-[#A7F3D0]';
    case 'info':
    default:
      return 'bg-[#1E40AF] border border-[#1E3A8A]';
  }
});

const titleClass = computed(() => {
  switch (toast.type.value) {
    case 'error':
      return 'text-[#BC2020] text-[18px] tracking-[0.72px] font-[400]';
    case 'success':
      return 'text-[#131313] text-[18px] tracking-[0.72px] font-[400]';
    case 'info':
    default:
      return 'text-[#FFF] text-[18px] tracking-[0.72px] font-[400]';
  }
});

const descriptionClass = computed(() => {
  switch (toast.type.value) {
    case 'error':
      return 'text-[#000] text-[16px] tracking-[0.32px] font-[400]';
    case 'success':
      return 'text-[#535353] text-[16px] tracking-[0.32px] font-[400]';
    case 'info':
    default:
      return 'text-[#F3F4F4] text-[16px] tracking-[0.32px] font-[400]';
  }
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
