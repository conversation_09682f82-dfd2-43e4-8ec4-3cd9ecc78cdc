<script setup lang="ts">
import type { Company } from '@/modules/tenders/types/tenders-types';
import AboutMatchingPopover from '../AboutMatchingPopover.vue';
import { computed, ref } from 'vue';
import NioScoreDots from '@/common/components/NioScoreDots.vue';
import { tenderScoreToPoints, tenderScoreToText } from '@/common/utils/score';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import NioTag from '@/common/components/NioTag.vue';
import { useTenderStore } from '../../stores/tenders-store';
import { useFormattedRates } from '@/common/composables/useFormattedRates.ts';
import InviteChip from '@/modules/tenders/components/InviteChip.vue';

const { company, invited, index } = defineProps<{ company: Company; tenderId: string; invited?: boolean; index: number }>();

const emit = defineEmits(['invite', 'uninvite']);

const showInviteButtons = computed(() => !window.location.href.includes('/matching/marketplace'));

const formattedRates = useFormattedRates(company.match_details?.matching_rates);

const handleInvite = () => {
  emit('invite', company.id);
};
const handleUninvite = () => {
  emit('uninvite', company.id);
};

const isPopoverOpen = ref(false);

const tenderStore = useTenderStore();
const invitedCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => Boolean(vendor.sent_at)).map(vendor => vendor.id));
const pendingCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => !vendor.sent_at).map(vendor => vendor.id));
const isVendorInvited = computed(() => invitedCompanyIds.value.includes(company.id));
const isVendorPending = computed(() => pendingCompanyIds.value.includes(company.id));
</script>

<template>
  <div v-if="company" class="flex flex-row justify-between min-h-[290px] w-full rounded-20 overflow-hidden">
    <div class="flex flex-col justify-between p-4 bg-[#F2F2F7] w-1/2 ">
      <div class="mb-4">
        <div class="flex gap-1">
          <span class="text-[18px] font-paragraph text-nio-grey-300 leading-normal mr-[5px] mt-[7px]">
            {{ index + 1 }}
          </span>
          <div>
            <InviteChip :status="isVendorInvited ? 'invited' : (isVendorPending ? 'pending' : 'not-invited')" />
            <h3 class="text-[32px] font-paragraph text-black leading-normal flex">
              {{ company.name }}
            </h3>
          </div>
        </div>
        <p class="font-paragraph text-sm leading-normal text-nio-grey-500">
          {{ company.headquarters }}, {{ company.country }}
        </p>
      </div>

      <div v-show="(company.employees?.positions?.length ?? 0) > 0" class="text-sm">
        <p class=" leading-normal font-bold">
          Employee positions
        </p>
        <p>
          {{ company.employees?.positions?.slice(0, 6).join(', ') }}
          <span v-show="(company.employees?.positions?.length ?? 0) > 6">...</span>
        </p>
      </div>

      <div class="flex flex-wrap gap-2 mt-auto">
        <NioTag v-if="company.main_industry" size="sm">
          {{ company.main_industry }}
        </NioTag>
        <NioTag v-if="company.founded_at" size="sm">
          Since {{ company.founded_at }}
        </NioTag>
      </div>
    </div>

    <div class="flex flex-col justify-between p-4 bg-[#E5E5EA] w-1/2 ">
      <NioScoreProgress
        :points="tenderScoreToPoints(company.match_details?.overall_score, 'total')"
        :title="tenderScoreToText(company.match_details?.overall_score)"
        size="lg"
        class="mb-4"
      />

      <div class="bg-nio-white rounded-10 shadow-custom-box-shadow-01 px-4 py-2 text-sm mb-5">
        <div class="grid grid-cols-2 gap-2">
          <span>Technologies</span>
          <NioScoreDots :points="tenderScoreToPoints(company.match_details?.technologies_score, 'technologies')" />

          <span>Domain</span>
          <NioScoreDots :points="tenderScoreToPoints(company.match_details?.projects_score, 'projects')" />

          <span>Location</span>
          <span class="text-nio-green-text text-[12px] font-bold leading-[14px]">{{ company.match_details?.location_match }}</span>

          <span>Rates</span>
          <span>
            {{ formattedRates }}
          </span>
        </div>
      </div>

      <div class="flex flex-row gap-2 mt-auto">
        <button
          :class="[
            'px-4 py-2 bg-nio-blue-800 text-nio-white text-[14px] rounded-20 hover:bg-nio-blue-600-hover transition-colors cursor-pointer',
            showInviteButtons ? 'w-1/2' : 'w-full'
          ]"
          @click="isPopoverOpen = true"
        >
          About Matching
        </button>
        <template v-if="showInviteButtons">
          <button
            v-if="invited"
            class="w-1/2 px-4 py-2 bg-nio-red-500 text-white text-[14px] rounded-20 hover:opacity-90 transition-colors cursor-pointer"
            @click="handleUninvite"
          >
            Cancel invite
          </button>

          <button
            v-else
            class="w-1/2 px-4 py-2 bg-nio-green-text text-white text-[14px] rounded-20 hover:opacity-90 transition-colors cursor-pointer"
            @click="handleInvite"
          >
            Invite
          </button>
        </template>
      </div>
    </div>
    <AboutMatchingPopover v-model="isPopoverOpen" :company="company" />
  </div>
</template>