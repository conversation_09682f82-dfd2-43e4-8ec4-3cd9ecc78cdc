<script setup lang="ts">
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';
import { ref } from 'vue';
import type { Tender } from '../../types/tenders-types';
import TenderSubmissionDateForm from './TenderSubmissionDateForm.vue';

interface Props {
  tenderData?: Tender;
}

defineProps<Props>();

const submitting = ref(false);
const isPopoverOpen = defineModel<boolean>();

const onSubmitSuccess = () => {
  isPopoverOpen.value = false;
};

</script>

<template>
  <DetailPopover01
    v-model="isPopoverOpen"
    width="1100px"
    min-height="auto"
    height="fit-content"
    title="Update Submission Deadline"
  >
    <TenderSubmissionDateForm
      :tender-data="tenderData"
      :submitting="submitting"
      @submit-success="onSubmitSuccess"
    />
  </DetailPopover01>
</template>

<style lang="css" scoped>

</style>
