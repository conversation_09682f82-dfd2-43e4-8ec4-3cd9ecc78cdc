<script lang="ts" setup>
// import AssistantRFPTips from './AssistantRFPTips.vue';
</script>

<template>
  <div>
    <main class="w-full bg-nio-grey-background-30 border border-nio-blue-outline-stroke-400 rounded-30 p-4 space-y-2.5">
      <slot name="header" />

      <section class="bg-nio-grey-background-90 rounded-15 p-4 pb-8 flex flex-col max-h-full h-full">
        <slot />
      </section>
      <!-- <AssistantRFPTips /> -->
    </main>
  </div>
</template>

<style lang="css" scoped>
.custom-shadow-01 {
  box-shadow: 0px 71px 20px 0px rgba(207, 207, 207, 0.00), 0px 45px 18px 0px rgba(207, 207, 207, 0.01), 0px 25px 15px 0px rgba(207, 207, 207, 0.05), 0px 11px 11px 0px rgba(207, 207, 207, 0.09), 0px 3px 6px 0px rgba(207, 207, 207, 0.10);
}
</style>
