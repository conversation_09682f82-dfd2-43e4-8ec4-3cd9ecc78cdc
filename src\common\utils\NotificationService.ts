import { ref } from 'vue';

export type NotificationType = 'error' | 'success' | 'info';

export class NotificationService {
  visible = ref(false);
  title = ref('');
  message = ref('');
  type = ref<NotificationType>('info');

  show(newTitle: string, newMessage: string, newType: NotificationType = 'info') {
    this.title.value = newTitle;
    this.message.value = newMessage;
    this.type.value = newType;
    this.visible.value = true;

    setTimeout(() => {
      this.visible.value = false;
    }, 5000);
  }

  hide() {
    this.visible.value = false;
  }
}

export const toast = new NotificationService();
