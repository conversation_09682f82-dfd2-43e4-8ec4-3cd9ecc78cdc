<template>
  <div
    v-if="show"
    popover
    class="fixed inset-0 flex items-center justify-center z-50 w-screen h-screen bg-transparent"
    @click.self="closeDialog"
  >
    <div
      class="w-[906px] h-[784px] bg-nio-grey-background-60 rounded-30 border border-[#BBD0FB] relative overflow-hidden shadow-complex backdrop-blur-[900px]"
    >
      <div class="flex justify-between items-start m-[70px_90px_70px_90px]">
        <div>
          <h2 class="text-[24px] font-light tracking-[0.96px] text-black">
            {{ $t('companies-dialog-upload.uploadCompanies') }}
          </h2>
          <!-- eslint-disable -->
          <p
              class="text-[20px] font-light tracking-[0.8px] text-nio-grey-500"
              v-html="
              $t('companies-dialog-upload.uploadRange', {
                min: `<span class='text-nio-blue-800'>${vendorLimits.min}</span>`,
                max: `<span class='text-nio-blue-800'>${
                  vendorLimits.max === Infinity ? '∞' : vendorLimits.max
                }</span>`,
              })
            "
          />

          <!-- eslint-enable -->
        </div>
        <div>
          <span class="text-[18px] font-normal text-nio-grey-700 mb-[15px] block">
            {{ $t('companies-dialog-upload.downloadExampleFile') }}
          </span>
          <div class="flex gap-[10px]">
            <a
              href="@/../vendor-upload-example.csv"
              download
              class="w-7 h-9 flex items-center justify-center hover:scale-125 transition-transform ease-out will-change-transform duration-200"
            >
              <csv-file />
            </a>
            <a
              href="@/../vendor-upload-example.xls"
              download
              class="w-7 h-9 flex items-center justify-center hover:scale-125 transition-transform"
            >
              <xls-file />
            </a>
            <a
              href="@/../vendor-upload-example.xlsx"
              download
              class="w-7 h-9 flex items-center justify-center hover:scale-125 transition-transform"
            >
              <xlxs-file />
            </a>
          </div>
        </div>
      </div>
      <div
        :class="[
          'w-[49.125rem] h-[28.0625rem] m-[30px_60px_120px_60px] rounded-10 flex items-center justify-center relative transition-all',
          isDragging ? 'bg-[#D5E0EC]' : 'bg-[rgba(249,249,249,0.90)]'
        ]"
        @dragover.prevent="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
        @click="openFileExplorer"
      >
        <div :class="{'hidden': !isDragging, 'flex flex-col items-center pointer-events-none': isDragging}">
          <upload-plus />
          <p class="text-[18px] text-nio-grey-500">
            {{ $t('companies-dialog-upload.dropYourFile') }}
          </p>
        </div>
        <p
          :class="{'hidden': isDragging, 'block': !isDragging}"
          class="text-[18px] text-nio-grey-500"
        >
          {{ $t('companies-dialog-upload.dragAndDrop') }}
          <span class="bg-nio-blue-800 text-white px-2.5 py-1 rounded-10 cursor-pointer " role="button">
            {{ $t('companies-dialog-upload.browse') }}
          </span>
        </p>
        <input
          ref="fileInput"
          type="file"
          class="hidden"
          accept=".csv, .xls, .xlsx"
          @change="handleFileUpload"
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import xlxsFile from '@/assets/icons/xlsx-file.svg';
import xlsFile from '@/assets/icons/xls-file.svg';
import csvFile from '@/assets/icons/csv-file.svg';
import uploadPlus from '@/assets/icons/upload-plus.svg';
import { maxVendors } from '@/config/intro.ts';

interface Props {
  show: boolean;
}

defineProps<Props>();

const emit = defineEmits<{
  (event: 'update:show', value: boolean): void;
  (event: 'file-uploaded', file: File): void;
}>();

const isDragging = ref<boolean>(false);
const fileInput = ref<HTMLInputElement | null>(null);

const vendorLimits = {
  min: 2,
  max: maxVendors
};

const handleDragOver = (event: DragEvent): void => {
  event.preventDefault();
  isDragging.value = true;
};

const handleDragLeave = (event: DragEvent): void => {
  const currentTarget = event.currentTarget as HTMLElement | null;
  if (currentTarget && !currentTarget.contains(event.relatedTarget as Node)) {
    isDragging.value = false;
  }
};

const handleDrop = (event: DragEvent): void => {
  event.preventDefault();
  isDragging.value = false;

  const file = event.dataTransfer?.files[0];
  if (file && isValidFile(file)) {
    emit('file-uploaded', file);
    closeDialog();
  }
};

const openFileExplorer = (): void => {
  fileInput.value?.click();
};

const handleFileUpload = (): void => {
  const file = fileInput.value?.files?.[0];
  if (file && isValidFile(file)) {
    emit('file-uploaded', file);
    closeDialog();
  }
};

const isValidFile = (file: File): boolean => {
  const validExtensions = ['csv', 'xls', 'xlsx'];
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  return fileExtension !== undefined && validExtensions.includes(fileExtension);
};

const closeDialog = (): void => {
  emit('update:show', false);
};

const handleKeyDown = (event: KeyboardEvent): void => {
  if (event.key === 'Escape') {
    closeDialog();
  }
};

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);
});
</script>
