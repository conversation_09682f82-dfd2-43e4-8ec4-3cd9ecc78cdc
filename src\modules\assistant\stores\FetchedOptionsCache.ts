import type { FormSelectOption } from '@/common/utils/forms';

type OptionMap = Map<string | number, FormSelectOption>;

class FetchedOptionsCache {
  private cache = new Map<string, OptionMap>();

  private getKey(payloadKey: string, endpointUrl?: string): string {
    return `${payloadKey}::${endpointUrl ?? 'static'}`;
  }

  get(payloadKey: string, endpointUrl?: string): OptionMap | undefined {
    return this.cache.get(this.getKey(payloadKey, endpointUrl));
  }

  set(payloadKey: string, endpointUrl: string | undefined, options: OptionMap): void {
    this.cache.set(this.getKey(payloadKey, endpointUrl), options);
  }

  merge(payloadKey: string, endpointUrl: string | undefined, newOptions: OptionMap): void {
    const key = this.getKey(payloadKey, endpointUrl);
    if (!this.cache.has(key)) {
      this.cache.set(key, new Map(newOptions));
    } else {
      const existing = this.cache.get(key)!;
      for (const [k, v] of newOptions.entries()) {
        existing.set(k, v);
      }
    }
  }

  clear(payloadKey: string, endpointUrl?: string): void {
    this.cache.delete(this.getKey(payloadKey, endpointUrl));
  }
}

export const fetchedOptionsCache = new FetchedOptionsCache();
