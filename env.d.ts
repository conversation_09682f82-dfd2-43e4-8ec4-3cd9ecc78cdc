/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_BASE_URL: string;
  readonly VITE_APP_KEY: string;
  readonly VITE_I18N_LOCALE: string;
  readonly VITE_I18N_FALLBACK_LOCALE: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_AUTH_API_URL: string;
  readonly VITE_AUTH_CLIENT_ID: string;
  readonly VITE_AUTH_SCOPE: string;
  readonly NIO_API_GIT_CLONE_DIR?: string;
  readonly NIO_API_GIT_BRANCH?: string;
  readonly VITE_SENTRY_DSN?: string;
  readonly VITE_GTAG_ID?: string;
  readonly VITE_CLARITY_ID?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
