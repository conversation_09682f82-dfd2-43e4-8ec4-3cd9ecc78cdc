<script setup lang="ts">
import { TendersSteps } from '../types/tenders-steps';
import IntroHeaderChip from './TendersHeaderChip.vue';

interface Props {
    currentStep: TendersSteps,
}

defineProps<Props>();
</script>

<template>
  <div class="h-[50px] flex items-center border-b border-nio-grey-300">
    <h3 class="text-[#0071E3] mr-[20px]">
      {{ $t('tenders.intro') }}
    </h3>
    <div class="flex items-center gap-[10px]">
      <IntroHeaderChip
        v-if="currentStep === TendersSteps.MATCHING || currentStep === TendersSteps.MATCHED"
        class="chip"
        :class="currentStep >= TendersSteps.MATCHING ? 'shown' : ''"
        :text="$t('tenders.matching-companies')"
        :is-active="currentStep === TendersSteps.MATCHING || currentStep === TendersSteps.MATCHED"
      />
      <IntroHeaderChip
        v-if="currentStep === TendersSteps.DETAIL"
        class="chip"
        :class="currentStep >= TendersSteps.MATCHING ? 'shown' : ''"
        :text="$t('tenders.tender-details')"
        :is-active="currentStep === TendersSteps.DETAIL"
      />
    </div>
  </div>
</template>

<style lang="css">
.chip {
  display: none;
  opacity: 0;
  transition: opacity 0.4s ease, display 0.2s ease;
  transition-delay: 0.5s;
  &.shown {
    opacity: 1;
    display: block;
  }
}

@starting-style {
  .chip.shown {
    opacity: 0;
  }
}
</style>
