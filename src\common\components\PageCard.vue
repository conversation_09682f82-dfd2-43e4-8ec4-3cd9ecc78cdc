<template>
  <div class="p-5 bg-nio-grey-background rounded-20 flex flex-col overflow-hidden relative">
    <div v-if="title || subtitle" class="mb-4">
      <div class="flex items-center gap-2">
        <h2 v-if="title" class="text-h5 font-normal text-nio-black leading-normal tracking-[0.72px] ">
          {{ title }}
        </h2>
      </div>
      <div v-if="subtitle" class="text-xs font-normal text-nio-grey-700">
        {{ subtitle }}
      </div>
    </div>

    <slot />
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title?: string;
  subtitle?: string;
}>();
</script>
