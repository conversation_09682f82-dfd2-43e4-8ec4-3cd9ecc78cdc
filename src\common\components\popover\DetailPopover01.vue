<template>
  <teleport to="body">
    <transition name="fade-in-x" class="custom-fade-in-duration" appear>
      <div
        v-if="isOpened"
        role="dialog"
        aria-modal="true"
        :aria-label="title"
        class="fixed top-0 left-0 z-40 w-screen h-screen flex justify-center backdrop-css overflow-y-scroll overscroll-contain"
        :class="{
          'items-baseline': align === 'top' || align === 'left' || align === 'right',
          'items-center': align === 'center',
        }"
        @click.self="handleBackdropClick"
      >
        <div
          :style="{
            minHeight: props.minHeight || height,
            ...(props.maxWidth && { maxWidth: props.maxWidth }),
          }"
          class="mt-[4rem] mb-[2rem] mx-[2.5rem] p-4 bg-nio-grey-background rounded-[24px] relative"
          :class="{
            'ml-[2rem] mr-auto': align === 'left',
            'mr-[2rem] ml-auto': align === 'right',
            'mx-auto': align === 'center'
          }"
        >
          <button
            class="absolute -top-[15px] right-[-30px] w-[30px] h-[30px] bg-[rgba(249,249,249,0.60)] border border-nio-blue-400 rounded-full flex items-center justify-center cursor-pointer hover:bg-[#F9F9F9]"
            aria-label="Close"
            @click="isOpened = false;"
          >
            <CloseIcon class="w-[13px] h-[13px]" />
          </button>
          <div v-if="props.title" class="absolute -top-[45px] left-[15px] flex justify-center items-center">
            <div>
              <h2 class="text-xl font-bold text-nio-grey-900 ">
                {{ props.title }}
              </h2>
            </div>
          </div>
          <slot />
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script lang="ts" setup>
import CloseIcon from '@/assets/icons/close-icon.svg';

const isOpened = defineModel<boolean>();

const props = withDefaults(defineProps<{
  height?: string;
  maxWidth?: string;
  title?: string;
  align?: 'top' | 'left' | 'right'| 'center';
  minHeight?: string;
  closeOnBackdropClick?: boolean;
}>(), {
  height: '470px',
  maxWidth: '1100px', // you can use also percentage
  title: undefined,
  align: 'center',
  minHeight: '470px',
  closeOnBackdropClick: true,
});

const height = props.height;

const handleBackdropClick = () => {
  if (props.closeOnBackdropClick) {
    isOpened.value = false;
  }
};
</script>

<style scoped lang="css">
.backdrop-css {
  background: linear-gradient(180deg, rgba(217, 217, 217, 0.15) 0%, rgba(166, 173, 182, 0.15) 100%);
  backdrop-filter: blur(15px);
}

.custom-fade-in-duration {
  --fade-in-x: 90ms;
}
</style>
