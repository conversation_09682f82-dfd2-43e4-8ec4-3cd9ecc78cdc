<script lang="ts" setup>
import {
  type BlurPromise,
  type DynamicFormStructure,
  type FormErrors,
  type FormStandardData,
  FormTypeMap, transformValidationErrorsForStandardForm
} from '@/common/utils/forms.ts';
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';
import { nioAxios } from '@/axios.ts';
import { computed, onMounted, ref } from 'vue';
import AssistantFormButtons from '@/modules/assistant/components/AssistantFormButtons.vue';
import type { AssistantGuideSteps } from '@/modules/assistant/types/assistant-steps.ts';
import FormStandardRoot from '@/common/components/forms/FormStandardRoot.vue';
import { useRouter } from 'vue-router';
import { routeMap } from '@/modules/assistant/routes';
import { useRfpFormCacheStore } from '@/modules/assistant/stores/rfp-form-cache-store.ts';
import { sleep } from '@/common/utils/sleep.ts';
import { saveForm } from '@/modules/assistant/util/assistant-form';
import SourceFilesList from '@/modules/assistant/components/SourceFilesList.vue';
interface Props {
  generalRfpData: AssistantRfpGeneral,
  currentGuideStep: AssistantGuideSteps,
}

const props = defineProps<Props>();
const emit = defineEmits(['nextStep', 'rfpTitleChanged']);
const router = useRouter();

const infoForm = ref<InstanceType<typeof FormStandardRoot>>();
const isError = ref(false);
const formErrors = ref<FormErrors>([]);
const dynamicFormStruct = ref<DynamicFormStructure>();
const rfpFormCacheStore = useRfpFormCacheStore();
const isFormDisabled = computed(() => Boolean(props.generalRfpData.tender_id));

if (!isFormDisabled.value) {
  nioAxios.patch(`/enterprise/assistant/rfp/${props.generalRfpData.id}/step`, {
    step: 'info',
  });
}

const fetchStepData = async() => {
  const cachedItem = rfpFormCacheStore.get(props.generalRfpData.id, 'info');
  if (cachedItem) {
    dynamicFormStruct.value = cachedItem;
    return;
  }
  try {
    const response = await nioAxios.get<DynamicFormStructure>(`/enterprise/assistant/rfp/${props.generalRfpData.id}/info`);
    dynamicFormStruct.value = response.data;
  } catch {
    isError.value = true;
  } finally {
    await sleep(3000);
  }
};

const onFormSubmit = async() => {
  if (isFormDisabled.value) {
    return;
  }
  formErrors.value = [];
  await saveForm({
    rfpId: props.generalRfpData.id,
    formPayload: { info: infoForm.value!.getFormData() },
    onValidationError422: e => {
      formErrors.value = transformValidationErrorsForStandardForm(e, 'info');
    },
    retryWithoutValidation: true,
  });
  if (formErrors.value.length === 0) {
    emit('rfpTitleChanged', infoForm.value!.getFormData()?.title);
    emit('nextStep');
  }
};

const handleSubmitForm = () => {
  infoForm.value?.submit();
};

const onFormBlur = async(blurPromise?: BlurPromise) => {
  formErrors.value = [];
  await saveForm({
    showToast: false,
    rfpId: props.generalRfpData.id,
    formPayload: { info: infoForm.value!.getFormData() },
    onValidationError422: e => {
      formErrors.value = transformValidationErrorsForStandardForm(e, 'info');
    },
    blurPromise,
    retryWithoutValidation: true,
  });
};

const saveForLater = async() => {
  await saveForm({
    shouldValidate: false,
    showToast: false,
    rfpId: props.generalRfpData.id,
    formPayload: { info: infoForm.value!.getFormData() },
  });
  await router.push({ name: routeMap.assistant.name });
};

await fetchStepData();

onMounted(() => {
  emit('rfpTitleChanged', infoForm.value!.getFormData()?.title);
});
</script>

<template>
  <div class="w-full">
    <div v-if="!isError && dynamicFormStruct" class="w-full">
      <div
        class="py-1 px-2 rounded-5 bg-nio-grey-100 backdrop-blur-sm font-medium text-sm -tracking-028px text-nio-blue-800 w-fit mb-8"
      >
        {{ $t('assistant.request-proposal') }}
      </div>
      <header class="flex justify-between items-start mb-11">
        <div>
          <h2 class="text-h28 text-nio-black font-semibold">
            General Information
          </h2>
          <h3 class="text-nio-grey-500 text-h5 font-semibold -tracking-[0.4px] leading-[22px]">
            Lay the foundation - define your project's core details.
          </h3>
        </div>
        <div class="text-h1 font-medium leading-[38px] inline-flex gap-1">
          <span class="text-nio-black-900">1</span>
          <span class="text-nio-grey-500">/</span>
          <span class="text-nio-grey-500">2</span>
        </div>
      </header>
      <SourceFilesList :files="generalRfpData.files" />
      <component
        :is="FormTypeMap[dynamicFormStruct.form]"
        ref="infoForm"
        :form-structure="dynamicFormStruct as DynamicFormStructure<FormStandardData>"
        :disabled="isFormDisabled"
        :errors="formErrors"
        :api-endpoint="`/enterprise/assistant/rfp/${generalRfpData.id}`"
        @form-submit="onFormSubmit"
        @nio-blur="onFormBlur"
      >
        <template #form-bottom>
          <AssistantFormButtons
            :current-guide-step="currentGuideStep"
            :tender-id="generalRfpData.tender_id"
            @save-for-later="saveForLater"
            @submit-form="handleSubmitForm"
          />
        </template>
      </component>
    </div>
    <div v-else>
      ERROR
    </div>
  </div>
</template>
