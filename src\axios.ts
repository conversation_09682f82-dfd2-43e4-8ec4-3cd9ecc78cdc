import axios from 'axios';
import { getAccessToken, getTokenExpiry, refreshAccessToken, startLoginFlow } from '@/modules/auth/facades/token';

export const nioAxios = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
});

nioAxios.interceptors.request.use(
  async config => {
    const expiry = getTokenExpiry();
    const token = getAccessToken();

    // Check if token exists and is still valid
    if (token && expiry && Date.now() < expiry) {
      config.headers.Authorization = `Bearer ${token}`;
    } else if (expiry && Date.now() >= expiry) {
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        const newToken = getAccessToken(); // Get the refreshed token
        if (newToken) {
          config.headers.Authorization = `Bearer ${newToken}`;
        }
      } else {
        startLoginFlow();
      }
    }

    return config;
  },
  error => Promise.reject(error)
);

nioAxios.interceptors.response.use(
  response => response,
  async error => {
    const expiry = getTokenExpiry();
    if (error.response?.status === 401 && (!expiry || (expiry && Date.now() >= expiry))) {
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        const newToken = getAccessToken();
        if (newToken) {
          error.config.headers.Authorization = `Bearer ${newToken}`;
        }
        return nioAxios.request(error.config);
      }
    }

    // TODO: Start login process if needed
    return Promise.reject(error);
  }
);
