import { defineStore } from 'pinia';
import { nioAxios } from '@/axios';
import { toast } from '@/common/utils/NotificationService';

import { computed } from 'vue';
import { maxVendors, uploadingDelayMillis } from '@/config/intro.ts';
import { AxiosError } from 'axios';
import { sleep } from '@/common/utils/sleep.ts';

interface Company {
  id?: string;
  importId: string;
  name: string;
  status?: 'pending' | 'verified' | 'unverified';
  similarVendors?: { name: string, companyId: string }[];
}

interface VendorsState {
  companies: Company[];
  loading: boolean;
  error: string | null;
  isConnected: boolean;
}

type LocalPollingInstance = {
  stop: () => void;
  lastResponse: unknown;
  poll: () => Promise<void>;
};
let pollingInstance: LocalPollingInstance | null = null;

export const useVendorsStore = defineStore('companies', {
  state: (): VendorsState => ({
    companies: [],
    loading: false,
    error: null,
    isConnected: false,
  }),

  getters: {
    filteredVendors: state => {
      return computed(() => {
        const existingCompanyIds = new Set(state.companies.map(v => v.id).filter(id => id !== undefined));

        // Filter alternatives from all companies
        return state.companies.map(company => ({
          ...company,
          similarVendors: company.similarVendors?.filter(alt => !existingCompanyIds.has(alt.companyId)) || [],
        }));
      });
    },
    vendors: state => state.companies
  },

  actions: {
    async uploadVendors(file: File) {
      const baseUrl = import.meta.env.VITE_API_BASE_URL || '';
      const url = `${baseUrl}/enterprise/workspace/companies/import`;

      this.loading = true;
      this.error = null;

      const startTime = performance.now();

      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await nioAxios.post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response.data && response.data.data) {
          const availableSlots = maxVendors - this.companies.length;

          const newVendors = response.data.data
            .slice(0, availableSlots)
            .map((company: any) => ({
              importId: company.import_id,
              name: company.name,
              status: 'pending',
            }));

          this.companies = [...this.companies, ...newVendors];

          if (response.data.data.length > availableSlots) {
            const elapsedTime = performance.now() - startTime;
            const remainingTime = uploadingDelayMillis - elapsedTime;
            const toastParams = [
              'Some companies were ignored!',
              availableSlots > 0 ? `Only ${availableSlots} companies were added due to the max limit of 30.` : 'No companies were added due to the max limit of 30.',
              'error'
            ] as const;

            if (remainingTime > 0) {
              setTimeout(() => {
                toast.show(...toastParams);
              }, remainingTime);
            } else {
              toast.show(...toastParams);
            }
          }
        } else {
          this.error = 'Unexpected response format';
        }
        this.startPollingStatuses();
        return response;
      } catch (err) {
        if (err instanceof AxiosError && err.response?.status === 422) {
          const errorMessage = err.response.data.error?.message || err.response.data.message || 'Unknown validation error';

          toast.show('Upload error', errorMessage, 'error');
          if (this.companies.length < 2) {
            this.companies = [];
          }
          this.error = `Validation error: ${errorMessage}`;
          throw err;
        } else {
          this.error = err instanceof Error ? err.message : 'Unknown error occurred';
        }
      } finally {
        this.loading = false;
      }
    },

    async updateVendor(importId: string, name: string, alternativeCompanyId: string) {
      const url = '/enterprise/workspace/companies/import/update';

      try {
        const vendorIndex = this.companies.findIndex(company => company.importId === importId);
        if (vendorIndex !== -1) {
          const updatedVendor = {
            ...this.companies[vendorIndex],
            id: alternativeCompanyId,
            status: 'pending' as const,
            name: name,
          };

          this.companies = [
            ...this.companies.slice(0, vendorIndex),
            updatedVendor,
            ...this.companies.slice(vendorIndex + 1)
          ];
        } else {
          console.warn(`Company with importId ${importId} not found.`);
        }

        const response = await nioAxios.post(
          url,
          { import_id: importId, name },
        );

        await sleep(500);
        if (pollingInstance && typeof pollingInstance.poll === 'function') {
          await pollingInstance.poll();
        } else {
          this.startPollingStatuses();
        }

        return response.data;
      } catch (error) {
        console.error('Company update error:', error);
        this.error = error instanceof Error ? error.message : 'Unknown error';
        throw error;
      }
    },

    startPollingStatuses() {
      if (pollingInstance) {
        return;
      }
      const poll = async() => {
        const importIds = this.companies.filter(v => v.status === 'pending').map(v => v.importId);
        if (importIds.length === 0) {
          this.stopPollingStatuses();
          return;
        }
        try {
          const { data } = await nioAxios.post('/enterprise/workspace/companies/import/status', { import_ids: importIds });
          if (!Array.isArray(data?.data)) {
            return;
          }
          data.data.forEach((statusUpdate: any) => {
            const idx = this.companies.findIndex(v => v.importId === statusUpdate.import_id);
            if (idx !== -1) {
              this.companies[idx] = {
                ...this.companies[idx],
                status: statusUpdate.status,
                name: statusUpdate.name,
                id: statusUpdate.company_id ?? undefined,
                similarVendors: statusUpdate.alternatives?.map((alt: any) => ({
                  companyId: alt.company_id,
                  name: alt.name,
                })) || [],
              };
            }
          });
          if (!this.companies.some(v => v.status === 'pending')) {
            this.stopPollingStatuses();
          }
        } catch {
          // error handling
        }
      };
      const intervalId = setInterval(poll, 5000);
      pollingInstance = {
        stop: () => {
          clearInterval(intervalId);
          pollingInstance = null;
        },
        lastResponse: null,
        poll,
      };
      poll();
    },

    stopPollingStatuses() {
      if (pollingInstance) {
        pollingInstance.stop();
        pollingInstance = null;
      }
    },

    async addToList() {
      await nioAxios.post('/enterprise/workspace', {
        company_ids: this.filteredVendors.value.map(v => v.id)
      });
    },

    deleteVendor(importId: string) {
      this.companies = this.companies.filter(company => {
        return company.importId !== importId;
      });
    },

    resetStore() {
      this.stopPollingStatuses();
      this.companies = [];
      this.loading = false;
      this.error = null;
      this.isConnected = false;
    }

  },
});
