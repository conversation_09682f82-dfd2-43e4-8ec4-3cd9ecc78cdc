<script setup lang="ts">
import { ref } from 'vue';
import { ConsultantTableType, type CandidateRow } from '../types/consultants-types';
import { getTimeLeft, getTimeUntil } from '@/common/utils/date';
import { routeMap as tendersRouteMap } from '@/modules/tenders/routes';
import NioTableIndexCol from '@/common/components/UI/table/NioTableIndexCol.vue';
import type { NioTableColumn } from '@/common/types/components';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';
import NioInfoBlock from '@/common/components/NioInfoBlock.vue';
import BasicPopover from '@/common/components/popover/BasicPopover.vue';
import ThreeDots from '@/assets/icons/three-dots.svg';
import { useWorkspaceRate } from '@/common/composables/useHourlyRate';
import { useQuestionStore } from '@/modules/companies/stores/question-store';
import type { Candidate } from '@/modules/tenders/types/tenders-types';
import AboutCandidate from '@/modules/tenders/components/tender-candidates/AboutCandidate.vue';
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';

interface Props {
  candidateEntryData: CandidateRow<ConsultantTableType>;
  index: number;
  tableType: ConsultantTableType;
  columns: NioTableColumn[];
}

const props = defineProps<Props>();
const emit = defineEmits(['assignToManager']);
const questionStore = useQuestionStore();
const isPopoverOpen = ref(false);
const candidateProfile = ref<Candidate | null>(null);

function openPopoverAndFetchCandidate() {

  questionStore.getCandidateProfile(props.candidateEntryData.id).then(candidate => {
    candidateProfile.value = candidate ?? null;
  });
  isPopoverOpen.value = true;
}

const { getValidRate } = useWorkspaceRate();
</script>

<template>
  <NioTableRow expandable :columns="columns">
    <template #name>
      <NioTableIndexCol :index="index + 1">
        <span class="truncate line-clamp-1 break-all">
          <span class="hover:underline" @click.stop="openPopoverAndFetchCandidate">{{ candidateEntryData.name }}</span>
        </span>
      </NioTableIndexCol>
    </template>
    <template #profession>
      {{ candidateEntryData.profession }}
    </template>
    <template #rate>
      {{ getValidRate(candidateEntryData.rate) }}
    </template>
    <template #seniority>
      <span class="truncate line-clamp-1 break-all capitalize items-center gap-x-2 flex">
        {{ candidateEntryData.seniority }} <span v-show="candidateEntryData.years_of_experience" class="text-xs bg-transparent border border-nio-grey-700 text-nio-grey-800 font-normal px-1 py-0.5 rounded-5">{{ candidateEntryData.years_of_experience }} years</span>
      </span>
    </template>

    <template #availability>
      <p class="truncate line-clamp-1 break-all">
        <template v-if="tableType === ConsultantTableType.AVAILABLE">
          {{ getTimeUntil(candidateEntryData.bench_specialist!.available_from) }}
        </template>
        <template v-else-if="candidateEntryData.assignment?.end_date">
          {{ getTimeLeft(candidateEntryData.assignment.end_date) }}
        </template>
        <template v-else>
          -
        </template>
      </p>
    </template>

    <template v-if="tableType === ConsultantTableType.ENGAGED" #managed-by>
      <div v-if="candidateEntryData.assignment?.manager">
        <div>{{ candidateEntryData.assignment.manager.name }} {{ candidateEntryData.assignment.manager.surname }}</div>
        <div class="text-xs line-clamp-1 break-all font-light">
          {{ [candidateEntryData.assignment.manager.division, candidateEntryData.assignment.manager.department].filter(Boolean).join(', ') }}
        </div>
      </div>
      <span v-else>
        -
      </span>
    </template>

    <template #actions>
      <div v-if="tableType === ConsultantTableType.ENGAGED" class="ml-auto w-fit h-fit">
        <BasicPopover v-if="true">
          <div role="listbox" class="ml-auto w-[28px] h-[23px] rounded-30 flex items-center justify-center custom-shadow-001 bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer" title="Actions">
            <ThreeDots class="h-4 w-4" />
          </div>
          <template #popover-content>
            <div class="flex flex-col w-[9rem]">
              <div
                class="text-sm justify-center text-center font-medium text-nio-grey-700 py-2 px-1 flex items-center gap-3 hover:bg-nio-grey-100 cursor-pointer rounded-10"
                role="option"
                @click="emit('assignToManager')"
              >
                <div>Assign to manager</div>
              </div>
              <div
                class="text-sm justify-center text-center font-medium text-nio-grey-700 py-2 px-1 flex items-center gap-3 hover:bg-nio-grey-100 cursor-pointer rounded-10"
                role="option"
                @click="openPopoverAndFetchCandidate"
              >
                <div>About Candidate</div>
              </div>
            </div>
          </template>
        </BasicPopover>
      </div>
    </template>

    <template #expand>
      <div class="flex items-center gap-3 px-3 mt-3">
        <NioInfoBlock title="Residence">
          <span class="capitalize">{{ candidateEntryData.country }}</span>
        </NioInfoBlock>
        <NioInfoBlock title="Seniority">
          <span class="capitalize">{{ candidateEntryData.seniority }}</span>
        </NioInfoBlock>
        <NioInfoBlock title="Company">
          <span class="capitalize">
            {{ [candidateEntryData.company.name, candidateEntryData.company.headquarters, candidateEntryData.company.country].join(', ') }}
          </span>
        </NioInfoBlock>

        <NioInfoBlock v-if="tableType === ConsultantTableType.ENGAGED && candidateEntryData.assignment?.tender_position?.name" title="Tender">
          <div class="font-bold capitalize">
            <router-link
              v-if="candidateEntryData.assignment?.tender?.id"
              :to="{name: tendersRouteMap.detail.children.tenderDetail.name, params: {id: candidateEntryData.assignment?.tender.id}}"
              class="hover:text-nio-blue-800 truncate line-clamp-1 break-all"
              :title="candidateEntryData.assignment?.tender.name"
              @click.stop
            >
              {{ candidateEntryData.assignment?.tender.name }}
            </router-link>
          </div>
        </NioInfoBlock>
        <NioInfoBlock v-if="tableType === ConsultantTableType.ENGAGED && candidateEntryData.assignment?.tender_position?.name" title="Tender Position">
          <span class="capitalize">{{ candidateEntryData.assignment?.tender_position.name }}</span>
        </NioInfoBlock>
      </div>
      <DetailPopover01
        v-model="isPopoverOpen"
        align="top"
        position="sticky"
        overflow="auto"
        min-height="470px"
        :title="`About ${candidateEntryData.name}`"
      >
        <Transition
          appear
          enter-active-class="transition-opacity duration-150 ease-in"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
        >
          <LazyLoad>
            <AboutCandidate
              v-if="candidateProfile"
              :candidate="candidateProfile"
            />
          </LazyLoad>
        </Transition>
      </DetailPopover01>
    </template>
  </NioTableRow>
</template>
