<template>
  <div
    class="flex items-center bg-nio-white rounded-full p-[5px] cursor-pointer transition-all duration-300"
  >
    <div
      v-for="(option, index) in options"
      :key="index"
      class="flex items-center justify-center px-3.5 py-1.5  rounded-full text-[16px] font-medium transition-all duration-300 whitespace-nowrap"
      :class="{
        'bg-nio-blue-800 text-nio-white': value === option.value,
        'bg-transparent text-nio-grey-500': value !== option.value,
      }"
      @click="toggleSwitch(option.value)"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  options: {
    type: Array as () => Array<{ label: string; value: string }>,
    default: () => [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
    ],
  },
});

const emit = defineEmits(['update:modelValue']);

const value = ref(props.modelValue);

watch(
  () => props.modelValue,
  newValue => {
    value.value = newValue;
  }
);

const toggleSwitch = (selectedValue: string) => {
  value.value = selectedValue;
  emit('update:modelValue', value.value);
};
</script>
