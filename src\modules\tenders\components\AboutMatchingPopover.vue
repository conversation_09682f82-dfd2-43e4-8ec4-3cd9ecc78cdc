<script setup lang="ts">
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';
import type { Company } from '@/modules/tenders/types/tenders-types';
import LabelBadge from './LabelBadge.vue';
import { CircleCheck, AlertTriangle, CircleX } from 'lucide-vue-next';
// import nordicsLogo from '@/assets/images/logos/nordics_logo.jpg';
// import clutchLogo from '@/assets/images/logos/clutch_logo.jpg';
// import websiteLogo from '@/assets/images/logos/website_logo.jpg';
// import Tooltip from '@/common/components/NioTooltip.vue';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import { tenderScoreToPoints, tenderScoreToText } from '@/common/utils/score';
import { computed } from 'vue';
import { useWorkspaceRate } from '@/common/composables/useHourlyRate';

const isPopoverOpen = defineModel<boolean>();

const { company } = defineProps<{ company: Company }>();

const projectsScore = computed(() => {
  return tenderScoreToPoints(company.match_details?.projects_score, 'projects');
});
const locationScore = computed(() => {
  return tenderScoreToPoints(company.match_details?.location_score, 'location');
});
const projectsScoreDescription = computed(() => {
  const count = (company.match_details?.projects_count ?? 0) > 100 ? '100+' : company.match_details?.projects_count ?? '';
  const plural = (company.match_details?.projects_count ?? 0) === 1 ? 'project' : 'projects';
  switch(projectsScore.value) {
    case 0:
      if (company.match_details?.projects_count && company.match_details?.projects_count > 0) {
        return `${company.name} has worked on ${count} similar ${plural}, but there is no significant overlap.`;
      } else {
        return `${company.name} has not worked on similar projects.`;
      }
    case 1:
      return `${company.name} has worked on ${count} similar ${plural}, but the overlap is limited.`;
    case 2:
      return `${company.name} has experience with ${count} similar ${plural}, indicating good alignment with your tender scope.`;
    case 3:
      return `${company.name} has completed ${count} highly relevant ${plural}, strongly matching your tender's requirements and goals.`;
  }
  return '';
});
const locationScoreDescription = computed(() => {
  const country = company.country ? `${company.country},` : '';
  const headquarters = company.headquarters ? `${company.headquarters},` : '';

  if(country !== '' || headquarters !== '') {
    switch(locationScore.value) {
      case 0:
        return `${company.name} operates in ${headquarters} ${country} which does not match your tender requirements country.`;
      case 1:
        return `${company.name} operates in ${headquarters} ${country} which is in the same economic region as your tender requirements country.`;
      case 2:
        return `${company.name} operates in ${headquarters} ${country} which is in the same continent as your tender requirements country.`;
      case 3:
        return `${company.name} operates in ${headquarters} ${country} which matches your tender requirements country.`;
    }
  }else{
    switch(locationScore.value) {
      case 0:
        return `${company.name} does not match your tender requirements country.`;
      case 1:
        return `${company.name} operates in the same economic region as your tender requirements country.`;
      case 2:
        return `${company.name} operates in the same continent as your tender requirements country.`;
      case 3:
        return `${company.name} matches your tender requirements country.`;
    }
  }

  return '';
});

const { getValidRateRange, getValidRate } = useWorkspaceRate();

const ratesDescription = computed(() => {
  const rates = company.match_details?.matching_rates;
  if (!rates) {
    return `Rate information for ${company.name} is currently not available.`;
  }

  if (typeof rates === 'number') {
    if (rates < 20) {
      return `Rate information for ${company.name} is currently not available.`;
    }
    return `${company.name} charges approximately ${getValidRate(rates)}.`;
  }

  if (typeof rates === 'object' && 'min' in rates && 'max' in rates) {
    const { min, max } = rates;

    if (min < 20) {
      if (max < 20) {
        return `Rate information for ${company.name} is currently not available.`;
      }
      return `${company.name} offers rates less than ${getValidRate(max)}.`;
    }
    if (min === max) {
      return `${company.name} charges approximately ${getValidRate(min)}.`;
    }
    return `${company.name} offers rates between ${getValidRateRange([min, max])}.`;
  }

  return `Rate information for ${company.name} is currently not available.`;
});

const technologies = computed(() => company.match_details?.technologies || []);
const matchedTechnologies = computed(() =>
  technologies.value.filter(t => t.score === 100)
);
const unmatchedTechnologies = computed(() =>
  technologies.value.filter(t => t.score === 0)
);
const alternativeTechnologies = computed(() =>
  technologies.value.filter(t => t.score > 0 && t.score < 100)
);
const alternativeTechnologiesNames = computed(() =>
  alternativeTechnologies.value.map(t => t.name)
);
const alternativeTechnologiesAlternatives = computed(() => {
  const found = alternativeTechnologies.value.flatMap(t => t.found_by);
  // remove duplicates
  return [...new Set(found)];
});

</script>

<template>
  <DetailPopover01
    v-model="isPopoverOpen"
    width="1100px"
    align="top"
    position="sticky"
    overflow="auto"
    min-height="470px"
    :title="`${company.name}: ${tenderScoreToText(company.match_details?.overall_score, 'total')}`"
  >
    <div class="h-full px-4 pb-2">
      <div class="flex flex-wrap justify-center gap-2 mt-5 h-33">
        <NioScoreProgress
          :points="tenderScoreToPoints(company.match_details?.technologies_score, 'technologies')"
          :title="tenderScoreToText(company.match_details?.technologies_score, 'technologies')"
          subtitle="Technologies"
          size="xl"
        />

        <NioScoreProgress
          :points="tenderScoreToPoints(company.match_details?.projects_score, 'projects')"
          :title="tenderScoreToText(company.match_details?.projects_score, 'projects')"
          subtitle="Domain"
          size="xl"
        />

        <NioScoreProgress
          :points="tenderScoreToPoints(company.match_details?.location_score, 'location')"
          :title="company.match_details?.location_match"
          subtitle="Location"
          size="xl"
        />
      </div>

      <hr class=" border-nio-grey-100">

      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 ">
          Technologies
        </h4>

        <div class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] ">
          <!-- Matched technologies -->
          <div class="flex flex-wrap gap-2 justify-center mb-3">
            <LabelBadge
              v-for="tech in matchedTechnologies"
              :key="tech.name"
              type="success"
              size="sm"
            >
              <span class="flex items-center gap-0.5">{{ tech.name }}</span>
            </LabelBadge>

            <LabelBadge
              v-for="tech in unmatchedTechnologies"
              :key="tech.name"
              type="danger"
              size="sm"
            >
              <span class="flex items-center gap-0.5 line-through">{{ tech.name }}</span>
            </LabelBadge>
          </div>

          <!-- Alternative solution for technologies -->
          <p
            v-if="alternativeTechnologies.length > 0 && alternativeTechnologiesAlternatives.length > 0"
            class="text-[14px] text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-3 text-center"
          >
            <AlertTriangle class="text-yellow-500 inline w-4 h-4 mr-1" />
            {{ company.name }} doesn't use
            <strong>{{ alternativeTechnologiesNames.join(', ') }}</strong>,
            but have relevant experience with similar technologies such as
            <strong>{{ alternativeTechnologiesAlternatives.join(', ') }}</strong>.
          </p>
        </div>
      </div>

      <hr class=" border-nio-grey-100">

      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 ">
          Domain Expertise
        </h4>

        <p class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px]">
          <CircleCheck v-if="projectsScore === 3" class="text-nio-green-text inline w-4 h-4" />
          <CircleCheck v-else-if="projectsScore === 2" class="text-yellow-500 inline w-4 h-4" />
          <AlertTriangle v-else-if="projectsScore === 1" class="text-yellow-500 inline w-4 h-4" />
          <CircleX v-else class="text-red-500 inline w-4 h-4" />
          {{ projectsScoreDescription }}
        </p>

        <!-- TODO: fill these from endpoint -->
        <!-- <div class="flex items-center gap-2 justify-center text-sm text-nio-grey-700 mt-3">
          <span class="flex items-center gap-0.5">
            <Tooltip text="www.random.website">
              <span class="rounded-full inline-block overflow-hidden bg-nio-grey-background border-1 border-nio-grey-background w-[25px] h-[25px]">
                <img lass="w-full h-auto object-cover" :src="websiteLogo">
              </span>
            </Tooltip>
            <Tooltip text="www.clutch.co">
              <span class="rounded-full inline-block overflow-hidden ml-[-10px] border-1 bg-nio-grey-background border-nio-grey-background w-[25px] h-[25px]">
                <img lass="w-full h-auto object-cover" :src="clutchLogo" alt="clutch">
              </span>
            </Tooltip>
            <Tooltip text="www.nordics.io">
              <span class="rounded-full inline-block overflow-hidden ml-[-10px] border-1 bg-nio-grey-background border-nio-grey-background w-[25px] h-[25px]">
                <img class="w-full h-auto object-cover" :src="nordicsLogo" alt="nordics">
              </span>
            </Tooltip>
          </span>
          Sources
        </div> -->
      </div>

      <hr class=" border-nio-grey-100">

      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 ">
          Location
        </h4>

        <p class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-4">
          <CircleCheck v-if="locationScore === 3" class="text-nio-green-text inline w-4 h-4" />
          <CircleCheck v-else-if="locationScore === 2" class="text-nio-green-text inline w-4 h-4" />
          <AlertTriangle v-else-if="locationScore === 1" class="text-yellow-500 inline w-4 h-4" />
          <CircleX v-else class="text-red-500 inline w-4 h-4" />
          {{ locationScoreDescription }}
        </p>
      </div>

      <hr class=" border-nio-grey-100">

      <div class="mt-5 mb-5">
        <h4 class="text-[22px] text-center font-paragraph text-black leading-normal mb-3 ">
          Rates
        </h4>

        <p class="text-[14px] text-center font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] mb-4">
          {{ ratesDescription }}
        </p>
      </div>
    </div>
  </DetailPopover01>
</template>
