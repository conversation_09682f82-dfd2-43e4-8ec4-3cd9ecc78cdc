import axios from 'axios';
import { calculatePKCECodeChallenge, generateRandomCodeVerifier } from 'oauth4webapi';

const authEndpoint = `${import.meta.env.VITE_AUTH_API_URL}/authorize`;
const tokenEndpoint = `${import.meta.env.VITE_AUTH_API_URL}/token`;
const clientId = import.meta.env.VITE_AUTH_CLIENT_ID;
const redirectUri = `${window.location.origin}/authorize`;

// Token management methods
export const getAccessToken = () => localStorage.getItem('access_token');
export const getRefreshToken = () => localStorage.getItem('refresh_token');
export const getTokenExpiry = () => {
  const expiry = localStorage.getItem('token_expiry');
  return expiry ? parseInt(expiry, 10) : null;
};

export const setTokens = (accessToken: string, refreshToken: string | null, expiresIn: number) => {
  localStorage.setItem('access_token', accessToken);
  if (refreshToken) {
    localStorage.setItem('refresh_token', refreshToken);
  }
  const expiryTime = Date.now() + expiresIn * 1000;
  localStorage.setItem('token_expiry', expiryTime.toString());
};

export const clearTokens = () => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('token_expiry');
};

// OAuth flow methods
export const startLoginFlow = async() => {
  try {
    const codeVerifier = generateRandomCodeVerifier();
    const codeChallenge = await calculatePKCECodeChallenge(codeVerifier);
    localStorage.setItem('code_verifier', codeVerifier);
    if (window.location.pathname !== '/') {
      sessionStorage.setItem('redirect_url', window.location.href);
    }
    window.location.href = `${authEndpoint}?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${import.meta.env.VITE_AUTH_SCOPE}&code_challenge=${codeChallenge}&code_challenge_method=S256&prompt=login`;
  } catch (error: any) {
    console.error('Error starting login flow:', error.message);
  }
};

export const exchangeCodeForToken = async() => {
  const urlParams = new URLSearchParams(window.location.search);
  const authorizationCode = urlParams.get('code');
  const codeVerifier = localStorage.getItem('code_verifier');
  if (!codeVerifier) {
    throw new Error('Code verifier is missing from localStorage');
  }

  const response = await axios.post<TokenResponse>(tokenEndpoint, {
    grant_type: 'authorization_code',
    client_id: clientId,
    redirect_uri: redirectUri,
    code: authorizationCode,
    code_verifier: codeVerifier,
  });
  localStorage.removeItem('code_verifier');
  setTokens(response.data.access_token, response.data.refresh_token ?? null, response.data.expires_in!);
};

export const refreshAccessToken = async() => {
  const refreshToken = getRefreshToken();
  if (!refreshToken) {
    clearTokens();
    window.location.href = '/'; // Redirect to login
    return false;
  }

  try {
    const response = await axios.post<TokenResponse>(tokenEndpoint, {
      grant_type: 'refresh_token',
      client_id: clientId,
      refresh_token: refreshToken,
    });

    setTokens(response.data.access_token, response.data.refresh_token ?? null, response.data.expires_in!);
    return true;
  } catch (error: any) {
    console.error('Error refreshing token:', error.response?.data || error.message);
    clearTokens();
    window.location.href = '/'; // Redirect to login
    return false;
  }
};

interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in?: number;
}
