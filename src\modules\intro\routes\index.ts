import type { NIORouteMap } from '@/router';

export const routeMap = {
  intro: {
    path: '/intro',
    name: 'Intro',
    meta: {
      i18nTitle: 'Intro',
      authNotRequired: true,
      headTitlePrefix: 'Intro',
    },
  },
} satisfies NIORouteMap;

// `routeIndex` s komponentami pre tento modul
export const routeIndex = {
  intro: {
    ...routeMap.intro,
    component: () => import('../pages/IntroPage.vue'),
  },
};

// Export ako array pre Vue Router
export default Object.values(routeIndex);
