<script setup lang="ts">
import { menuLinks, customSubMenuItems, type CustomSubMenuItem } from '@/config/menu';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const links = computed(() => {
  const custom = customSubMenuItems.find(menu => menu.matchedRoute === route.matched[0]?.name);
  if (custom?.subMenuItems && custom.subMenuItems.length > 0) {
    return custom;
  }
  const currentMenu = menuLinks.find(menu => menu.routeName === route.matched[0]?.name);
  return currentMenu;
});
</script>

<template>
  <nav v-if="links?.subMenuItems && links.subMenuItems.length > 0">
    <ul class="flex items-center gap-4 w-full justify-center flex-wrap mb-8">
      <li v-for="item in links.subMenuItems" :key="item.routeName">
        <router-link
          :to="item.routeName ? { name: item.routeName } : {...route}"
          class="text-h3 font-semibold text-nio-grays-gray-2 hover:text-nio-labels-primary/30 p-2"
          :class="item.routeName === route.name || ((links as CustomSubMenuItem).matchedRoute === route.name && item.actAsActive) ? 'text-nio-labels-primary!' : ''"
        >
          {{ item.title }}
        </router-link>
      </li>
    </ul>
  </nav>
</template>

<style lang="css" scoped>

</style>
