@import './fonts.css' layer(base);

@import 'tailwindcss';
@plugin 'tailwindcss-primeui';

@theme {
  --breakpoint-m-tablet: 744px;
  --breakpoint-l-desktop: 1036px;
  --breakpoint-x-desktop: 1440px;
  --breakpoint-xl-desktop: 1983px;
  --breakpoint-2xl: 1536px;

  --background-image-nio-gradient-background: var(--nio-gradient-background,
      linear-gradient(180deg, #d9d9d9 0%, #a6adb6 100%));
  --background-image-nio-gradient-blue: var(--Gradient,
      linear-gradient(87deg, #0071e3 0%, #bbd0fb 102.5%));
  --background-image-nio-metch-gradient: linear-gradient(187deg,
      #c5bac0 5.16%,
      #876f7c 59.68%,
      #79637f 77.85%,
      #6c5683 96.03%);

  --color-nio-grey-background: #f9f9f9;
  --color-nio-grey-background-15: #f9f9f926;
  --color-nio-grey-background-30: #f9f9f94d;
  --color-nio-grey-background-60: #f9f9f999;
  --color-nio-grey-background-90: #f9f9f9e5;
  --color-nio-red-background: #f2d2d3;
  --color-nio-border-default: #E1E1E2;
  --color-nio-black-900: #131313;
  --color-nio-white: #ffffff;
  --color-nio-black: #000000;
  --color-nio-blue-100: #f2f6fe;
  --color-nio-blue-200: #e5ecfe;
  --color-nio-blue-300: #d9e3fd;
  --color-nio-blue-400: #bbd0fb;
  --color-nio-blue-500: #5598FF;
  --color-nio-blue-600-hover: #348de9;
  --color-nio-blue-800: #0071e3;
  --color-nio-blue-800-10: #D5E0EC;
  --color-nio-blue-outline-stroke-400: #bbd0fb;
  --color-nio-green-bg: #c8f3d6;
  --color-nio-green-text: #5da773;
  --color-nio-grey-hover-100: #dcdcdc;
  --color-nio-grey-000: #f3f4f4;
  --color-nio-grey-100: #e8e8e8;
  --color-nio-grey-200: #d0d0d0;
  --color-nio-grey-300: #b9b9b9;
  --color-nio-grey-400: #a1a1a1;
  --color-nio-grey-500: #8a8a8a;
  --color-nio-grey-700: #535353;
  --color-nio-grey-900: #292929;
  --color-nio-red-500: #bc2020;
  --color-nio-text-white: #D9D9D9;
  --color-nio-grey-3: #C7C7CC;
  --color-nio-grey-4: #3B3B3B;
  --color-nio-grey-5: #2E2E2E;
  --color-nio-grey-6: #787880;
  --color-nio-grey-7: #434242;
  --color-nio-grey-8: #3D3D3E;
  --color-nio-grey-9: #414141;
  --color-nio-grey-10: #8E8E93;
  --color-nio-grey-11: #E5E5EA;
  --color-nio-grey-12: #D1D1D6;
  --color-nio-labels-primary: #000;
  --color-nio-grays-gray-2: #AEAEB2;

  --font-sans: Inter, Roboto, sans-serif;

  --text-h1: 32px;
  --text-h2: 26px;
  --text-h3: 24px;
  --text-h4: 22px;
  --text-h5: 20px;
  --text-p-m: 12px;
  --text-p-l: 14px;
  --text-p-x: 16px;
  --text-p-xl: 18px;
  --text-btt-s: 16px;
  --text-btt-m: 20px;

  --font-weight-heading: 700;
  --font-weight-paragraph: 400;

  --leading-heading: 1.2;
  --leading-paragraph: 1.6;

  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  --radius-5: 5px;
  --radius-10: 10px;
  --radius-15: 15px;
  --radius-20: 20px;
  --radius-25: 25px;
  --radius-30: 30px;
  --radius-45: 45px;
  --radius-50: 50px;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

.-tracking-028px {
  @apply tracking-[-0.28px];
}

.text-h28 {
  @apply text-[28px];
}
