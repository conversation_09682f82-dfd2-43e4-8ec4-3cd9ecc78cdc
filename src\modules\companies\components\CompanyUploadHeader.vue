<script setup lang="ts">
import AnimatedTextSlot from '@/common/components/AnimatedTextSlot.vue';

</script>

<template>
  <div class="flex items-center justify-between">
    <div>
      <h3 class="text-nio-black leading-8 tracking-[-0.48px] text-h3">
        {{ $t('companies.title') }}
      </h3>
      <AnimatedTextSlot class="custom-color text-nio-grey-500 text-h5 tracking-[-0.4px] leading-6">
        {{ $t('companies.subtitle') }}
      </AnimatedTextSlot>
    </div>
  </div>
</template>

<style lang="css" scoped>
.custom-color {
  --blur-anim-color: --nio-grey-500;
}
</style>
