<template>
  <div class="spinner" />
</template>

<style scoped>
.spinner {
  width: 18px;
  height: 18px;
  --c: radial-gradient(farthest-side,#8A8A8A 92%,#0000);
  background:
    var(--c) 50% 0,
    var(--c) 50% 100%,
    var(--c) 100% 50%,
    var(--c) 0 50%;
  background-size: 4.5px 4.5px;
  background-repeat: no-repeat;
  animation: spinner-kh173p 1s ease-in-out infinite;
  transform-origin: center;

  &.light {
    --c: radial-gradient(farthest-side,#ffffff 92%,#0000);
  }
}

@keyframes spinner-kh173p {
  to {
    transform: rotate(0.5turn);
  }
}
</style>
