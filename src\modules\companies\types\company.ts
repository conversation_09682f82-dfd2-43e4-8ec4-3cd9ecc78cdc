import { CompanyCategory, CompanyStatus } from '@/modules/auth/types/auth-types';

export type Company = {
  id: string;
  name: string;
  region: string;
  hq?: string;
  country?: string;
  technologies?: string[];
  contact_email?: string;
  contact_phone?: string;
  headquarters?: string;
  main_industry?: string;
  founded_at?: number;
  industries?: string[];
  created_at?: string;
  about?: string;
  image?: string;
  cover?: string;
  logo?: string;
  category: CompanyCategory;
  status: CompanyStatus;
  employees_count?: number;
};

export type Vendor = Company & {
  linkedin?: string;
  clients?: [];
  employees?: {
    employees: number;
    developers: number;
    positions: [];
  };
  rates?: {
    juniors: number;
    mediors: number;
    seniors: number;
    leads: number;
  };
};