<template>
  <div class="w-full">
    <div class="w-full flex items-center text-center justify-between text-xs text-nio-grey-500">
      <div>
        {{ getValidRate(props.lowValue) }}
      </div>
      <div>
        {{ getValidRate(props.highValue) }}
      </div>
    </div>
    <div class="relative w-full h-2.5 flex items-center *:h-full">
      <div class="bg-nio-green-text rounded-l-full flex-1" />
      <div class="bg-nio-grey-12 flex-1" />
      <div class="bg-red-100 rounded-r-full flex-1" />

      <!-- Dot -->
      <div
        class="absolute top-1/2 min-w-3 min-h-3 w-3 h-3 -translate-y-1/2 rounded-full shadow-[0_0_0px_2px_#fff]"
        :class="verdictProps.dotColor"
        :style="{ left: dotPosition + '%' }"
      />
    </div>
    <div :class="verdictProps.textColor" class="w-full text-center text-sm mt-1">
      <div>
        {{ getValidRate(props.rate) }} | {{ verdictProps.verdictText }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useWorkspaceRate } from '../composables/useHourlyRate';

const props = defineProps<{
  rate: number
  lowValue: number
  highValue: number
}>();

const { getValidRate } = useWorkspaceRate();

const dotPosition = computed(() => {
  if (props.highValue === props.lowValue) {
    return 0;
  }

  const raw =
    ((props.rate - props.lowValue) /
      (props.highValue - props.lowValue)) *
    100;

  return Math.min(100, Math.max(0, raw));
});

const verdictProps = computed(() => {
  if (props.highValue === props.lowValue) {
    return {
      dotColor: 'bg-nio-green-text',
      textColor: 'text-nio-green-text',
      verdictText: 'Average',
    };
  }

  const span = props.highValue - props.lowValue;
  const normalized = Math.min(
    1,
    Math.max(0, (props.rate - props.lowValue) / span)
  );

  if (normalized < 1 / 3) {
    return {
      dotColor: 'bg-nio-green-text',
      textColor: 'text-nio-green-text',
      verdictText: 'Below budget',
    };
  } else if (normalized < 2 / 3) {
    return {
      dotColor: 'bg-nio-grey-12',
      textColor: 'text-nio-grey-400',
      verdictText: 'Average',
    };
  } else {
    return {
      dotColor: 'bg-nio-red-500',
      textColor: 'text-nio-red-500',
      verdictText: 'Above budget',
    };
  }
});
</script>
