<script setup lang="ts">
import { ref } from 'vue';
import CloseIcon2 from '@/assets/icons/close-icon-2.svg';

type Props = {
  modalTitle: string,
  modalDescription: string,
  confirmButtonText: string,
}

defineProps<Props>();
const dialog = ref<HTMLDialogElement>();
const emit = defineEmits(['confirmed', 'cancelled']);

const showModal = () => {
  dialog.value?.showModal();
};

const closeModal = () => {
  dialog.value?.close();
};

const onConfirmClick = () => {
  emit('confirmed');
  closeModal();
};

const onCancelClick = () => {
  emit('cancelled');
  closeModal();
};

const onMouseDown = (e: Event) => {
  if (e.target === dialog.value) {
    dialog.value.close();
  }
};

defineExpose({
  show: showModal,
  close: closeModal,
});
</script>

<template>
  <dialog
    ref="dialog"
    class="confirmation-dialog border border-nio-blue-400 overflow-visible mx-auto relative bg-nio-grey-background rounded-[24px]"
    @close="closeModal"
    @click="onMouseDown"
  >
    <div class="px-5 py-5 flex flex-col ">
      <h3 class="text-h3 mb-[15px]">
        {{ modalTitle }}
      </h3>
      <div class="text-h5 text-nio-grey-500 ">
        {{ modalDescription }}
      </div>
      <hr class="mt-4 mb-4">
      <div class="mb-0 mt-auto flex justify-between items-center gap-[10px]">
        <button
          class="text-p-xl px-[30px] py-[10px] rounded-15 border-nio-grey-200 border-[2px] text-black hover:bg-black hover:text-white hover:border-black cursor-pointer"
          @click="onCancelClick"
        >
          {{ $t('misc.cancel') }}
        </button>
        <button
          class="text-p-xl px-[30px] py-[10px] rounded-15 border-nio-red-500 hover:bg-nio-red-500 text-nio-red-500 border-[2px] hover:text-white cursor-pointer"
          @click="onConfirmClick"
        >
          {{ confirmButtonText }}
        </button>
      </div>
    </div>
    <div
      type="button"
      class="cursor-pointer flex items-center justify-center absolute -translate-y-1/2 top-0 -right-[30px] bg-[rgba(249,249,249,0.60)] border border-nio-blue-400 rounded-full w-[30px] h-[30px] hover:bg-[#F9F9F9]"
      @click="closeModal"
    >
      <CloseIcon2 class="" />
    </div>
  </dialog>
</template>

<style lang="css">
.confirmation-dialog {
  opacity: 0;
  transition: all 0.2s allow-discrete;
  backdrop-filter: blur(450px);
  box-shadow: 0px 149px 42px 0px rgba(222, 222, 222, 0.00), 0px 95px 38px 0px rgba(222, 222, 222, 0.01), 0px 54px 32px 0px rgba(222, 222, 222, 0.05), 0px 24px 24px 0px rgba(222, 222, 222, 0.09), 0px 6px 13px 0px rgba(222, 222, 222, 0.10);

  &[open] {
    opacity: 1;
  }

  @starting-style {
    &[open] {
      opacity: 0;
    }
  }

  &::backdrop {
    background-color: rgb(0 0 0 / 0%);
    transition: all 0.2s allow-discrete;
  }

  &[open]::backdrop {
    background: linear-gradient(180deg, rgba(217, 217, 217, 0.90) 0%, rgba(206, 203, 206, 0.90) 50%, rgba(166, 173, 182, 0.90) 100%);
    backdrop-filter: blur(45px);
  }

  @starting-style {
    &[open]::backdrop {
      background-color: rgb(0 0 0 / 0%);
    }
  }
}

</style>
