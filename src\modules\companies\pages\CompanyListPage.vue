<script setup lang="ts">
import { ref, watch } from 'vue';
import CompanyList from '@/modules/companies/components/CompanyList.vue';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import CompanyListSkeleton from '@/modules/companies/components/skeletons/CompanyListSkeleton.vue';
import CompanyListTableSkeleton from '@/modules/companies/components/skeletons/CompanyListTableSkeleton.vue';
import CompanyFilter from '@/modules/companies/components/CompanyFilter.vue';
import { useVendors } from '@/modules/companies/composables/useVendors';

const isList = ref(localStorage.getItem('isList') === 'true');
const {
  companies,
  filters,
  page,
  meta,
  filterComponents,
  lazyLoad,
  isLoadingFirstTime
} = useVendors(!isList.value);

watch(isList, newValue => {
  lazyLoad.value = !newValue;
  localStorage.setItem('isList', newValue.toString());
});

</script>

<template>
  <PageContainerWrapper transparent>
    <!-- Vendors Filter -->
    <div class="mb-5">
      <CompanyFilter
        v-model:filters="filters"
        v-model:list-view="isList"
        :filter-components="filterComponents"
      />
    </div>

    <!-- Vendors List -->
    <div class="relative">
      <div v-if="isLoadingFirstTime" class="absolute top-0 left-0 w-full h-full">
        <CompanyListTableSkeleton v-if="isList" />
        <CompanyListSkeleton v-else />
      </div>
      <div v-else>
        <CompanyList
          v-model:page="page"
          :companies="companies"
          :meta="meta"
          :list-view="isList"
        />
      </div>
    </div>
    <router-view />
  </PageContainerWrapper>
</template>

