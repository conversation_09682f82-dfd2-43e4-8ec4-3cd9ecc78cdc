<script setup lang="ts">
import NioFilterField from '@/common/components/UI/filter/NioFilterField.vue';
import type { FilterComponent } from '@/common/types/filter';

defineProps<{
  filterComponents: FilterComponent[];
}>();
const filters = defineModel<any>('filters');
</script>

<template>
  <div class="flex gap-2 flex-wrap items-center">
    <div class="flex flex-wrap gap-3">
      <NioFilterField
        v-for="filter in filterComponents"
        :key="filter.name"
        v-model="filters[filter.component.payload_key]"
        :filter="filter"
      />
    </div>
  </div>
</template>
