<script lang="ts" setup>
interface Props {
    text: string,
    isActive?: boolean
}

withDefaults(defineProps<Props>(), {
  isActive: false,
});
</script>

<template>
  <div
    class="flex items-center justify-center text-center px-[10px] py-[5px] rounded-10"
    :class="[isActive ? 'text-[#0071E3] bg-[rgba(249,_249,_249,_0.90)]' : 'text-[#8A8A8A] bg-[rgba(249,_249,_249,_0.30)]']"
  >
    {{ text }}
  </div>
</template>
