import { createRouter, createWebHistory } from 'vue-router';
import { mergeDeep } from '@/common/utils/objects';
import Home from '@/views/Home.vue';
import { routeMap as introRouteMap } from '@/modules/intro/routes';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import { routeMap as authRouteMap } from '@/modules/auth/routes';

export type NIORouteMap = {
  [key: string]: {
    path: string;
    name: string;
    meta: {
      i18nTitle: string;
      authNotRequired?: boolean;
      headTitlePrefix?: string;
    };
    children?: NIORouteMap;
  };
};

const globalRouteMap = {};

function loadModuleRoutes() {
  const modules = import.meta.glob('@/modules/**/routes/index.ts', { eager: true });
  const routeIndexArray: Array<any> = [];

  for (const path in modules) {
    const module: any = modules[path];
    if (module.routeMap) {
      mergeDeep(globalRouteMap, module.routeMap);
    }
    routeIndexArray.push(...module.default);
  }

  return routeIndexArray;
}

function initializeRoutes() {
  const routesArray = loadModuleRoutes();
  const routes = routesArray.map(route => {
    if (route.children) {
      route.children = Object.values(route.children);
    }
    return route;
  });

  routes.push({
    path: '/',
    name: 'Home',
    component: Home,
  });

  return routes;
}

const routes = initializeRoutes();

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

router.addRoute({
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: () => import('@/views/NotFound.vue'),
});

router.beforeEach((to, from, next) => {
  if ([introRouteMap.intro.name, authRouteMap.authorize.name].includes(to.name as string)) {
    return next();
  }
  const authStore = useAuthStore();
  if (!authStore.userProfile?.workspaces?.length) {
    return next({ name: introRouteMap.intro.name });
  }
  return next();
});

export default router;
