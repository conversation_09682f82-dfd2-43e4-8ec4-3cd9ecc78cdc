<template>
  <div class="relative">
    <span class="absolute inset-y-0 left-2 flex items-center text-gray-500 cursor-default">
      <MagnifyingGlassIcon class="w-4 h-4" />
    </span>
    <input
      v-model="modelValue"
      type="text"
      :disabled="disabled"
      :placeholder="placeholder"
      class="w-full h-[29px] pl-8 pr-2 py-2 bg-nio-grey-000 rounded-md focus:ring-2 focus:ring-nio-blue-400
             text-nio-grey-500 text-[14px] font-medium leading-normal tracking-[-0.28px] outline-hidden"
      :class="disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : ''"
    >
  </div>
</template>

<script setup lang="ts">
import MagnifyingGlassIcon from '@/assets/icons/magnifying-glass-icon.svg';

const modelValue = defineModel<string>();
withDefaults(defineProps<{
  placeholder?: string,
  disabled?: boolean
}>(), {
  placeholder: undefined,
  disabled: false
});
</script>
