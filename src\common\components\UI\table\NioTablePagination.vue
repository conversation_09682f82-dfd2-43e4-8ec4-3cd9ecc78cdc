<script setup lang="ts">
import NioPagination from '@/common/components/NioPagination.vue';
import type { Meta } from '@/common/types/data-fetcher';

const page = defineModel<number>('page', { required: true });
defineProps<{
  meta: Meta;
}>();
</script>

<template>
  <div class="w-full flex justify-center text-center ">
    <div class="mt-auto mb-0 mx-auto nio-pagination">
      <NioPagination
        v-model="page"
        :items-length="meta.total"
        :items-per-page="meta.per_page"
      />
    </div>
  </div>
</template>