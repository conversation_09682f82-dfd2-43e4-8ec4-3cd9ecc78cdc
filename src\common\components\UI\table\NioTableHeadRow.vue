<script setup lang="ts">
import type { NioTableColumn } from '@/common/types/components';

defineProps<{
  columns: NioTableColumn[],
}>();
</script>

<template>
  <div class="contents items-center">
    <template v-for="col in columns" :key="col.key">
      <div
        :class="[
          'py-sm px-4 text-left text-md font-paragraph text-nio-grey-10',
          'text-' + (col.align ?? 'left')
        ]"
      >
        {{ col.label }}
      </div>
    </template>
  </div>
</template>

