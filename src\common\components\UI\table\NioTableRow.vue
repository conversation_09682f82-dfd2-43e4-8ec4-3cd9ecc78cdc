<script setup lang="ts">
import { computed, ref } from 'vue';
import type { NioTableColumn } from '@/common/types/components';

const props = defineProps<{
  columns: NioTableColumn[],
  expandable?: boolean,
}>();

const isExpanded = ref(false);

const columnsCount = computed(() => props.columns.length);

</script>

<template>
  <div
    class="group contents items-center relative"
    @click="expandable && (isExpanded = !isExpanded)"
  >
    <template v-for="col in columns" :key="col.key">
      <div
        :class="[
          {'group-hover:bg-[#F2F2F7] group-hover:shadow-[0px_2px_2px_-1px_#e8e8ec] cursor-pointer': expandable},
          'flex flex-col justify-center px-4 py-2 text-[18px] leading-normal font-paragraph text-nio-grey-900 border-b border-nio-grey-100 transition-all duration-100',
          `text-${col.align ?? 'left'}`,
          {'bg-[#F2F2F7] border-none': expandable && isExpanded}
        ]"
      >
        <slot :name="col.key" />
      </div>
    </template>
    <div
      v-show="expandable && isExpanded"
      class="pb-2 bg-[#F2F2F7] border-b border-nio-grey-100"
      :style="{gridColumn: `span ${columnsCount}`}"
    >
      <Transition appear>
        <div v-show="expandable && isExpanded">
          <slot name="expand" />
        </div>
      </Transition>
    </div>
  </div>
</template>