import { type Locator, type Page } from '@playwright/test';
import { AssistantBaseStep } from './AssistantBaseStep';

export class AssistantFinalStep extends AssistantBaseStep {
  readonly heading: Locator;
  readonly createTenderButton: Locator;
  readonly openTenderLink: Locator;

  constructor(page: Page, rfpTitle: string) {
    super(page);
    this.heading = page.getByRole('heading', { name: rfpTitle });
    this.createTenderButton = page.getByRole('button', { name: 'Create New Tender' });
    this.openTenderLink = page.getByRole('link', { name: 'Open Tender' });
  }
}
