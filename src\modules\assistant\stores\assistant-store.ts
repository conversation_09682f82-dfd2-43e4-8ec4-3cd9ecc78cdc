import { defineStore } from 'pinia';
import { nioAxios } from '@/axios.ts';
import type { AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';

export const useAssistantStore = defineStore('assistant-store', () => {

  const storeRfp = async(text: string, files: File[] = [], vendorId?: number[]): Promise<AssistantRfpGeneral> => {
    try {
      const formData = new FormData();
      formData.append('text', text);

      for (const file of files) {
        formData.append('file[]', file);
      }

      const response = await nioAxios.post<{ data: AssistantRfpGeneral }>(
        '/enterprise/assistant/rfp',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Accept': 'application/json',
          },
        }
      );

      return response.data.data;
    } catch (error) {
      console.error(`Error saving RFP with vendor ${vendorId}:`, error);
      throw error;
    }
  };

  return { storeRfp };
});
