<template>
  <div
    :class="hoverScaleActive ? 'hover-active' : ''"
    class="intro-card relative will-change-transform cursor-pointer w-[255px] card h-[315px] bg-[rgba(249,249,249,0.60)] hover:bg-nio-grey-background-90 transition-all duration-300 ease-in-out"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  hoverScaleActive?: boolean
}

withDefaults(defineProps<Props>(), {
  hoverScaleActive: true
});
</script>
