<template>
  <div class="p-4 rounded-30 border border-nio-blue-400 bg-nio-grey-background custom-shadow">
    <div class="flex flex-wrap">
      <div
        v-for="(category, index) in technologies"
        :key="index"
        class="min-h-[214px]"
        :class="{
          'flex-[1_1_calc(50%-10px)]': index === technologies.length - 1 && technologies.length % 4 === 3,
          'flex-[1_1_calc(25%-10px)] ': technologies.length % 4 !== 3 || index !== technologies.length - 1,
        }"
      >
        <div class="p-[15px] bg-white rounded-15 shadow flex flex-col justify-between relative h-full custom-shadow">
          <div class="absolute top-[15px] left-[15px] px-2 py-1 text-[14px] font-normal text-nio-grey-700 border border-nio-grey-200 rounded-30 leading-4">
            {{ category.name }}
          </div>
          <div class="flex-grow mt-[50px]" />
          <div class="flex flex-wrap-reverse">
            <div
              v-for="(item, idx) in category.items.slice().reverse()"
              :key="idx"
              class="px-3 py-1 text-[16px] font-normal text-nio-black-900 rounded-5 bg-nio-grey-100 leading-4"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface TechnologyCategory {
  name: string;
  items: string[];
}

const { technologies } = defineProps<{
  technologies: TechnologyCategory[];
}>();
</script>

<style scoped>
.flex {
  gap: 10px;
}
.custom-shadow {
  box-shadow: 0px 71px 20px 0px rgba(207, 207, 207, 0.00),
  0px 45px 18px 0px rgba(207, 207, 207, 0.01),
  0px 25px 15px 0px rgba(207, 207, 207, 0.05),
  0px 11px 11px 0px rgba(207, 207, 207, 0.09),
  0px 3px 6px 0px rgba(207, 207, 207, 0.10);
}
</style>
