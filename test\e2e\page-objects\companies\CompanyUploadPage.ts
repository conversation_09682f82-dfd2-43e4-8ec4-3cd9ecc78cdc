import { Locator, Page } from '@playwright/test';
import * as path from 'node:path';
import { BasePage } from '../BasePage';

export class CompanyUploadPage extends BasePage {
  public readonly uploadCard: Locator;
  public readonly filledUploadCard: Locator;
  public readonly searchCompaniesCard: Locator;
  public readonly browseButton: Locator;
  public readonly searchBox: Locator;
  public readonly allVerifiedCompanies: Locator;
  public readonly searchResetButton: Locator;
  public readonly searchCloseButton: Locator;
  public readonly addToListButton: Locator;

  constructor(page: Page) {
    super(page);
    this.uploadCard = this.page.getByRole('button', { name: 'Upload Vendors' });
    this.filledUploadCard = this.page.getByRole('button', { name: 'csv, 0.1 KB companies-ignac-bajza.csv' });
    this.searchCompaniesCard = this.page.getByRole('button', { name: 'Search Vendors' });
    this.browseButton = this.page.getByRole('button', { name: 'browse' });
    this.searchBox = this.page.getByRole('searchbox', { name: 'Search...' });
    this.allVerifiedCompanies = this.page.getByRole('listitem').filter({ hasText: 'Verified' });
    this.searchResetButton = this.page.getByRole('button').getByLabel('Reset');
    this.searchCloseButton = this.page.getByRole('button').getByLabel('Close search');
    this.addToListButton = this.page.getByRole('button').getByLabel('Add to list');
  }

  async uploadFile(filePath) {
    const fileChooserPromise = this.page.waitForEvent('filechooser');
    await this.browseButton.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(path.join(filePath));
  }
}
