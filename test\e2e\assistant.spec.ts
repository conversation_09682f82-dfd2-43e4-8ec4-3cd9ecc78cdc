import * as fs from 'node:fs';
import { expect, test } from '@playwright/test';
import { AssistantStep1 } from './page-objects/assistant/AssistantStep1';
import { AssistantPosition } from './page-objects/assistant/AssistantPosition';
import { AssistantStep2 } from './page-objects/assistant/AssistantStep2';
import { AssistantStart } from './page-objects/assistant/AssistantStart';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { AssistantFinalStep } from './page-objects/assistant/AssistantFinalStep';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const sampleProjectFilePath = path.join(__dirname, 'fixtures', 'project.txt');
const projectDescriptionFilePath = path.join(__dirname, 'fixtures', 'project.txt');
const RFP_STEP_TIMEOUT = 70000;
const TENDER_TIMEOUT = 30000;
let createdRfpTitle: string;

function formatDate(date: Date) {
  return date.toISOString().split('T')[0];
}

test('create RFP', async({ page }) => {
  test.slow();
  const projectDescription = await fs.promises.readFile(sampleProjectFilePath, 'utf-8');

  // Assistant start page.
  const assistantStart = new AssistantStart(page);
  await assistantStart.goto();
  await assistantStart.textInsertButton.click();
  await assistantStart.textInput.fill(projectDescription);
  await assistantStart.createButton.click();

  // Step 1.
  const step1 = new AssistantStep1(page);

  // Step 1 - preconditions.
  await expect(step1.spinner).toBeVisible();
  await expect(step1.heading).toBeVisible({ timeout: RFP_STEP_TIMEOUT });
  const generatedRfpTitle = await step1.rfpTitleInput.inputValue();
  if (generatedRfpTitle) {
    await expect(page).toHaveTitle(`${generatedRfpTitle} | Assistant | Nordics Platform`);
  } else {
    await expect(page).toHaveTitle('New RFP | Assistant | Nordics Platform');
  }

  // Step 1 - actions.
  const now = new Date();
  const nowPlus30Days = new Date(now);
  nowPlus30Days.setDate(now.getDate() + 30);
  const nowPlus90Days = new Date(now);
  nowPlus90Days.setDate(now.getDate() + 90);
  createdRfpTitle = (generatedRfpTitle || 'Project') + ' ' + now.toISOString();
  await step1.rfpTitleInput.fill(createdRfpTitle);
  await step1.selectMultiOption(step1.locationSelect, step1.locationOptions, ['Albania']);
  await step1.startDateInput.fill(formatDate(nowPlus30Days));
  await step1.endDateInput.fill(formatDate(nowPlus90Days));
  await step1.selectSingleOption(step1.timezoneSelect, 'UTC');
  await step1.selectSingleOption(step1.primaryIndustrySelect, 'Banking & Financial Services');

  // Step 1 - assertions.
  await expect(step1.rfpTitleInput).toHaveValue(createdRfpTitle);
  await expect(step1.getFormCellByLabel('Preferred Vendor Location')).toContainText('Albania');
  await expect(step1.getFormCellByLabel('Time Zone')).toContainText('UTC');
  await expect(step1.getFormCellByLabel('Project Industry')).toContainText('Banking & Financial Services');
  await expect(step1.descriptionEditButton).toHaveText('Edit');
  await expect(page.getByRole('dialog')).not.toBeVisible();
  await step1.descriptionEditButton.click();
  await expect(page.getByRole('dialog')).toBeVisible();
  await expect(page.getByRole('dialog').getByRole('textbox')).not.toBeEmpty();
  await page.getByRole('button', { name: 'Save', exact: true }).click();
  await expect(page.getByRole('dialog')).not.toBeVisible();

  await step1.nextButton.click();

  // Step 2.
  const step2 = new AssistantStep2(page);

  // Step 2 - preconditions.
  await expect(step2.spinner).toBeVisible();
  await expect(step2.heading).toBeVisible({ timeout: RFP_STEP_TIMEOUT });
  const positionCount = await step2.getPositionCount();
  expect(positionCount).toBeGreaterThan(0);
  await expect(page).toHaveTitle(`${createdRfpTitle} | Assistant | Nordics Platform`);

  // Remove all positions except the first one.
  for (let i = 1; i < positionCount; i++) {
    // Keep removing 2nd position (with index 1).
    await step2.deletePosition(1);
  }

  await expect(step2.positionHeaders).toHaveCount(1);

  // Position 1 - preconditions.
  const position1 = new AssistantPosition(page, 0);
  await expect(position1.getFormCellByLabel('Languages')).toContainText('English');

  // Position 1 - actions.
  await position1.fill({
    title: 'Software Developer',
    numberOfResourcesIncrease: 2,
    seniority: 'medior',
    workload: 0.5,
    languages: ['English', 'French', 'Spanish'],
    rateExpectations: [0.2, 0.5], // Fill via slider.
    technologies: ['.NET Assemblies', '.NET Development'],
    mandatoryTechnologies: '.NET Development',
    keyTools: ['1Password'],
    mandatoryTools: '1Password'
  });

  // Position 1 - assertions.
  await expect(position1.resourcesNumberInput).toHaveValue('2');
  await expect(position1.getFormCellByLabel('Seniority Level')).toContainText('medior');
  await expect(position1.getFormCellByLabel('Seniority Level')).not.toContainText('junior');
  // Account for drag and drop offset.
  const workloadValue1 = await position1.workloadInput.inputValue();
  expect(parseInt(workloadValue1)).toBeGreaterThan(40);
  expect(parseInt(workloadValue1)).toBeLessThan(60);
  await expect(position1.getFormCellByLabel('Languages')).toContainText('3 items selected');
  // Account for drag and drop offset.
  const rateMinValue1 = await position1.rateMinInput.inputValue();
  expect(parseInt(rateMinValue1)).toBeGreaterThan(50);
  expect(parseInt(rateMinValue1)).toBeLessThan(70);
  // Account for drag and drop offset.
  const rateMaxValue1 = await position1.rateMaxInput.inputValue();
  expect(parseInt(rateMaxValue1)).toBeGreaterThan(140);
  expect(parseInt(rateMaxValue1)).toBeLessThan(160);

  await expect(position1.getFormCellByLabel('Technology Stack')).toContainText('2 items selected');
  await expect(position1.getFormCellByLabel('Must-have Technology')).toContainText('.NET Development');
  await expect(position1.getFormCellByLabel('Key Tools')).toContainText('1Password');

  await step2.addPosition(0);
  await expect(step2.positionHeaders).toHaveCount(2);

  // Position 2 - preconditions.
  const position2 = new AssistantPosition(page, 1);
  await expect(position2.getFormCellByLabel('Languages')).toContainText('English');

  // Position 2 - actions.
  await position2.fill({
    title: 'AI Engineer',
    seniority: 'senior',
    languages: ['Spanish'],
    rateExpectationsInputs: [80, 200], // Fill via inputs.
    technologies: ['.NET Development', 'Agentic AI'],
    mandatoryTechnologies: 'Agentic AI',
    keyTools: ['AI Testing'],
    mandatoryTools: 'AI Testing',
  });

  // Position 2 - Collapse.
  await position2.collapseButton.click();
  // await expect(position2.jobTitleInput).toBeVisible({ timeout: 10000 });
  await expect(page.getByTestId('collapsed-section-1')).toBeVisible();
  await expect(page.getByTestId('expanded-section-1')).not.toBeVisible();
  await position2.collapseButton.click();

  // Position 2 - assertions.
  await expect(position2.resourcesNumberInput).toHaveValue('1');
  await expect(position2.getFormCellByLabel('Seniority Level')).toContainText('senior');
  await expect(position2.workloadInput).toHaveValue('100');
  await expect(position2.getFormCellByLabel('Languages')).toContainText('Spanish');
  await expect(position2.rateMinInput).toHaveValue('80');
  await expect(position2.rateMaxInput).toHaveValue('200');

  await expect(position2.getFormCellByLabel('Technology Stack')).toContainText('2 items selected');
  await expect(position2.getFormCellByLabel('Must-have Technology')).toContainText('Agentic AI');
  await expect(position2.getFormCellByLabel('Key Tools')).toContainText('AI Testing');

  await step2.createRfpButton.click();

  // Final step.
  const finalStep = new AssistantFinalStep(page, createdRfpTitle);

  // Final step - preconditions.
  await expect(finalStep.heading).toBeVisible({ timeout: RFP_STEP_TIMEOUT });
  await expect(finalStep.createTenderButton).toBeVisible();

  await page.getByRole('button', { name: 'Collapse' }).nth(0).click();

  const inputs = page.getByTestId('collapsed-section-0').locator('input');

  const inputCount = await inputs.count();
  expect(inputCount).toBeGreaterThan(0); // Ensure inputs are present

  for (let i = 0; i < inputCount; i++) {
    const el = inputs.nth(i);
    await expect(el).toBeDisabled(); // Check if the input is disabled
  }

  // Save for later.
  await finalStep.saveForLaterButton.click();
  await expect(page).toHaveURL(assistantStart.urlPath);
  await expect(assistantStart.textInsertButton).toBeVisible();
});

// TODO(Marian Rusnak): Fix once https://nordics.atlassian.net/browse/PLAT-862 is fixed.
test.fixme('RFP -> Save to tenders', async({ page }) => {
  expect(createdRfpTitle, 'createdRfpTitle must be set, run RFP creation test first').toBeDefined();
  await page.goto('/assistant');
  await page.getByRole('link', { name: 'History' }).click();
  await page.getByRole('link', { name: createdRfpTitle }).click();
  const finalStep = new AssistantFinalStep(page, createdRfpTitle);
  await expect(finalStep.createTenderButton).toBeVisible();
  await finalStep.createTenderButton.click();

  await expect(page).toHaveTitle('Tenders | Nordics Platform', { timeout: TENDER_TIMEOUT });
  await expect(page.getByRole('heading', { name: createdRfpTitle })).toBeVisible();
});

// TODO(Marian Rusnak): Fix once https://nordics.atlassian.net/browse/PLAT-862 is fixed.
test.fixme('RFP -> Open tender', async({ page }) => {
  expect(createdRfpTitle, 'createdRfpTitle must be set, run RFP creation test first').toBeDefined();
  await page.goto('/assistant');
  await page.getByRole('link', { name: 'History' }).click();
  await page.getByRole('link', { name: createdRfpTitle }).click();
  const finalStep = new AssistantFinalStep(page, createdRfpTitle);
  await expect(finalStep.openTenderLink).toBeVisible();
  await finalStep.openTenderLink.click();

  await expect(page).toHaveTitle('Tenders | Nordics Platform', { timeout: TENDER_TIMEOUT });
  await expect(page.getByRole('heading', { name: createdRfpTitle })).toBeVisible();
});

test('Errors step 1', async({ page }) => {
  const originalTitle = 'TaskFlow: Advanced Task Management Software (currently at step info)';
  const projectDescriptionMock = await fs.promises.readFile(projectDescriptionFilePath, 'utf-8');
  const now = new Date();
  const nowPlus30Days = new Date(now);
  nowPlus30Days.setDate(now.getDate() + 30);
  const nowPlus90Days = new Date(now);
  nowPlus90Days.setDate(now.getDate() + 90);
  const nowSub90Days = new Date(now);
  nowSub90Days.setDate(now.getDate() - 90);

  await page.goto('/assistant');
  await page.getByRole('link', { name: 'History' }).click();
  await page.getByRole('link', { name: originalTitle }).click();

  const step1 = new AssistantStep1(page);
  await expect(step1.heading).toBeVisible({ timeout: RFP_STEP_TIMEOUT });

  // RFP Title
  await step1.rfpTitleInput.fill('');
  await step1.heading.click();
  await step1.expectErrorFor(step1.rfpTitleInput);
  await step1.rfpTitleInput.fill(originalTitle);

  // Vendor Location
  await step1.deselectAll(step1.locationSelect, step1.locationOptions);
  await step1.expectErrorFor(step1.locationSelect, { visible: false });
  await step1.selectMultiOption(step1.locationSelect, step1.locationOptions, ['Andorra']);

  // Expected start & end
  await step1.startDateInput.fill(formatDate(nowPlus30Days));
  await step1.endDateInput.fill(formatDate(nowSub90Days));
  await step1.expectErrorFor(step1.endDateInput);
  await step1.startDateInput.fill(formatDate(nowPlus30Days));
  await step1.endDateInput.fill(formatDate(nowPlus90Days));

  // Industry
  await step1.expectErrorFor(step1.primaryIndustrySelect);

  // Test description error
  await step1.descriptionEditButton.click();
  const originalContent = await page.getByRole('dialog').getByRole('textbox').inputValue() ?? projectDescriptionMock;
  const textareaElement = await page.getByRole('dialog').getByRole('textbox');
  await textareaElement.fill('');
  await page.getByRole('button', { name: 'Save', exact: true }).click();
  await step1.expectErrorFor(textareaElement);
  await page.getByRole('dialog').getByRole('button', { name: 'Close' }).click();
  await step1.expectErrorFor(step1.descriptionEditButton);

  // Test error notification when clicking next
  await step1.nextButton.click();
  await expect(page.getByRole('heading', { name: 'Upload error' })).toBeVisible({ timeout: 3000 });

  // Fill description back
  await step1.descriptionEditButton.click();
  await textareaElement.fill(originalContent as string);
  await page.getByRole('button', { name: 'Save', exact: true }).click();
  await step1.saveForLaterButton.click();
});

test('Errors step 2', async({ page }) => {
  const originalTitle = 'TaskFlow: Advanced Task Management Software (currently at step resources)';

  await page.goto('/assistant');
  await page.getByRole('link', { name: 'History' }).click();
  await page.getByRole('link', { name: originalTitle }).click();

  const step2 = new AssistantStep2(page);

  // Step 2 - preconditions.
  await expect(step2.heading).toBeVisible({ timeout: RFP_STEP_TIMEOUT });
  const positionCount = await step2.getPositionCount();

  await step2.addPosition(positionCount - 1);

  // Position 2 - actions.
  const position2 = new AssistantPosition(page, positionCount);
  position2.resourcesNumberInput.fill('0');

  await step2.expectErrorFor(position2.jobTitleInput);
  await step2.expectErrorFor(position2.resourcesNumberInput);
  await step2.expectErrorFor(position2.senioritySelect);
  await step2.expectErrorFor(position2.technologiesSelect);
  await step2.expectErrorFor(position2.mandatoryTechnologiesSelect, { visible: false });
  await step2.expectErrorFor(position2.toolsSelect, { visible: false });

  // Test error notification when saving RFP
  await step2.createRfpButton.click();
  await expect(page.getByRole('heading', { name: 'Upload error' })).toBeVisible();

  await position2.removePosition();
});

