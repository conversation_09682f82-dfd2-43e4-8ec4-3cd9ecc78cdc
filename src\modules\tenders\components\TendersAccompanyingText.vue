<template>
  <div class="intro-text-container">
    <transition
      mode="out-in"
      name="slide-up-04"
      :duration="400"
      appear
    >
      <div
        v-if="currentStep === TendersSteps.TENDERS"
        class="step-text mb-[92.5px]"
      >
        <h3
          ref="stepTitle"
          class="step-title text-h3"
        >
          {{ $t('tenders.intro-title') }}
        </h3>
        <div
          ref="stepDesc"
          class="step-desc text-[#8A8A8A] text-[20px] rounded"
        >
          {{
            $t('tenders.intro-desc')
          }}
        </div>
      </div>
      <div
        v-else-if="currentStep === TendersSteps.MATCHING"
        class="step-text mb-[92.5px]"
      >
        <h3
          ref="stepTitle"
          class="step-title text-h3"
        >
          {{ $t('tenders.in-progress') }}
        </h3>
        <div
          ref="stepDesc"
          class="step-desc text-[#8A8A8A] text-[20px] rounded"
        >
          {{
            $t('tenders.in-progress-desc')
          }}
        </div>
      </div>
      <div
        v-else-if="currentStep === TendersSteps.MATCHED && Number.isInteger(top) && Number.isInteger(fit)"
        class="step-text mb-[92.5px]"
      >
        <h3
          ref="stepTitle"
          class="step-title text-h3"
        >
          <span v-if="top! > 0">{{ $t('tenders.great-news') }}</span>
          <span v-else>{{ $t('tenders.oops') }}</span>
        </h3>
        <div
          v-if="top! > 0"
          ref="stepDesc"
          class="step-desc text-[#8A8A8A] text-[20px]"
        >
          <span class="!text-nio-blue-800">{{ top }}</span>
          {{ $t('tenders.pre-match-texts.x_0+_part_1') }}
          <span class="!text-nio-blue-800">{{ fit }}</span>
          {{ $t('tenders.pre-match-texts.x_0+_part_2') }}
        </div>
        <div
          v-else-if="fit! > 0"
          ref="stepDesc"
          class="step-desc text-[#8A8A8A] text-[20px]"
        >
          {{ $t('tenders.pre-match-texts.0_x_part_1') }}
          <span class="!text-nio-blue-800">{{ fit }}</span>
          {{ $t('tenders.pre-match-texts.0_x_part_2') }}
        </div>
        <div
          v-else
          ref="stepDesc"
          class="step-desc text-[#8A8A8A] text-[20px]"
        >
          {{ $t('tenders.pre-match-texts.0_0') }}
        </div>
      </div>
      <div
        v-else
        class="step-text mb-[92.5px]"
      >
        <h3
          ref="stepTitle"
          class="step-title text-h3"
        >
          {{ $t('tenders.intro-title') }}
        </h3>
        <div
          ref="stepDesc"
          class="step-desc text-[#8A8A8A] text-[20px] rounded"
        >
          {{
            $t('tenders.intro-desc')
          }}
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { TendersSteps } from '@/modules/tenders/types/tenders-steps';
import { onWatcherCleanup, ref, watch } from 'vue';
import { transformText } from '@/common/utils/text';

interface Props {
  currentStep: TendersSteps,
  top?: number,
  fit?: number,
}

defineProps<Props>();

const stepTitle = ref<HTMLHeadingElement>();
const stepDesc = ref<HTMLDivElement>();

watch(stepTitle, newVal => {
  if (newVal) {
    transformText(newVal, 1.4);
  }
});

watch(stepDesc, newVal => {
  let timeout: ReturnType<typeof setTimeout>;
  if (newVal) {
    timeout = setTimeout(() => {
      transformText(newVal, 1.4);
    }, 1000);
  }
  onWatcherCleanup(() => {
    clearTimeout(timeout);
  });
});

</script>

<style lang="css" scoped>
.step-desc {
  --blur-anim-color: #8A8A8A;
  opacity: 0;
}

.intro-text-container:has(.step-desc.blur-anim) {
  .step-desc {
    opacity: 1;
  }
}
</style>
