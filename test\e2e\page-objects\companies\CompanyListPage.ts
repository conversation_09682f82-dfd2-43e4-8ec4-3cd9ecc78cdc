import { Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class CompanyListPage extends BasePage {
  public readonly vendorCards;
  public readonly uploadButton;
  public readonly firstHeading;
  public readonly tableViewButton;
  public readonly cardViewButton;
  public readonly tableRows;
  public readonly nioPagination;
  public readonly loadMoreButton;
  public readonly nioFilters;

  constructor(page: Page) {
    super(page);
    this.vendorCards = this.page.getByTestId('company-card');
    this.uploadButton = this.page.getByRole('link', { name: 'upload' });
    this.firstHeading = this.page.getByRole('heading').first();
    this.tableViewButton = this.page.getByTestId('nio-switch-right');
    this.cardViewButton = this.page.getByTestId('nio-switch-left');
    this.tableRows = this.page.getByTestId('vendor-table-row');
    this.nioPagination = this.page.getByTestId('nio-pagination');
    this.loadMoreButton = this.page.getByTestId('load-more-button');
    this.nioFilters = this.page.getByTestId('nio-filter-field');
  }

  async goto() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Vendor Hub' }).click();
    await this.page.waitForURL('/companies');
  }
}
