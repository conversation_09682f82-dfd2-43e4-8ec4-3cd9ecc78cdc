<script lang="ts" setup>
import {
  type BlurPromise,
  type DynamicFormItemData,
  type DynamicFormStructure,
  type FormCollectionData,
  FormComponentMap, type FormErrors,
  FormReactiveTypeMap,
  type FormSelectOption,
  type SuggestedResourcesObject
} from '@/common/utils/forms.ts';
import { ref, unref, onMounted } from 'vue';
import ChevronTopIcon from '@/assets/icons/chevron-top.svg';
import ThreeDots from '@/assets/icons/three-dots.svg';
import BasicPopover from '@/common/components/popover/BasicPopover.vue';
import DuplicateIcon from '@/assets/icons/duplicate-icon.svg';
import DeleteIcon from '@/assets/icons/x-icon.svg';
import PlusIcon2 from '@/assets/icons/plus-icon-2.svg';
import NioInput from '@/common/components/UI/NioInput.vue';
import { isEmptyArray } from '@/common/utils/arrays';
import { toast } from '@/common/utils/NotificationService';
import LoadingText from '@/modules/assistant/components/LoadingText.vue';

type Props = {
  formStructure: DynamicFormStructure<FormCollectionData>,
  errors: Record<number, FormErrors>,
  errorPosition?: 'left' | 'right',
  defaultCollapse?: boolean,
  disabled?: boolean,
  suggestedData?: SuggestedResourcesObject,
}

const props = withDefaults(defineProps<Props>(), {
  errorPosition: 'left',
  defaultCollapse: false,
  disabled: false,
  suggestedData: undefined,
});
const emit = defineEmits(['form-submit', 'nio-blur']);

const collection = ref([...props.formStructure.data]);
const reactiveValues = ref<(Record<'id' | 'body', any> & { suggested?: boolean; suggestedIndex?: number })[]>([]);
const componentKey = ref(0);
const dropdownOptions = ref<Record<number, Record<string, FormSelectOption[]>>>({});

const collapsed = ref<boolean[]>([]);

onMounted(() => {
  collapsed.value = collection.value.map(() => props.defaultCollapse);
});

const toggleExpand = (idx: number) => {
  collapsed.value[idx] = !collapsed.value[idx];
};

const initReactiveObject = () => {
  props.formStructure.data.forEach(item => {
    const reactiveItem = {
      id: item.id,
      body: {} as Record<string, any>,
    };
    item.body.forEach(bodyItem => {
      if (!FormReactiveTypeMap[bodyItem.component.id]) {
        return;
      }
      reactiveItem.body[bodyItem.component.payload_key] = (isEmptyArray(bodyItem.value) ? undefined : bodyItem.value) ?? bodyItem.component.default_value ?? FormReactiveTypeMap[bodyItem.component.id](bodyItem.component);
    });
    reactiveValues.value.push(reactiveItem);
  });
};

const addToCollection = (afterIdx: number, duplicate = false) => {
  const newItem = props.formStructure.data[0];
  const newReactiveItem = {
    id: null,
    body: {} as Record<string, any>,
  };
  if (duplicate) {
    newReactiveItem.body = JSON.parse(JSON.stringify(reactiveValues.value[afterIdx].body));
  } else {
    newItem.body.forEach(bodyItem => {
      if (!FormReactiveTypeMap[bodyItem.component.id]) {
        return;
      }
      newReactiveItem.body[bodyItem.component.payload_key] = bodyItem.component.default_value ?? FormReactiveTypeMap[bodyItem.component.id]();
    });
  }
  collection.value.splice(afterIdx + 1, 0, newItem);
  reactiveValues.value.splice(afterIdx + 1, 0, newReactiveItem);
  collapsed.value.splice(afterIdx + 1, 0, false);
  componentKey.value++;
  emit('nio-blur');
};

const deleteCollection = (idx: number) => {
  if (collection.value.length > 1) {
    collection.value.splice(idx, 1);
    reactiveValues.value.splice(idx, 1);
    componentKey.value++;
    emit('nio-blur');
  }
};

const onSubmit = () => {
  emit('form-submit', unref(reactiveValues).map(item => ({
    id: item.id,
    ...item.body,
  })));
};

const setDropdownOptions = (bodyIndex: number, payloadId: string, options: FormSelectOption[]) => {
  dropdownOptions.value ??= {};
  dropdownOptions.value[bodyIndex] ??= {};
  dropdownOptions.value[bodyIndex][payloadId] = options;
};

defineExpose({
  getFormData: () => unref(reactiveValues).map(item => ({
    id: item.id,
    ...item.body,
  })),
  submit: onSubmit,
});

initReactiveObject();

const emitNioBlur = async(blurPromise?: BlurPromise) => {
  if (props.disabled) {
    return;
  }
  emit('nio-blur', blurPromise);
};

const filterInheritedOptionsFn = (structureIdx: number, payloadKey: string) => {
  return (item: FormSelectOption) => {
    return (reactiveValues.value[structureIdx].body[payloadKey] as (string | number)[]).includes(item.value);
  };
};

const computeInheritedOptions = (idx: number, input: DynamicFormItemData) => {
  const allComponentOptions = dropdownOptions.value[idx]?.[input.component.options_from!.payload_key] ?? [];
  const foundInheritedOptionsBase = allComponentOptions.filter(filterInheritedOptionsFn(idx, input.component.options_from!.payload_key)) ?? [];
  return foundInheritedOptionsBase;
};

const addSuggestedItemToCollection = (suggestedIndex: number) => {
  const suggestedItem = props.suggestedData!.data[suggestedIndex];
  const newItem = { ...props.formStructure.data[0] };

  const newReactiveItem = {
    id: null,
    suggested: true,
    suggestedIndex,
    body: {} as Record<string, any>,
  };

  // Set suggested item values by looping through all properties
  Object.entries(suggestedItem).forEach(([key, value]) => {
    const field = newItem.body.find(field => field.component.payload_key === key);
    if (field) {
      if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && 'id' in value[0]) {
        newReactiveItem.body[key] = value.map(item => item.id);

        // For select/multi-select fields, populate dropdown options in the field component
        if (field.component.id === 'select' || field.component.id === 'multi-select') {
          const options = value.map(item => ({
            label: item.name,
            value: item.id
          }));
          field.component.options = [...(field.component.options || []), ...options];
          field.component.selected_options = [...(field.component.selected_options || []), ...options];
        }
      } else {
        newReactiveItem.body[key] = value;
      }
    }
  });

  newItem.body.forEach(bodyItem => {
    if (!Object.hasOwn(newReactiveItem.body, bodyItem.component.payload_key)) {
      if (!FormReactiveTypeMap[bodyItem.component.id]) {
        return;
      }
      newReactiveItem.body[bodyItem.component.payload_key] = bodyItem.component.default_value ?? FormReactiveTypeMap[bodyItem.component.id]();
    }
  });

  // Add to collection at the end
  collection.value.push(newItem);
  reactiveValues.value.push(newReactiveItem);
  collapsed.value.push(false);
  componentKey.value++;
  emit('nio-blur');
  toast.show('Item added', 'Suggested item was successfully added to the collection.', 'success');
};
</script>

<template>
  <div>
    <div v-for="(collectionItem, idx) in collection" :key="idx" class="group">
      <div class="flex items-center justify-between mb-3.5">
        <h3 class="text-sm font-semibold -tracking-028px leading-[18px] text-nio-grey-500">
          Position
        </h3>
        <div class="flex items-center gap-2">
          <BasicPopover v-if="!disabled" :disabled="disabled">
            <div role="listbox" class="w-[28px] h-[23px] rounded-30 flex items-center justify-center custom-shadow-001 bg-nio-grey-100" :class="disabled ? '' : 'hover:bg-nio-grey-hover-100 cursor-pointer'">
              <ThreeDots class="h-4 w-4" />
            </div>
            <template #popover-content>
              <div class="flex flex-col w-[9rem]">
                <div
                  class="text-sm font-medium text-nio-grey-700 py-2 px-1 flex items-center gap-3 hover:bg-nio-grey-100/20 cursor-pointer rounded-10"
                  role="option"
                  @click="addToCollection(idx, true)"
                >
                  <div>
                    <DuplicateIcon />
                  </div>
                  <div>Duplicate</div>
                </div>
                <div
                  class="text-sm font-medium text-nio-grey-700 py-2 px-1 flex items-center gap-3 hover:bg-nio-grey-100/20 cursor-pointer rounded-10"
                  role="option"
                  @click="deleteCollection(idx)"
                >
                  <div class="w-4 h-4 rounded-full border-[1.2px] border-nio-grey-700 flex items-center justify-center">
                    <DeleteIcon />
                  </div>
                  <div>Delete</div>
                </div>
              </div>
            </template>
          </BasicPopover>

          <button
            type="button"
            title="Collapse/Expand"
            class="w-[28px] h-[23px] rounded-30 flex items-center justify-center custom-shadow-001 bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer transition-transform"
            @click="toggleExpand(idx)"
          >
            <ChevronTopIcon :class="collapsed[idx] ? 'rotate-[180deg]' : ''" />
          </button>
        </div>
      </div>

      <div class="relative">
        <!-- Collapsed state -->
        <div
          :data-testid="`collapsed-section-${idx}`"
          class="border bg-nio-grey-100 rounded-[10px] h-[96px] mb-4 flex items-center px-9 w-full transition-all duration-300"
          :class="[
            collapsed[idx] ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none absolute h-0 -z-10',
            errors?.[idx] ? 'border-nio-red-500/20' : 'border-transparent'
          ]"
        >
          <div class="tracking-[-0.32px] leading-[16px] font-semibold text-nio-grey-700">
            {{ collectionItem.body[0]?.name }}
          </div>
          <div class="flex-1 flex justify-center">
            <NioInput v-model="reactiveValues[idx].body[collectionItem.body[0]?.component.payload_key]" disabled />
          </div>

          <div v-if="errors?.[idx]" class="absolute text-sm top-0 right-0 bg-nio-red-500/80 text-white py-1.5 px-2.5 rounded-md">
            {{ $t('misc.n-errors', errors[idx].length) }}
          </div>
        </div>

        <!-- Expanded state -->
        <div
          v-if="!collapsed[idx]"
          :data-testid="`expanded-section-${idx}`"
          class="grid xl:grid-cols-2 gap-[2px] transition-all duration-300 mb-4"
          :class="{
            'opacity-100 pointer-events-auto': !collapsed[idx],
            'opacity-0 pointer-events-none absolute h-0 -z-10': collapsed[idx]
          }"
        >
          <div
            v-for="(input, idx2) in collectionItem.body"
            :key="idx2"
            class="form-group bg-nio-grey-100 grid grid-cols-2"
          >
            <label
              :for="`${idx}-${idx2}-${input.component.payload_key}`"
              class="flex items-center p-6 2xl:p-9 tracking-[-0.32px] leading-[20px] font-semibold text-nio-grey-700"
            >
              {{ input.name }}
            </label>
            <div class="p-6 2xl:p-9 flex justify-end items-center">
              <component
                :is="FormComponentMap[input.component.id]"
                :key="`${componentKey}-${idx2}-${input.component.payload_key}`"
                v-model="reactiveValues[idx].body[input.component.payload_key]"
                :disabled="disabled"
                :input-id="`${idx}-${idx2}-${input.component.payload_key}`"
                :error="errors?.[idx]?.find(error => error.payload_key === input.component.payload_key)"
                :input-data="input"
                :error-position="errorPosition"
                :collection-identifier="idx"
                :inherited-options="input.component.options_from ? computeInheritedOptions(idx, input) : undefined"
                @options-updated="(data: FormSelectOption[]) => setDropdownOptions(idx, input.component.payload_key, data)"
                @nio-blur="emitNioBlur"
              />
            </div>
          </div>
        </div>
      </div>

      <div v-if="!disabled" class="mt-4 mb-10">
        <button
          type="button"
          class="flex opacity-0 group-hover:opacity-100 transition-all duration-100 items-center gap-1 h-8 px-3 rounded-45 bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer"
          @click="addToCollection(idx)"
        >
          <PlusIcon2 />
          <div class="text-nio-grey-500 font-medium text-sm -tracking-028px">
            Add Position
          </div>
        </button>
      </div>
    </div>

    <div v-if="collection.length === 0" class="mt-4 mb-10">
      <button
        type="button"
        class="flex transition-all duration-100 items-center gap-1 h-8 px-3 rounded-45 bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer"
        @click="addToCollection(-1)"
      >
        <PlusIcon2 />
        <div class="text-nio-grey-500 font-medium text-sm -tracking-028px">
          Add Position
        </div>
      </button>
    </div>

    <div v-if="props.suggestedData?.isLoading || props.suggestedData?.data" class="relative">
      <div class="mb-11">
        <div v-if="suggestedData?.isLoading">
          <div class="text-h5 font-semibold text-nio-grey-500">
            Analyzing documents to identify key positions…
          </div>
          <div class="flex justify-center items-center h-40">
            <LoadingText />
          </div>
        </div>
        <div v-else-if="props.suggestedData?.data && props.suggestedData!.data.length > 0" class="text-h5 font-semibold text-nio-grey-500">
          We've identified key positions tailored to your specific needs.
        </div>
        <div v-else class="text-h5 font-semibold text-nio-grey-500">
          Based on the provided document, all necessary roles for the project have been clearly defined. No additional suggestions are needed.
        </div>
      </div>
      <div class="grid grid-cols-2 gap-0.5">
        <div
          v-for="(suggestedItem, suggestedIndex) in props.suggestedData.data"
          :key="`suggested-${suggestedIndex}`"
          class="border bg-nio-grey-100 rounded-[10px] h-[96px] flex items-center gap-8 px-9 w-full transition-all duration-300 opacity-100 pointer-events-auto"
        >
          <div>
            <div class="tracking-[-0.32px] leading-[16px] font-semibold text-nio-grey-500 text-sm">
              Job Title
            </div>
            <div class="text-nio-grey-900 font-semibold line-clamp-2" :title="suggestedItem.job_title">
              {{ suggestedItem.job_title }}
            </div>
          </div>
          <div>
            <div class="tracking-[-0.32px] leading-[16px] font-semibold text-nio-grey-500 text-sm">
              Tech stack
            </div>
            <div class="text-nio-grey-900 font-semibold line-clamp-2" :title="[...suggestedItem.technologies.map(tech => tech.name), ...suggestedItem.tools.map(tool => tool.name)].join(', ')">
              {{ [...suggestedItem.technologies.map(tech => tech.name), ...suggestedItem.tools.map(tool => tool.name)].join(', ') }}
            </div>
          </div>
          <div class="flex-1 flex justify-end">
            <button
              type="button"
              class="bg-nio-white hover:bg-nio-blue-600-hover hover:cursor-pointer rounded-full px-4 py-2 text-[14px] font-paragraph text-nio-black-900 hover:text-white"
              @click="addSuggestedItemToCollection(suggestedIndex)"
            >
              Use
            </button>
          </div>
        </div>
      </div>
    </div>
    <slot name="form-bottom" />
  </div>
</template>

<style lang="css" scoped>
.form-group {
  &:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    @media (min-width: 1280px) {
      border-top-right-radius: 0;
      border-top-left-radius: 10px;
    }
  }

  &:nth-child(2) {
    @media (min-width: 1280px) {
      border-top-right-radius: 10px;
    }
  }

  &:last-child {
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    grid-column: 1 / -1;
  }
}

.custom-shadow-001 {
  box-shadow: 0px 0px 0px 2px #E8E8E8;
}

.rotate-180 {
  transform: rotate(180deg);
}
</style>
