<template>
  <div class="relative inline-block max-w-full">
    <input
      :id="inputId"
      ref="inputElement"
      v-model="modelValue"
      type="text"
      :disabled="disabled"
      class="min-w-[96px] max-w-full h-[32px] px-4 py-2 bg-nio-grey-background text-nio-black-900 border rounded-[10px] focus:outline-none focus:border-nio-blue-400 caret-nio-blue-800 caret-w-2 text-[16px] font-medium leading-[20px] tracking-[-0.48px] box-border overflow-hidden whitespace-nowrap"
      :class="[
        error?.message ? 'border-nio-red-background' : 'border-gray-300',
        disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : ''
      ]"
      :aria-describedby="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      :placeholder="placeholder"
      @input="adjustWidth"
      @blur="emit('nio-blur')"
    >
    <div
      v-if="error?.message"
      :id="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-6 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ error.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { DynamicFormItemData, FormErrors } from '@/common/utils/forms.ts';

const modelValue = defineModel<string>();
withDefaults(
  defineProps<{
      placeholder?: string,
      error?: FormErrors[0],
      errorPosition?: 'left' | 'right',
      inputId?: string,
      disabled?: boolean,
      inputData?: DynamicFormItemData,
      collectionIdentifier?: number,
    }>(),
  {
    placeholder: undefined,
    error: undefined,
    errorPosition: 'left',
    inputId: undefined,
    disabled: false,
    inputData: undefined,
    collectionIdentifier: undefined,
  }
);
const emit = defineEmits(['nio-blur']);

const inputElement = ref<HTMLInputElement | null>(null);

const adjustWidth = () => {
  if (inputElement.value) {
    inputElement.value.style.width = '96px';
    const scrollWidth = inputElement.value.scrollWidth;
    inputElement.value.style.width = `${Math.min(scrollWidth + 2, 400)}px`; // Add a small buffer to the width
    if (scrollWidth < 400) {
      inputElement.value.style.overflow = 'hidden';
      inputElement.value.style.textOverflow = 'clip';
    } else {
      inputElement.value.style.overflow = 'hidden';
      inputElement.value.style.textOverflow = 'ellipsis';
    }
  }
};

onMounted(() => {
  adjustWidth();
});

watch(modelValue, adjustWidth);
</script>
