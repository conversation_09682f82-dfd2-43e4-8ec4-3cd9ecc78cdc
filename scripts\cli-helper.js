import path from 'node:path';
import { execa } from 'execa';

export const isWindows = process.platform === 'win32';

export async function exec(cmd, args = []) {
  if (isWindows) {
    const command = [cmd, ...args].map(arg => `"${arg}"`).join(' ');
    await execa('wsl', ['--', 'bash', '-ic', command], { stdio: 'inherit' });
  } else {
    await execa(cmd, args, { stdio: 'inherit' });
  }
}

export async function execCapture(cmd, args = []) {
  if (isWindows) {
    const command = [cmd, ...args].map(arg => `"${arg}"`).join(' ');
    const { stdout } = await execa('wsl', ['--', 'bash', '-ic', command]);
    return stdout;
  } else {
    const { stdout } = await execa(cmd, args);
    return stdout;
  }
}

export function toWslPath(winPath) {
  const driveLetter = winPath[0].toLowerCase();
  const rest = winPath.slice(2).replace(/\\/g, '/');
  return `/mnt/${driveLetter}${rest}`;
}

export async function runShellScript(relativeScriptPath, args = []) {
  const resolvedPath = path.resolve(relativeScriptPath);
  const commandPath = isWindows ? toWslPath(resolvedPath) : resolvedPath;
  await exec(commandPath, args);
}
