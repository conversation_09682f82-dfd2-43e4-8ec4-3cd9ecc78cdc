<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue';
import type { FilterDesign } from '@/common/types/filter';

const FilterTypeMap: Record<string, any> = {
  'multi-select': defineAsyncComponent(() => import('@/common/components/UI/filter/NioFilterDropdown.vue')),
  'input-text': defineAsyncComponent(() => import('@/common/components/UI/filter/NioFilterInput.vue')),
  'number': defineAsyncComponent(() => import('@/common/components/UI/filter/NioFilterNumber.vue')),
  'single-date': defineAsyncComponent(() => import('@/common/components/UI/filter/NioFilterDate.vue')),
};

defineProps<{
  filter: {
    name: string;
    component: { id: string; options?: any; props?: Record<string, any>, min?: number, max?: number };
  };
  design?: FilterDesign;
}>();

const modelValue = defineModel<any>();

const appliedFiltersCount = computed(() => {
  if (Array.isArray(modelValue.value)) {
    return modelValue.value.length;
  }
  return modelValue.value ? 1 : 0;
});
</script>

<template>
  <component
    :is="FilterTypeMap[filter.component.id]"
    v-model="modelValue"
    data-testid="nio-filter-field"
    :filter="filter"
    :options="filter.component.options"
    :design="design"
    :name="filter.name"
  >
    <template #label>
      <button class="cursor-pointer flex items-center gap-2 justify-between align-middle">
        <span>{{ filter.name }}</span>
        <Transition appear>
          <span
            v-if="appliedFiltersCount > 0"
            :class="`rounded-full text-[10px] w-4 h-4 inline-flex items-center justify-center
            ${design === 'dark'
            ? 'bg-nio-grey-900 text-white'
            : 'bg-nio-blue-500 text-white'}`"
          >
            {{ appliedFiltersCount }}
          </span>
        </Transition>
      </button>
    </template>
  </component>
</template>
