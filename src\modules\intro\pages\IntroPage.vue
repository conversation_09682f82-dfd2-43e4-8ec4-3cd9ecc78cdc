<template>
  <div
    :key="introPageDivKey"
    class="w-full transition-all duration-500 ease-[var(--ease-out-3)]"
    :class="[currentStep <= IntroSteps.UPLOAD_VENDORS ? 'pt-[100px]' : '']"
  >
    <main class="flex items-center">
      <div class="flex-1">
        <IntroHeader :current-step="currentStep" />
        <div
          id="intro-content-container"
          class="pl-[30px]"
        >
          <IntroAccompanyingText
            :current-step="currentStep"
            :user-name="userName"
          />
          <div class="flex gap-[30px] [&>.card]:rounded-50 [&>.card]:border [&>.card]:border-[#BBD0FB]">
            <Card
              v-if="!isLoadingVisible && !searchState.isOpen"
              class="h-[315px]"
              :hover-scale-active="!simulatedFile"
              @click="onUploadCardClick"
            >
              <UploadCardSlot
                :current-step="currentStep"
                :file-detail="simulatedFile"
                @remove-file="resetUploadState"
              />
            </Card>
            <Card
              v-if="!isLoadingVisible"
              ref="introCardSearchEl"
              class="!transition-none"
              :class="[
                (currentStep >= IntroSteps.VERIFICATION || searchState.isOpen) ? 'flex-1' : '',
                searchState.isOpen ? 'mr-[30px] min-h-[315px] !h-fit' : '',
              ]"
              :hover-enabled="!pendingCompanies"
              @click="onClickSearch"
            >
              <SearchCardSlot
                v-model="searchState.text"
                :show-input="searchState.isOpen && !pendingCompanies"
                :pending-companies="pendingCompanies"
                @close-search="searchState.isOpen = false"
                @added-vendor-to-list="currentStep = IntroSteps.VERIFICATION"
              />
            </Card>
          </div>

          <div
            v-if="isLoadingVisible"
            class="flex gap-[30px] [&>.card]:rounded-25 [&>.card]:border [&>.card]:border-[#BBD0FB]"
          >
            <Card
              :hover-scale-active="!simulatedFile"
              :hover-enabled="false"
              class="flex-1"
            >
              <UploadLoadingCardSlot
                :current-step="currentStep"
                :file-detail="simulatedFile"
                @remove-file="resetUploadState"
              />
            </Card>
          </div>

          <div
            v-if="currentStep === IntroSteps.UPLOADING"
            class="w-full text-center pt-[135px]"
          >
            <AnimatedText
              class="text-h1"
              :text="$t('intro.uploading-animated-text') + '.'"
            />
          </div>
          <CompanyList
            v-if="!isLoadingVisible && (currentStep > IntroSteps.UPLOADING || vendorsStore.vendors.length > 0)"
            @all-vendors-verified="onAllVendorsVerified"
          />
        </div>
      </div>
      <div class="w-[86px]" />
      <IntroStepsBadges :current-step="currentStep" />
    </main>
    <UploadCompanyDialog
      v-model:show="modalState.upload.isShown"
      @file-uploaded="onFileUploaded"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, reactive, ref } from 'vue';
import { useVendorsStore } from '@/modules/intro/stores/companiesStore.ts';
import IntroHeader from '@/modules/intro/components/IntroHeader.vue';
import { IntroSteps } from '../types/intro-steps';
import IntroAccompanyingText from '@/modules/intro/components/IntroAccompanyingText.vue';
import Card from '@/common/components/Card.vue';
import IntroStepsBadges from '@/modules/intro/components/IntroStepsBadges.vue';
import UploadCardSlot from '@/modules/intro/components/cards/UploadCardSlot.vue';
import SearchCardSlot from '@/modules/intro/components/cards/SearchCardSlot.vue';
import CompanyList from '@/modules/intro/components/CompanyList.vue';
import UploadLoadingCardSlot from '@/modules/intro/components/cards/UploadLoadingCardSlot.vue';
import AnimatedText from '@/common/components/AnimatedText.vue';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import { useRouter } from 'vue-router';
import { routeMap as vendorsRouteMap } from '@/modules/companies/routes';
import UploadCompanyDialog from '@/modules/intro/components/UploadCompanyDialog.vue';
import { uploadingDelayMillis } from '@/config/intro.ts';

const vendorsStore = useVendorsStore();
const authStore = useAuthStore();
const simulatedFile = ref<File | undefined>();
const currentStep = ref<IntroSteps>(IntroSteps.INTRO);
const userName = authStore.getUserName() ?? '';
const modalState = reactive({
  upload: {
    isShown: false,
  },
});
const searchState = reactive({
  isOpen: false,
  text: '',
});
const isLoadingVisible = ref(false);
const uploadError = ref<string | null>(null);
const introPageDivKey = ref<number>(86472);
const router = useRouter();
const introCardSearchEl = ref();

const pendingCompanies = computed(() => vendorsStore.vendors.some(v => [undefined, 'pending'].includes(v.status)));

if (authStore.userProfile?.workspaces?.length) {
  router.replace({ name: vendorsRouteMap.companies.name });
}

const timeoutUploadVendors = setTimeout(() => {
  currentStep.value = IntroSteps.UPLOAD_VENDORS;
}, 4000);

const onClickSearch = () => {
  if (pendingCompanies?.value) {
    return;
  }
  clearTimeout(timeoutUploadVendors);
  searchState.isOpen = true;
};

const searchIntroCardListener = (event: MouseEvent) => {
  if (searchState.text?.length > 0) {
    return;
  }
  const target = introCardSearchEl.value?.$el;
  if (!target) {
    return;
  }
  const withinBoundaries = event.composedPath().includes(target);

  if (!withinBoundaries) {
    searchState.isOpen = false;
  }
};

onMounted(() => {
  document.addEventListener('click', searchIntroCardListener);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', searchIntroCardListener);
});

const onUploadCardClick = () => {
  if (simulatedFile.value) {
    return;
  }
  clearTimeout(timeoutUploadVendors);
  currentStep.value = IntroSteps.UPLOAD_VENDORS;
  modalState.upload.isShown = true;
};

const onFileUploaded = async(file: File) => {
  simulatedFile.value = file;
  currentStep.value = IntroSteps.UPLOADING;
  modalState.upload.isShown = false;
  isLoadingVisible.value = true;
  uploadError.value = null;

  try {
    await Promise.all([
      vendorsStore.uploadVendors(file),
      new Promise(resolve => setTimeout(resolve, uploadingDelayMillis)),
    ]);

    isLoadingVisible.value = false;
    currentStep.value = IntroSteps.VERIFICATION;
  } catch (error) {
    console.error('File upload failed:', error);
    uploadError.value = 'Failed to upload the file. Please try again.';
    isLoadingVisible.value = false;
    resetUploadState();
    if (vendorsStore.vendors.length > 1) {
      currentStep.value = vendorsStore.filteredVendors.value.every(v => v.status === 'verified') ? IntroSteps.ADD_VENDORS : IntroSteps.VERIFICATION;
      return;
    }
    currentStep.value = IntroSteps.UPLOAD_VENDORS;
  }
};

const onAllVendorsVerified = () => {
  currentStep.value = IntroSteps.ADD_VENDORS;
};

const resetUploadState = () => {
  simulatedFile.value = undefined;
};
</script>
