<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';
import type { Candidate, TenderCandidate } from '@/modules/tenders/types/tenders-types';
import { candidateScoreToText, isScoreAvailable } from '@/common/utils/score';
import NioSwitch from '@/common/components/UI/NioSwitch.vue';
import AboutCandidateMatch from './AboutCandidateMatch.vue';
import AboutCandidate from './AboutCandidate.vue';
import { useQuestionStore } from '@/modules/companies/stores/question-store';

const isPopoverOpen = defineModel<boolean>('open');
const switchedToAboutMatch = defineModel<boolean>('switchedToAboutMatch', { default: false });
const { candidate } = defineProps<{
   candidate: TenderCandidate,
}>();
const candidateFullProfile = ref<Candidate | null>(null);
const questionStore = useQuestionStore();

const title = computed(() => {
  if (switchedToAboutMatch.value && isScoreAvailable(candidate.matching?.score)) {
    return `${candidate.name}: ${candidateScoreToText(candidate.matching?.score, 'total')}`;
  } else {
    return `About: ${candidate.name}`;
  }
});

onMounted(() => {
  questionStore.getCandidateProfile(candidate.id).then(candidate => {
    candidateFullProfile.value = candidate ?? null;
  });
});

</script>

<template>
  <DetailPopover01
    v-model="isPopoverOpen"
    align="top"
    position="sticky"
    overflow="auto"
    min-height="470px"
    :title="title"
  >
    <div class="flex justify-center items-center pb-3">
      <NioSwitch
        v-if="isScoreAvailable(candidate.matching?.score)"
        v-model="switchedToAboutMatch"
        class="border border-nio-grey-100"
        color="blue"
      >
        <template #left>
          <span>About Candidate</span>
        </template>
        <template #right>
          <span>About Match</span>
        </template>
      </NioSwitch>
    </div>

    <Transition
      appear
      enter-active-class="transition-opacity duration-150 ease-in"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
    >
      <LazyLoad v-if="switchedToAboutMatch">
        <AboutCandidateMatch
          :candidate="candidate"
        />
      </LazyLoad>
      <LazyLoad v-else>
        <AboutCandidate
          v-if="candidateFullProfile"
          :candidate="candidateFullProfile"
        />
      </LazyLoad>
    </Transition>
  </DetailPopover01>
</template>
