<template>
  <div
    :class="[
      'w-[49.125rem] h-[28.0625rem] m-[30px_60px_120px_60px] rounded-10 flex items-center justify-center relative transition-all',
      isDragging ? 'bg-[#D5E0EC]' : 'bg-[rgba(249,249,249,0.90)]'
    ]"
    @dragover.prevent="handleDragOver"
    @dragleave="handleDragLeave"
    @drop="handleDrop"
    @click="openFileExplorer"
  >
    <div :class="{'hidden': !isDragging, 'flex flex-col items-center pointer-events-none': isDragging}">
      <uploadPlus />
      <p class="text-[18px] text-nio-grey-500">
        {{ $t('vendors-dialog-upload.dropYourFile') }}
      </p>
    </div>
    <p
      :class="{'hidden': isDragging, 'block': !isDragging}"
      class="text-[18px] text-nio-grey-500"
    >
      {{ $t('vendors-dialog-upload.dragAndDrop') }}
      <span class="bg-nio-blue-800 text-white px-2.5 py-1 rounded-10 cursor-pointer">
        {{ $t('vendors-dialog-upload.browse') }}
      </span>
    </p>
    <input
      ref="fileInput"
      type="file"
      class="hidden"
      :accept="accept"
      @change="handleFileUpload"
    >
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import uploadPlus from '@/assets/icons/upload-plus.svg';

const isDragging = ref(false);
const fileInput = ref<HTMLInputElement | null>(null);

interface Props {
  accept?: string,
}

withDefaults(defineProps<Props>(), {
  accept: '.pdf, .txt'
});

const emit = defineEmits(['fileSelected']);

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  const currentTarget = event.currentTarget as HTMLElement | null;
  if (currentTarget && !currentTarget.contains(event.relatedTarget as Node)) {
    isDragging.value = false;
  }
};

const openFileExplorer = () => {
  fileInput.value?.click();
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;

  const file = event.dataTransfer?.files[0];
  if (file) {
    emit('fileSelected', file);
  }
};

const handleFileUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input?.files?.[0]) {
    emit('fileSelected', input.files[0]);
  }
};
</script>
