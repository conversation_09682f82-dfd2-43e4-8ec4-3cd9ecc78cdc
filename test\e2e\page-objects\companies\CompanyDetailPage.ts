import { Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class CompanyDetailPage extends BasePage {
  public readonly heading;
  public readonly closeButton;

  constructor(page: Page) {
    super(page);
    this.heading = this.page.getByRole('heading').first();
    this.closeButton = this.page.getByRole('button', { name: 'close' });
  }

  isDetailPageVisible(): Promise<boolean> {
    return this.heading.isVisible();
  }
}
