<script lang="ts">
let currentOpenPopover: { close: () => void } | null = null;
</script>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';

type Props = {
  disabled?: boolean,
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const popoverRef = { close: () => {} };
const isShown = ref(false);
const contentEl = ref<HTMLElement | null>(null);

const closePopover = () => {
  isShown.value = false;
  if (currentOpenPopover === popoverRef) {
    currentOpenPopover = null;
  }
};

popoverRef.close = closePopover;

const openPopover = () => {
  if (props.disabled) {
    return;
  }
  if (currentOpenPopover && currentOpenPopover !== popoverRef) {
    currentOpenPopover.close();
  }
  isShown.value = true;
  currentOpenPopover = popoverRef;
};

const togglePopover = () => {
  if (isShown.value) {
    closePopover();
  } else {
    openPopover();
  }
};

const clickOutside = (event: MouseEvent) => {
  if (!isShown.value) {return;}
  if (contentEl.value && !event.composedPath().includes(contentEl.value)) {
    closePopover();
  }
};

onMounted(() => {
  document.addEventListener('click', clickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', clickOutside);
  if (currentOpenPopover === popoverRef) {
    currentOpenPopover = null;
  }
});
</script>

<template>
  <div class="relative">
    <div
      role="button"
      aria-label="Toggle popover"
      @click.stop.prevent="togglePopover"
    >
      <slot />
    </div>
    <div
      v-if="isShown"
      ref="contentEl"
      class="absolute right-full bottom-full translate-x-2 translate-y-2 cbs rounded-15 px-1.5 pt-1.5 pb-1.5 z-20 border border-nio-grey-100 bg-nio-white"
      @click.stop.prevent="togglePopover"
    >
      <slot name="popover-content" />
    </div>
  </div>
</template>

<style lang="css" scoped>
.cbs {
  box-shadow: 0px 95px 27px 0px rgba(105,105,105,0.00),
  0px 61px 24px 0px rgba(105,105,105,0.01),
  0px 34px 20px 0px rgba(105,105,105,0.05),
  0px 15px 15px 0px rgba(105,105,105,0.08),
  0px 4px 8px 0px rgba(105,105,105,0.09);
}
</style>
