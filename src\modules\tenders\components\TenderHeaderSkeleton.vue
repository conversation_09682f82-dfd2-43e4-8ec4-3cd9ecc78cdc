<template>
  <div class="flex flex-col animate-pulse">
    <div class="flex justify-between items-center mb-4">
      <div class="text-[14px] bg-nio-grey-100  rounded-5 ">
        <span class="opacity-0"> By
          <span class="text-[14px]  leading-[14px]">
            @Someone
          </span>
          <span class="text-[14px]  leading-[14px] ml-1">
            Some time ago
          </span>
        </span>
      </div>

      <div class="inline-flex px-2 py-1 text-[12px] font-bold rounded-5 bg-nio-grey-100 leading-[14px] uppercase">
        <span class="opacity-0">Tender Status</span>
      </div>
    </div>

    <h1 class="text-[26px] w-3/4 font-normal bg-nio-grey-100 rounded-10 leading-[28px] tracking-[0.52px] mb-2">
      <span class="opacity-0">Some tender name</span>
    </h1>

    <p class="text-[14px] font-normal bg-nio-grey-100 rounded-10 leading-[20px] tracking-[0.36px] mb-4 ">
      <span class="opacity-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Curabitur pretium tincidunt lacus.</span>
    </p>

    <div class="flex flex-wrap gap-2">
      <span class="bg-nio-grey-100 rounded-5">
        <NioTag class="opacity-0 color-transparent" size="sm">
          Some Region
        </NioTag>
      </span>
      <span class="bg-nio-grey-100 rounded-5">
        <NioTag class="opacity-0 color-transparent" size="sm">
          Main industry
        </NioTag>
      </span>
    </div>
  </div>
</template>