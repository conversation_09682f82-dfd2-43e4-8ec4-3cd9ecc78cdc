<template>
  <div class="relative">
    <button
      :id="inputId"
      class="flex items-center px-[18px] py-[12px] min-w-[83px] h-[40px] bg-nio-grey-background border rounded-[24px] text-nio-grey-500 text-[14px] font-medium leading-[16px] tracking-[-0.42px]"
      :class="[
        error?.message ? 'border-nio-red-background' : 'border-gray-300',
        disabled ? '' : 'hover:bg-nio-white hover:text-nio-black-900 cursor-pointer focus:outline-none focus:border-nio-blue-400'
      ]"
      type="button"
      :aria-describedby="`${inputData.component.payload_key}-error`"
      @click="openInput"
    >
      <PencilIcon class="w-4 h-4" />
      <span class="ml-[6px]">{{ buttonText }}</span>
    </button>

    <div
      v-if="error?.message"
      :id="`${inputData.component.payload_key}-error`"
      class="absolute -bottom-6 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ error?.message }}
    </div>

    <DetailPopover01
      v-model="isOpened"
      :title="inputData.name"
      height="470px"
      :close-on-backdrop-click="false"
    >
      <div class="flex flex-col h-[470px] w-[770px] rounded-[24px] bg-nio-grey-background">
        <textarea
          v-model="draftText"
          placeholder="Start Writing..."
          class="flex-1 bg-transparent resize-none border-none text-nio-grey-900 text-[16px] font-medium leading-[20px] tracking-[-0.32px] placeholder:text-nio-grey-500 placeholder:text-[16px] placeholder:font-medium placeholder:leading-[20px] placeholder:tracking-[-0.32px] custom-scrollbar-01 focus:outline-none focus:ring-0"
          :aria-describedby="`${inputData.component.payload_key}-error-inside`"
        />
        <div class="mt-4 self-end flex items-center w-full" :class="error?.message ? 'justify-between' : 'justify-end'">
          <div
            v-if="error?.message"
            :id="`${inputData.component.payload_key}-error-inside`"
            class="text-[14px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
            :class="errorPosition === 'right' ? 'right-0' : ''"
          >
            {{ error?.message }}
          </div>
          <button
            type="button"
            :disabled="saving"
            class="px-4 py-2 bg-nio-blue-800 text-white rounded-[24px] text-[14px] font-medium leading-normal tracking-[-0.28px] focus:outline-none hover:bg-nio-blue-600-hover hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            @click="saveText"
          >
            Save
          </button>
        </div>
      </div>
    </DetailPopover01>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import PencilIcon from '@/assets/icons/pencil-icon.svg';
import DetailPopover01 from '@/common/components/popover/DetailPopover01.vue';
import type { BlurPromise, DynamicFormItemData, FormErrors } from '@/common/utils/forms.ts';

const props = withDefaults(
  defineProps<{
  error?: FormErrors[0],
  inputData: DynamicFormItemData,
  errorPosition?: 'left' | 'right',
  inputId?: string,
  disabled?: boolean,
}>(),
  {
    error: undefined,
    errorPosition: 'left',
    inputId: undefined,
    disabled: false,
  }
);

const isOpened = ref(false);
const text = defineModel<string>({ required: true });
const draftText = ref(text.value ?? '');
const emit = defineEmits(['nio-blur']);
const saving = ref(false);

const buttonText = computed(() => (text.value ? 'Edit' : 'Insert Text'));

const saveText = async() => {
  if (saving.value) {
    return;
  }
  saving.value = true;
  text.value = draftText.value.trim();
  const blurPromise = (Promise as PromiseConstructor & {withResolvers: () => BlurPromise}).withResolvers();
  emit('nio-blur', blurPromise);
  try {
    await blurPromise.promise;
    isOpened.value = false;
  } catch {
    if (!props.error) {
      isOpened.value = false;
    }
  } finally {
    saving.value = false;
  }
};

const openInput = () => {
  if (props.disabled) {
    return;
  }
  isOpened.value = true;
};
</script>
