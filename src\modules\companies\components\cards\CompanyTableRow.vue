<script setup lang="ts">
import { computed, ref } from 'vue';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';
import CompanyCategoryLabel from '@/modules/companies/components/CompanyCategoryLabel.vue';
import type { Company } from '@/modules/companies/types/company';
import type { NioTableColumn } from '@/common/types/components';
import { useRouter } from 'vue-router';
import { routeMap } from '@/modules/companies/routes/';
import BasicPopover from '@/common/components/popover/BasicPopover.vue';
import ThreeDots from '@/assets/icons/three-dots.svg';
import ConfirmationModal from '@/common/components/popover/ConfirmationModal.vue';
import CompanyStatusLabel from '@/modules/companies/components/CompanyStatusLabel.vue';

const props = defineProps<{
  company: Company;
  columns: NioTableColumn[];
}>();
const router = useRouter();
const emit = defineEmits(['deleteCompany']);
const modal = ref<InstanceType<typeof ConfirmationModal>>();
const isImageLoaded = ref(true);

const showModal = () => {
  modal.value?.show();
};
const companyNoLogoText = computed(() => {
  const nameParts = props.company.name.split(' ');
  const firstChar = props.company.name.charAt(0);
  const secondChar = nameParts.length > 1 ? nameParts[nameParts.length - 1].charAt(0) : (props.company.name.length > 1 ? props.company.name.charAt(1) : '');
  return firstChar + secondChar;
});

const handleVendorClick = (vendorId: string) => {
  router.push({ name: routeMap.vendorDetail.name, params: { id: vendorId } });
};

</script>

<template>
  <NioTableRow
    data-testid="vendor-table-row"
    expandable
    :columns="columns"
    @click.stop="handleVendorClick(company.id)"
  >
    <template #name>
      <div class="flex items-center gap-2">
        <div class="w-7 h-7 overflow-hidden rounded-full align-middle flex items-center justify-center bg-nio-grey-900 text-nio-grey-100 ">
          <img
            v-if="company.logo && isImageLoaded"
            :src="company.logo"
            :alt="company.name"
            class="object-cover w-full h-full"
            @error="isImageLoaded = false"
          >
          <span v-else class="text-nio-white relative flex items-center justify-center text-[8px]">
            {{ companyNoLogoText }}
          </span>
        </div>
        {{ company.name }}
      </div>
    </template>

    <template #location>
      {{ company.country }}
    </template>

    <template #category>
      <CompanyCategoryLabel :category="company.category" />
    </template>

    <template #status>
      <CompanyStatusLabel :status="company.status" />
    </template>

    <template #actions>
      <BasicPopover class="inline-block">
        <div role="listbox" class="ml-auto w-7 h-6 rounded-full flex items-center justify-center bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer" title="Actions">
          <ThreeDots class="h-4 w-4" />
        </div>
        <template #popover-content>
          <div class="flex flex-col">
            <div
              class="text-sm justify-center text-center font-medium text-nio-grey-700 py-1 px-2 flex items-center gap-3 hover:bg-nio-grey-100 cursor-pointer rounded-10"
              role="option"
              @click.stop="showModal"
            >
              <div>Remove</div>
              <ConfirmationModal
                ref="modal"
                class="absolute text-center top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] cursor-auto"
                :modal-title="`Delete Vendor: ${company.name}`"
                modal-description="Do you really want to delete this vendor? This action cannot be undone."
                confirm-button-text="Delete"
                @confirmed="emit('deleteCompany', company.id)"
              />
            </div>
          </div>
        </template>
      </BasicPopover>
    </template>
  </NioTableRow>
</template>