<template>
  <div class="slider-wrapper relative">
    <div class="custom-slider" :class="disabled ? 'opacity-60' : 'cursor-pointer'">
      <div class="slider-track" />
      <div class="slider-thumb" :style="thumbStyle" :class="{ 'bg-nio-grey-500': disabled }">
        <span class="thumb-value">{{ modelValue }}%</span>
      </div>
    </div>
    <input
      :id="inputId"
      v-model="modelValue"
      :disabled="disabled"
      type="range"
      :min="inputData?.component?.min ?? 0"
      :max="inputData?.component?.max ?? 100"
      class="hidden-slider"
      :class="disabled ? '' : 'cursor-pointer'"
      :aria-describedby="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      @blur="emit('nio-blur')"
    >
    <div
      v-if="error?.message"
      :id="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-6 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ error?.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { DynamicFormItemData, FormErrors } from '@/common/utils/forms.ts';

type Props = {
  error?: FormErrors[0],
  errorPosition?: 'left' | 'right',
  inputId?: string,
  disabled?: boolean,
  inputData: DynamicFormItemData,
  collectionIdentifier?: number,
}

const props = withDefaults(
  defineProps<Props>(),
  {
    error: undefined,
    errorPosition: 'left',
    inputId: undefined,
    disabled: false,
    collectionIdentifier: undefined,
  }
);
const emit = defineEmits(['nio-blur']);

const modelValue = defineModel({ type: Number, default: 0 });

if (props.inputData?.component?.max && modelValue.value > props.inputData.component.max) {
  modelValue.value = props.inputData.component.max;
}

const thumbStyle = computed(() => {
  const ratio = modelValue.value / 100;
  const width = 38 + ratio * (54 - 38);
  return {
    left: modelValue.value + '%',
    transform: 'translate(-50%, -50%)',
    width: width + 'px'
  };
});
</script>

<style scoped>
.slider-wrapper {
  position: relative;
  width: 160px;
}
.custom-slider {
  position: relative;
  width: 100%;
  height: 32px;
  background: #fff;
  border-radius: 16px;
  pointer-events: none;
}
.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 16px;
  pointer-events: none;
}
.slider-thumb {
  position: absolute;
  top: 50%;
  height: 40px;
  width: 40px;
  max-width: 40px;
  background: #3b82f6;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
.thumb-value {
  color: #fff;
  font-weight: bold;
  font-size: 12px;
  user-select: none;
  padding-left: 10px;
  padding-right: 10px;
}
.hidden-slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 32px;
  opacity: 0;
  z-index: 2;
}
</style>
