<template>
  <div
    class="grid gap-[10px] w-full"
    :class="!props.techChips?.length || !pricingCardsWithPrice?.length ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'"
  >
    <div
      v-if="techChips?.length && pricingCardsWithPrice.length === 4"
      class="bg-black text-white rounded-30 p-[30px] h-full flex flex-col justify-between"
    >
      <div class="inline-block px-[12px] py-[6px] bg-nio-grey-background-15 text-nio-grey-200 text-[18px] font-normal rounded-5 leading-[18px] w-fit">
        {{ techTitle }}
      </div>
      <div class="grow mt-[30px] flex items-end">
        <ChipsList :chips="techChips" />
      </div>
    </div>

    <div
      v-if="techChips?.length && pricingCardsWithPrice.length !== 4"
      class="bg-black text-white rounded-30 p-[30px] h-full flex flex-col justify-between"
      :class="{ 'md:col-span-2': !pricingCardsWithPrice.length }"
    >
      <div class="inline-block px-[12px] py-[6px] bg-nio-grey-background-15 text-nio-grey-200 text-[18px] font-normal rounded-5 leading-[18px] w-fit">
        {{ techTitle }}
      </div>
      <div class="grow mt-[30px] flex items-end">
        <ChipsList :chips="techChips" />
      </div>
    </div>

    <div
      v-if="pricingCardsWithPrice.length"
      class="flex flex-wrap gap-[10px] w-full"
    >
      <div
        v-for="(card, index) in pricingCardsWithPrice"
        :key="index"
        class="flex-[1_1_33%] bg-black text-white rounded-30 p-[30px] flex flex-col justify-between min-h-[331px]"
      >
        <div class="inline-block px-[12px] py-[6px] bg-nio-grey-background-15 text-nio-grey-200 text-[18px] font-normal rounded-5 leading-[18px] w-fit">
          {{ card.title }}
        </div>
        <h1 class="text-[44px] font-normal text-white leading-[54px] mt-auto">
          {{ getValidRate(card.price) }}
        </h1>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ChipsList from '@/modules/companies/components/companyDetail/ChipsList.vue';
import { useWorkspaceRate } from '@/common/composables/useHourlyRate';

interface PricingCard {
  title: string;
  price?: number;
}

interface Props {
  techTitle: string;
  techChips?: string[];
  pricingCards?: PricingCard[];
}

const props = defineProps<Props>();

const { getValidRate } = useWorkspaceRate();

const pricingCardsWithPrice = computed(() => {
  return props.pricingCards?.filter(card => card.price !== undefined) || [];
});
</script>
