<template>
  <teleport to="body">
    <div
      v-if="isOpened"
      role="dialog"
      aria-modal="true"
      class="fixed top-0 left-0 z-40 w-screen h-screen backdrop-css justify-center overflow-y-auto overscroll-contain"
      @click.self="closeDialog"
    >
      <div
        class="w-[88.5rem] my-16 mx-auto max-w-[calc(100vw-120px)] p-4 relative bg-nio-grey-background-60 backdrop-blur-[50px] rounded-30 border-x border-x-nio-blue-outline-stroke-400 border-t border-t-nio-blue-outline-stroke-400"
      >
        <Button
          name="close"
          aria-label="close"
          class="absolute -top-[15px] right-[-30px] w-[30px] h-[30px] bg-[rgba(249,249,249,0.60)] border border-nio-blue-400 rounded-full flex items-center justify-center cursor-pointer hover:bg-[#F9F9F9]"
          @click="isOpened = false;"
        >
          <CloseIcon
            class="w-[13px] h-[13px]"
          />
        </Button>

        <HeaderComponent
          v-if="data && !loading"
          :id="data.id"
          class="mt-6"
          :name="data.name"
          :location="`${data.headquarters}, ${data.country}`"
          :founded-year="String(data.founded_at)"
          :cover="data.cover"
          :logo="data.logo"
          :loading="false"
        />
        <HeaderComponent
          v-else
          id="-"
          class="mt-6"
          name="-"
          location="-"
          founded-year="-"
          cover="-"
          logo="-"
          :loading="loading"
        />

        <div class="grid grid-cols-1 gap-3 mt-6">
          <Block
            v-if="loading || (data?.about)"
            :loading="!data"
            title="About"
            align="top"
            variant="transparent"
          >
            <template #data>
              <span class="text-nio-grey-900 text-[28px] font-normal leading-normal">
                {{ data?.about }}
              </span>
            </template>
          </Block>
          <div />

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
            <Block
              v-if="loading || (data?.employees?.positions && data.employees.positions.length > 0)"
              :loading="!data"
              title="Positions"
              :data="data?.employees?.positions"
              variant="black"
              :class="{'col-span-2': !data?.technologies || data.technologies.length === 0}"
            />

            <Block
              v-if="loading || (data?.technologies && data.technologies.length > 0)"
              :loading="!data"
              title="Technologies"
              :data="data?.technologies"
              variant="black"
              :class="{'col-span-2': !data?.employees?.positions || data.employees.positions.length === 0}"
            />
          </div>

          <Block
            v-if="loading || (data?.industries && data.industries.length > 0)"
            :loading="!data"
            title="Industries"
            :data="data?.industries"
            variant="black"
          />

          <div class="grid grid-cols-1 gap-3 lg:grid-cols-2">
            <div class="md:col-span-1">
              <Block
                :loading="!data"
                title="Main Industry"
                :data="data?.main_industry"
                variant="black"
              />
            </div>

            <div class="md:col-span-1 grid grid-cols-1 lg:grid-cols-2 gap-3">
              <Block
                :loading="!data"
                title="Employees"
                variant="white"
              >
                <template #data>
                  <div class="flex items-top gap-1">
                    <span>{{ data?.employees?.employees.toString() }}</span> <span class="text-nio-grey-300 text-[22px] mt-2">FTE's</span>
                  </div>
                </template>
              </Block>

              <Block
                :loading="!data"
                title="Developers"
                variant="white"
              >
                <template #data>
                  <div class="flex items-top gap-1">
                    <span>{{ data?.employees?.developers.toString() }}</span> <span class="text-nio-grey-300 text-[22px] mt-2">FTE's</span>
                  </div>
                </template>
              </Block>
            </div>
          </div>
          <Block
            v-if="loading || (data?.clients && data.clients.length > 0)"
            :loading="!data"
            title="Clients"
            :data="clients"
            variant="transparent"
          />
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import CloseIcon from '@/assets/icons/close-icon.svg';
import HeaderComponent from '@/modules/companies/components/companyDetail/HeaderComponent.vue';
import { computed } from 'vue';
import type { Vendor } from '@/modules/companies/types/company';
import Block from '@/modules/companies/components/companyDetail/Block.vue';

const isOpened = defineModel<boolean>('is-opened');

interface Props {
  data: Vendor | null;
  loading: boolean;
}

const props = defineProps<Props>();

const closeDialog = () => {
  isOpened.value = false;
};

const clients = computed(() => {
  if (!props.data) {
    return '';
  }
  return (props.data.clients as string[])?.join(', ');
});

</script>

<style scoped>
.backdrop-css {
  background: linear-gradient(180deg, rgba(217, 217, 217, 0.15) 0%, rgba(166, 173, 182, 0.15) 100%);
  backdrop-filter: blur(15px);
}
</style>
