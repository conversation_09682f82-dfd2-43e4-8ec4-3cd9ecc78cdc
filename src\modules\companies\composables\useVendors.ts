import { useDataFetcher } from '@/common/composables/useDataFetcher';
import type { Company } from '@/modules/companies/types/company';

export function useVendors(lazyLoad: boolean = false) {

  const listFetcher = useDataFetcher<Company>(
    '/enterprise/workspace/companies',
    {},
    {
      defaultPage: 1,
      lazyLoad
    }
  );

  return {
    companies: listFetcher.data,
    filters: listFetcher.filters,
    page: listFetcher.page,
    meta: listFetcher.meta,
    isLoading: listFetcher.isLoading,
    filterComponents: listFetcher.filterComponents,
    lazyLoad: listFetcher.lazyLoad,
    refresh: listFetcher.refresh,
    isLoadingFirstTime: listFetcher.isLoadingFirstTime,
  };
}
