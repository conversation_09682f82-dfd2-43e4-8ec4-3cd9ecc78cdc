<template>
  <label class="flex items-center cursor-pointer gap-2">
    <input
      v-model="modelValue"
      type="checkbox"
      :disabled="disabled"
      class="w-4 h-4 min-w-4 min-h-4 rounded-[6px] border-[1.5px] border-nio-border-default focus:ring-2 focus:ring-nio-blue-400
             checked:bg-nio-blue-800 checked:border-transparent disabled:bg-nio-grey-background-60 disabled:border-nio-grey-500"
    >
    <span
      class="text-nio-grey-500 transition-colors"
      :class="{
        'text-nio-grey-900': modelValue && !disabled,
        'text-nio-grey-500': disabled
      }"
      @click.stop
    >
      {{ label }}
    </span>
  </label>
</template>

<script setup lang="ts">
const modelValue = defineModel<boolean>();

defineProps<{ label: string; disabled?: boolean }>();
</script>
