import { expect, type Locator, type Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class AssistantPosition extends BasePage {
  readonly positionIndex: number;
  readonly collapseButton: Locator;
  readonly positionOptionsButton: Locator;
  readonly positionDeleteButton: Locator;
  readonly positionDuplicateButton: Locator;

  readonly jobTitleInput: Locator;
  readonly resourcesNumberInput: Locator;
  readonly senioritySelect: Locator;
  readonly workloadSliderHandle: Locator;
  readonly workloadSliderTrack: Locator;
  readonly workloadInput: Locator;
  readonly languageSelect: Locator;
  readonly languageOptions: Locator;
  readonly rateRangeTrack: Locator;
  readonly rateRangeLowerHandle: Locator;
  readonly rateRangeUpperHandle: Locator;
  readonly rateMinInput: Locator;
  readonly rateMaxInput: Locator;
  readonly technologiesSelect: Locator;
  readonly technologiesOptions: Locator;
  readonly mandatoryTechnologiesSelect: Locator;
  readonly toolsSelect: Locator;
  readonly toolsOptions: Locator;
  // readonly mandatoryToolsSelect: Locator;

  constructor(page: Page, positionIndex: number) {
    super(page);
    this.positionIndex = positionIndex;
    this.collapseButton = page.getByRole('button', { name: 'Collapse' }).nth(positionIndex);
    this.positionOptionsButton = this.page.getByRole('listbox').nth(this.positionIndex);
    this.positionDeleteButton = this.page.getByRole('option', { name: 'Delete' });
    this.positionDuplicateButton = this.page.getByRole('option', { name: 'Duplicate' });

    this.jobTitleInput = page.getByLabel('Job Title').nth(positionIndex);
    this.resourcesNumberInput = page.getByLabel('Number of Resources').nth(positionIndex);
    this.senioritySelect = page.getByRole('button', { name: 'Seniority Level' }).nth(positionIndex);
    this.languageSelect = page.getByRole('button', { name: 'Languages' }).nth(positionIndex);
    this.languageOptions = this.getFormCellByLabel('Languages').getByRole('listitem');
    this.technologiesSelect = page.getByRole('button', { name: 'Technology Stack' }).nth(positionIndex);
    this.technologiesOptions = this.getFormCellByLabel('Technology Stack').getByRole('listitem');
    this.mandatoryTechnologiesSelect = page.getByRole('button', { name: 'Must-have Technology' }).nth(positionIndex);
    this.toolsSelect = page.getByRole('button', { name: 'Key Tools' }).nth(positionIndex);
    this.toolsOptions = this.getFormCellByLabel('Key Tools').getByRole('listitem');
    // this.mandatoryToolsSelect = page.getByRole('button', { name: 'Must-have Tool' }).nth(positionIndex);

    const workloadCell = this.getFormCellByLabel('Workload');
    this.workloadSliderHandle = workloadCell.locator('.slider-thumb');
    this.workloadSliderTrack = workloadCell.locator('.slider-track');
    this.workloadInput = page.getByLabel('Workload').nth(positionIndex);

    const rateExpectationsCell = this.getFormCellByLabel('Hourly Rate Expectations');
    this.rateRangeTrack = rateExpectationsCell.locator('.slider-base');
    this.rateRangeLowerHandle = rateExpectationsCell.locator('.slider-handle-lower');
    this.rateRangeUpperHandle = rateExpectationsCell.locator('.slider-handle-upper');
    this.rateMinInput = rateExpectationsCell.getByLabel('Min');
    this.rateMaxInput = rateExpectationsCell.getByLabel('Max');
  }

  async fill(data) {
    if (data.title) {
      await this.jobTitleInput.fill(data.title);
    }
    if (data.numberOfResourcesIncrease) {
      await this.increaseNumber(this.resourcesNumberInput, data.numberOfResourcesIncrease);
    }
    if (data.seniority) {
      await this.selectSingleOption(this.senioritySelect, data.seniority);
    }
    if (data.workload !== undefined) {
      await this.moveSlider(this.workloadSliderTrack, this.workloadSliderHandle, data.workload);
    }
    if (data.languages) {
      await this.selectMultiOption(this.languageSelect, this.languageOptions, data.languages, { useCheckbox: true });
    }
    if (data.rateExpectations) {
      await this.moveSliderRange(this.rateRangeTrack, this.rateRangeLowerHandle, this.rateRangeUpperHandle, data.rateExpectations);
    }
    if (data.rateExpectationsInputs) {
      await this.fillRateInputs(data.rateExpectationsInputs[0], data.rateExpectationsInputs[1]);
    }
    if (data.technologies) {
      await this.selectMultiOption(this.technologiesSelect, this.technologiesOptions, data.technologies);
    }
    if (data.mandatoryTechnologies) {
      await this.selectSingleOption(this.mandatoryTechnologiesSelect, data.mandatoryTechnologies);
    }
    if (data.keyTools) {
      await this.selectMultiOption(this.toolsSelect, this.toolsOptions, data.keyTools);
    }
    // if (data.mandatoryTools) {
    //   await this.selectSingleOption(this.mandatoryToolsSelect, data.mandatoryTools);
    // }
  }

  getFormCellByLabel(label: string): Locator {
    return super.getFormCellByLabel(label).nth(this.positionIndex);
  }

  async fillRateInputs(minRate: number, maxRate: number) {
    await this.rateMinInput.fill(minRate.toString());
    await this.rateMaxInput.fill(maxRate.toString());
    // Trigger blur events to validate inputs
    await this.rateMinInput.blur();
    await this.rateMaxInput.blur();
  }

  async removePosition() {
    await this.positionOptionsButton.click();
    await expect(this.positionDeleteButton).toBeVisible();
    await this.positionDeleteButton.click();
  }
}
