<template>
  <div
    class="flex flex-col items-center justify-center w-full transition-all duration-300"
    :class="done ? 'h-[240px]' : 'h-[180px]'"
  >
    <template v-if="!done">
      <LoaderSmall />
      <div class="mt-5 text-[18px] font-medium leading-[24px] tracking-[-0.36px] text-nio-black text-center max-w-[80%] h-12">
        <AnimatedTextSlot :key="primaryKey" :speed-multiplier="1.2">
          {{ texts[currentTextIndex] }}
        </AnimatedTextSlot>
      </div>
    </template>

    <template v-else>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-15 w-15 text-green-600 mb-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        stroke-width="2"
      >
        <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
      </svg>
      <p class="text-[18px] font-medium text-center text-nio-black mb-4">
        The matching process is complete!
      </p>
      <button
        class="px-5 py-2 bg-nio-black text-nio-white rounded-full text-[14px] hover:bg-nio-grey-900 transition cursor-pointer"
        @click="navigateToMatching"
      >
        Results
      </button>
    </template>
  </div>
</template>

<script setup lang="ts">
import LoaderSmall from '@/common/components/Loader-small.vue';
import AnimatedTextSlot from '@/common/components/AnimatedTextSlot.vue';
import { useRouter } from 'vue-router';
import { routeMap } from '../routes';
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps<{
  matchingInProgress: boolean;
}>();

const router = useRouter();
const done = computed(() => !props.matchingInProgress);

const texts = [
  'Assembling your shortlist with care',
  'Looking into your request',
  'Almost there — preparing your matches.',
  'Getting closer to the right match.',
  'Finding what fits best.',
  'Matching with intention.',
  'Sorting through possibilities.',
  'Curating your vendor shortlist.',
  'Aligning needs with experience.',
  'Fine-tuning the recommendations.',
  'Looking deeper into the details.',
  'Selecting with precision.',
  'Balancing expertise and fit.',
  'Making sure it feels right.'
];

const currentTextIndex = ref(0);
const primaryKey = ref(0);
let intervalId: ReturnType<typeof setInterval>;

function startRotation() {
  intervalId = setInterval(() => {
    currentTextIndex.value = (currentTextIndex.value + 1) % texts.length;
    primaryKey.value++;
  }, 3000);
}

function stopRotation() {
  if (intervalId) {clearInterval(intervalId);}
}

onMounted(() => {
  if (!done.value) {startRotation();}
});

watch(done, isDone => {
  if (isDone) {
    stopRotation();
  } else {
    startRotation();
  }
});

onBeforeUnmount(() => {
  stopRotation();
});

const navigateToMatching = () => {
  router.push({ name: routeMap.detail.children.tenderMatching.name });
};
</script>
