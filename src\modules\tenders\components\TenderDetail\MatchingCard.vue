<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-[10px] p-[15px] bg-gray-100 rounded-30">
    <div class="p-4 bg-white rounded-15 shadow-md flex flex-col relative custom-shadow">
      <GreenCircle v-if="topMatches.length" class="absolute top-6 right-6 w-6 h-6" />
      <RedCircle v-else class="absolute top-6 right-6 w-6 h-6" />
      <div class="text-[16px] font-normal text-nio-grey-500 tracking-[0.64px] mb-4">
        Top Matches
      </div>
      <div class="flex items-center gap-[10px] mb-6">
        <span :class="[(potentialMatches.length === 0 ? 'text-[38px]' : 'text-[64px]'), (topMatches.length === 0 ? 'text-nio-grey-300' : 'text-nio-green-text'), 'font-normal', 'leading-none']">
          {{ topMatches.length }}
        </span>
        <span :class="[(potentialMatches.length === 0 ? 'text-[38px]' : 'text-[64px]'), 'text-nio-grey-300', 'font-normal', 'leading-none']">
          of {{ matches.length }}
        </span>
      </div>
      <div v-if="topMatches.length" class="border-t border-gray-200 pt-4 mt-4">
        <ul>
          <li
            v-for="(match, index) in topMatches"
            :key="index"
            class="flex justify-between items-center py-3"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 rounded-10 overflow-hidden border border-gray-200 mr-4">
                <img src="@/assets/tmp/images/face.png" alt="vendor" class="w-full h-full object-cover">
              </div>
              <div>
                <div class="text-[18px] font-medium text-gray-900">
                  {{ match.name }}
                </div>
                <div class="text-[14px] text-gray-500">
                  {{ match.hq }}, {{ match.country }}
                </div>
              </div>
            </div>
            <div class="text-[32px] font-normal text-nio-green-text">
              {{ match.score }}%
            </div>
          </li>
        </ul>
      </div>
      <div v-if="topMatches.length === 0" :class="['mt-auto', 'text-nio-red-500', 'font-medium', 'leading-normal', 'tracking-[-0.036px] text-balance', potentialMatches.length === 0 ? 'text-[14px]' : 'text-[18px]']">
        Unfortunately we couldn't determine top vendor matches for this request*
      </div>
    </div>
    <div class="flex flex-col gap-[10px]">
      <div v-if="potentialMatches.length > 0" class="p-6 bg-nio-grey-background-60 rounded-15 custom-shadow">
        <div class="text-[16px] font-normal text-nio-grey-500 mb-4">
          Potential Matches
        </div>
        <div class="text-[38px] font-normal text-nio-grey-700 mb-2">
          {{ potentialMatches.length }}
        </div>
        <div class="border-t border-gray-200 pt-4">
          <div v-if="potentialMatches[0]" class="flex justify-between items-center py-3">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-10 overflow-hidden border border-gray-200 mr-3">
                <img src="@/assets/tmp/images/face.png" alt="vendor" class="w-full h-full object-cover">
              </div>
              <div>
                <div class="text-[16px] font-medium text-gray-900">
                  {{ potentialMatches[0].name }}
                </div>
                <div class="text-[14px] text-gray-500">
                  {{ potentialMatches[0].hq }}, {{ potentialMatches[0].country }}
                </div>
              </div>
            </div>
            <div class="text-[32px] font-normal text-nio-grey-500">
              {{ potentialMatches[0].score }}%
            </div>
          </div>
        </div>
      </div>
      <div class="p-6 bg-nio-grey-background-60 rounded-15 custom-shadow">
        <div class="text-[16px] font-normal text-nio-grey-500 mb-4">
          Not a Match
        </div>
        <div class="text-[38px] font-normal text-nio-grey-700 mb-2">
          {{ notMatches.length }}
        </div>
        <div class="border-t border-gray-200 pt-4">
          <div v-if="notMatches[0]" class="flex justify-between items-center py-3">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-10 overflow-hidden border border-gray-200 mr-3">
                <img src="@/assets/tmp/images/face.png" alt="vendor" class="w-full h-full object-cover">
              </div>
              <div>
                <div class="text-[16px] font-medium text-gray-900">
                  {{ notMatches[0].name }}
                </div>
                <div class="text-[14px] text-gray-500">
                  {{ notMatches[0].hq }}, {{ notMatches[0].country }}
                </div>
              </div>
            </div>
            <div class="text-[32px] font-normal text-nio-grey-500">
              {{ notMatches[0].score }}%
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import GreenCircle from '@/assets/icons/green-circle.svg';
import RedCircle from '@/assets/icons/red-circle.svg';

interface Match {
  name: string;
  hq?: string;
  country?: string;
  score: number;
}

const props = withDefaults(
  defineProps<{
      matches?: Match[];
    }>(),
  {
    matches: () => [],
  }
);

const topMatches = computed(() =>
  props.matches.filter(match => match.score >= 90)
);

const potentialMatches = computed(() =>
  props.matches.filter(match => match.score >= 50 && match.score < 90)
);

const notMatches = computed(() =>
  props.matches.filter(match => match.score < 50)
);
</script>

<style scoped>
.custom-shadow {
  box-shadow: 0px 71px 20px 0px rgba(207, 207, 207, 0.00),
  0px 45px 18px 0px rgba(207, 207, 207, 0.01),
  0px 25px 15px 0px rgba(207, 207, 207, 0.05),
  0px 11px 11px 0px rgba(207, 207, 207, 0.09),
  0px 3px 6px 0px rgba(207, 207, 207, 0.10);
}
</style>
