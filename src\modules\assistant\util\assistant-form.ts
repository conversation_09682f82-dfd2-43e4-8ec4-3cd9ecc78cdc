import { nioAxios } from '@/axios';
import type { BlurPromise } from '@/common/utils/forms';
import { toast } from '@/common/utils/NotificationService';
import { AxiosError } from 'axios';

interface SaveFormOptions {
  shouldValidate?: boolean;
  blurPromise?: BlurPromise;
  showToast?: boolean;
  rfpId: string | number;
  formPayload: Record<string, any>;
  onValidationError422?: (e: any) => void;
  retryWithoutValidation?: boolean;
}

export const saveForm = async({
  shouldValidate = true,
  blurPromise,
  showToast = true,
  rfpId,
  formPayload,
  onValidationError422,
  retryWithoutValidation = false,
}: SaveFormOptions): Promise<boolean> => {
  const reqHeaders: Record<string, boolean | string> = {};
  if (!shouldValidate) {
    reqHeaders['Nio-Bypass-Validation'] = true;
  }

  try {
    await nioAxios.put(`/enterprise/assistant/rfp/${rfpId}`, formPayload, {
      headers: reqHeaders,
    });
    blurPromise?.resolve();
    return true;
  } catch (e: any) {
    const isValidationError = e.status === 422;

    if (isValidationError && onValidationError422) {
      onValidationError422(e);
    }

    if (showToast) {
      const errorResponse = (e as AxiosError<{ message: string; error?: { message?: string } }>).response;
      const errorMessage = errorResponse?.data?.error?.message || errorResponse?.data?.message || 'Unknown validation error';
      toast.show('Upload error', errorMessage, 'error');
    }

    blurPromise?.reject();

    if (retryWithoutValidation && isValidationError) {
      saveForm({
        shouldValidate: false,
        showToast: false,
        rfpId,
        formPayload,
        onValidationError422,
        retryWithoutValidation: false,
      });
    }

    if (!isValidationError) {
      throw new Error('Form submit error');
    }

    return false;
  }
};
