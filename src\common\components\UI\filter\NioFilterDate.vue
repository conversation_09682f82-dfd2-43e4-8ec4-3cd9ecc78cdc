<script setup lang="ts">
import { ref, watch } from 'vue';
import { debounce } from 'lodash-es';
import { XIcon } from 'lucide-vue-next';

const modelValue = ref<number|undefined>(undefined);
const emit = defineEmits<{ (e: 'update:modelValue', value: any): void }>();

defineProps<{
  type?: 'text' | 'number';
  name?: string;
}>();

const debouncedEmit = debounce((value: any) => {
  emit('update:modelValue', value);
}, 300);

watch(modelValue, newValue => {
  debouncedEmit(newValue);
});
</script>

<template>
  <div class="relative input-date-customization">
    <input
      v-model="modelValue"
      type="date"
      :placeholder="name"
      class="w-38 h-9 px-3 py-2 placeholder:text-nio-grey-900 text-gray-700 text-sm rounded-full border border-gray-300/40 hover:border-nio-blue-400 focus:outline-none focus:ring-0 focus:bg-white transition-all duration-200 hover:bg-gray-50"
    >
    <button
      v-if="modelValue"
      class="absolute inset-y-0 right-7 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
      @click="modelValue = undefined"
    >
      <XIcon class="w-4 h-4 cursor-pointer" />
    </button>
  </div>
</template>

<style scoped>
.input-date-customization {
  input[type="date"]::-webkit-calendar-picker-indicator {
    position: relative;
    bottom: 5px;
  }

  input[type=date] {
    text-align: left;
  }

  input[type="date"]:before {
    color: rgb(156, 156, 156);
    content: attr(placeholder) !important;
    font-size: 12px;
    display: block;
    height: 12px;
  }
}
</style>
