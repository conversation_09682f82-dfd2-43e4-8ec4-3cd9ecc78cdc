import { routeMap as assistantRoutes } from '@/modules/assistant/routes';
import { routeMap as tendersRouteMap } from '@/modules/tenders/routes';
import { routeMap as companiesRouteMap } from '@/modules/companies/routes';
import { routeMap as consultantsRouteMap } from '@/modules/consultants/routes';

type SubmenuItem = {
  routeName?: string;
  title: string;
  actAsActive?: boolean;
}

export interface MenuItem {
  label: string;
  routeName: string;
  subMenuItems?: SubmenuItem[];
}

export interface CustomSubMenuItem {
  matchedRoute: string;
  subMenuItems: SubmenuItem[];
}

export const menuLinks: MenuItem[] = [
  {
    label: 'Assistant',
    routeName: assistantRoutes.assistant.name,
    subMenuItems: [
      {
        routeName: assistantRoutes.assistant.children.assistantDashboard.name,
        title: 'Assistant',
      },
      {
        routeName: assistantRoutes.assistant.children.assistantHistory.name,
        title: 'History',
      },
    ]
  },
  {
    label: 'Tenders',
    routeName: tendersRouteMap.tenders.name,
    subMenuItems: [
      {
        routeName: tendersRouteMap.tenders.name,
        title: 'Tenders',
      },
    ]
  },
  {
    label: 'Consultants',
    routeName: consultantsRouteMap.consultants.name,
    subMenuItems: [
      {
        routeName: consultantsRouteMap.consultants.children.engaged.name,
        title: 'Engaged',
      },
      {
        routeName: consultantsRouteMap.consultants.children.available.name,
        title: 'Available',
      },
    ]
  },
  {
    label: 'Vendor Hub',
    routeName: companiesRouteMap.companies.name,
    subMenuItems: [
      {
        routeName: companiesRouteMap.companies.children.companiesDashboard.name,
        title: 'Vendor Hub',
      },
      // temporarily disabled due to issues with the upload functionality
      //{
      //  routeName: companiesRouteMap.companies.children.vendorsUpload.name,
      //  title: 'Upload',
      //},
    ]
  },
];

export const customSubMenuItems: CustomSubMenuItem[] = [
  {
    matchedRoute: tendersRouteMap.detail.name,
    subMenuItems: [
      {
        title: 'Tender',
        routeName: tendersRouteMap.detail.children.tenderDetail.name,
      },
      {
        title: 'Vendor Matching',
        routeName: tendersRouteMap.detail.children.tenderMatching.name,
      },
      {
        title: 'Marketplace',
        routeName: tendersRouteMap.detail.children.tenderMatchingMarketplace.name,
      },
      {
        title: 'Proposals',
        routeName: tendersRouteMap.detail.children.tenderProposal.name,
      },
    ]
  },
  {
    matchedRoute: assistantRoutes.assistantRfp.name,
    subMenuItems: [
      {
        routeName: assistantRoutes.assistant.children.assistantDashboard.name,
        title: 'Assistant',
        actAsActive: true,
      },
      {
        routeName: assistantRoutes.assistant.children.assistantHistory.name,
        title: 'History',
      },
    ]
  },
];
