#!/bin/bash
set -ex

apiCloneDir=$1

(
  # Execute subshell in the API clone directory.
  cd "$apiCloneDir"

  # Check if container "api" is running
  if docker compose ps -q api | xargs docker inspect -f '{{.State.Running}}' 2>/dev/null | grep -q true; then
    docker compose exec api rm -rf vendor
  else
    echo "Container 'api' is not running, skipping vendor cleanup in container."
  fi

  # Always try to bring the containers down (even if not running)
  docker compose down || true
)

# Remove the directory
if grep -qi microsoft /proc/sys/kernel/osrelease; then
  echo "Removing directory in WSL: $apiCloneDir. 🔐 You may be prompted for your WSL sudo password to clean docker/.containers"
  sudo rm -rf "$apiCloneDir"
else
  rm -rf "$apiCloneDir"
fi
