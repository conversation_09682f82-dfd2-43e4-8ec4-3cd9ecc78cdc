<template>
  <div class="spinner" />
</template>

<script setup lang="ts">
</script>
<style>
.spinner {
  width: 40px;
  height: 40px;
  --c: radial-gradient(farthest-side, #8A8A8A 92%, #0000);
  background: var(--c) 50% 0,
  var(--c) 50% 100%,
  var(--c) 100% 50%,
  var(--c) 0 50%;
  background-size: 9.6px 9.6px;
  background-repeat: no-repeat;
  animation: spinner-kh173p 1s infinite;
}

@keyframes spinner-kh173p {
  to {
    transform: rotate(.5turn);
  }
}
</style>
