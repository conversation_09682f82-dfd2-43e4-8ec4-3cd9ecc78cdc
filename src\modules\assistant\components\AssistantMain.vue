<script lang="ts" setup>
import MetchAirImg from '@/assets/images/metch_air-min.jpg';
import AssistantHistory from '@/modules/assistant/components/AssistantHistory.vue';
</script>

<template>
  <div>
    <main class="@container w-full min-h-[500px] h-fit bg-nio-grey-background-30 border border-nio-blue-outline-stroke-400 rounded-30 p-4">
      <div class="grid w-full gap-2.5 grid-cols-1 @[54rem]:grid-cols-[1fr_240px] @[67rem]:grid-cols-[1fr_240px_1fr]">
        <AssistantHistory />
        <section class="rounded-15 p-4 flex flex-col justify-between" :style="{backgroundImage: `url('${MetchAirImg}')`, backgroundSize: 'cover', backgroundPosition: 'center'}">
          <div class="px-2 py-1 rounded-5 backdrop-blur-md bg-nio-grey-background-15 w-fit">
            <span class="text-nio-white font-medium leading-[14px] -tracking-028px text-sm">About Assistant</span>
          </div>
          <h2 class="text-h28 font-medium text-nio-white tracking-[-0.56px] leading-8">
            How It Works?
          </h2>
        </section>
        <section class="bg-nio-metch-gradient rounded-15 p-4 pb-8 flex flex-col justify-between col-span-1 @[54rem]:col-span-2 @[67rem]:col-span-1">
          <div class="px-2 py-1 rounded-5 backdrop-blur-md bg-nio-grey-background-15 w-fit">
            <span class="text-nio-white font-medium leading-[14px] -tracking-028px text-sm">News</span>
          </div>
          <h2 class="text-h28 font-medium text-nio-white tracking-[-0.56px] leading-8">
            Projects similar to yours typically require a strong focus on Java. Make sure to prioritize this in your RFP.
          </h2>
        </section>
      </div>
    </main>
  </div>
</template>
