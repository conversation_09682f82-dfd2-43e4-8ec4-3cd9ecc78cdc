import { useDataFetcher } from '@/common/composables/useDataFetcher';
import { ConsultantTableType, type CandidateRow } from '../types/consultants-types';

export function useConsultants(tableType: ConsultantTableType) {

  const FETCH_URLS = {
    [ConsultantTableType.AVAILABLE]: '/enterprise/candidates/bench',
    [ConsultantTableType.ENGAGED]: '/enterprise/candidates/assigned',
  };

  const listFetcher = useDataFetcher<CandidateRow<typeof tableType>>(
    FETCH_URLS[tableType],
    {},
    {
      defaultPage: 1,
      lazyLoad: false
    }
  );

  return {
    consultants: listFetcher.data,
    filters: listFetcher.filters,
    page: listFetcher.page,
    meta: listFetcher.meta,
    isLoading: listFetcher.isLoading,
    filterComponents: listFetcher.filterComponents,
    firstLoadHadData: listFetcher.firstLoadHadData,
    isLoadingFirstTime: listFetcher.isLoadingFirstTime,
  };
}