<script setup lang="ts">
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import { exchangeCodeForToken, startLoginFlow } from '@/modules/auth/facades/token';
import { useRouter } from 'vue-router';
import { routeMap as introRouteMap } from '@/modules/intro/routes';
import { routeMap as assistantsRouteMap } from '@/modules/assistant/routes';

const authStore = useAuthStore();
const router = useRouter();

const checkTokenAndInitializeApp = async() => {
  try {
    const userData = await authStore.fetchUserProfile();
    if (!userData?.workspaces?.length) {
      const targetRoute = { name: introRouteMap.intro.name };
      window.history.replaceState('', '', router.resolve(targetRoute).fullPath);
      await router.replace(targetRoute);
    } else {
      const redirectURL = sessionStorage.getItem('redirect_url');
      let targetRoute = { name: assistantsRouteMap.assistant.name } as Record<string, any>;
      if (redirectURL) {
        sessionStorage.removeItem('redirect_url');
        const url = new URL(redirectURL);
        const path = url.pathname;
        const queryParams = Object.fromEntries(url.searchParams.entries());
        targetRoute = { path: path, query: queryParams };
      }
      await router.replace(targetRoute);
      window.history.replaceState(null, '', router.resolve(targetRoute).fullPath);
    }
  } catch {
    startLoginFlow();
  }
};

exchangeCodeForToken()
  .then(async() => {
    await checkTokenAndInitializeApp();
  })
  .catch(async() => {
    try {
      await checkTokenAndInitializeApp();
    } catch {
      //
    }
  });
</script>

<template>
  <div />
</template>
