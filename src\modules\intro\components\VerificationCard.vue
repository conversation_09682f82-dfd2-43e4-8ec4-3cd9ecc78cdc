<template>
  <div
    class="flex flex-col"
    role="listitem"
    :aria-label="displayName"
    :data-verified="type === 'verified'"
  >
    <div v-if="type !== 'unverified'">
      <div
        class="group flex flex-col h-[300px] justify-between items-start bg-[rgba(249,249,249,0.60)] rounded-25 border border-[#BBD0FB] hover:bg-nio-grey-background-90 transition-transform duration-300 ease-in-out relative most-used-shadow"
      >
        <div
          class="absolute w-full top-5 left-0 px-5 flex items-center justify-between"
        >
          <div
            :class="['px-2 py-1 text-xs font-medium rounded-30 border', getTypeClass(type)]"
          >
            <span :class="[type === 'pending' ? 'animated-gradient-text' : '', getTypeClass(type), 'capitalize-text']">
              {{ getTypeLabel(type) }}
            </span>
          </div>
          <BasicPopover>
            <div class="opacity-0 group-hover:opacity-100 duration-300 w-[32px] h-[27px] rounded-30 flex items-center justify-center custom-shadow-001 bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer">
              <ThreeDots class="h-4 w-4" />
            </div>
            <template #popover-content>
              <div class="flex flex-col w-fit select-none">
                <div
                  class="text-sm font-medium text-nio-grey-700 py-2 px-1 flex items-center gap-3 hover:bg-nio-grey-100/20 cursor-pointer rounded-10"
                  @click="deleteVendor"
                >
                  <div class="w-4 h-4 rounded-full border-[1.2px] border-nio-grey-700 flex items-center justify-center">
                    <DeleteIcon />
                  </div>
                  <div>Delete</div>
                </div>
              </div>
            </template>
          </BasicPopover>
        </div>
        <div class="flex flex-col h-full justify-end gap-[10px] ml-[20px] w-5/6 mb-[30px]">
          <div class="w-[40px] h-[40px] bg-[rgba(249,249,249,0.3)] rounded-full" />
          <div class="flex flex-col">
            <h2 class="text-[24px] font-normal leading-[29px] text-[#000] break-words">
              {{ displayName }}
            </h2>
            <p class="text-[16px] font-normal leading-[19px] text-[#8a8a8a]">
              Region
            </p>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="similarVendors.length > 0">
      <div
        class="group flex flex-col justify-center h-[300px] items-start bg-[rgba(249,249,249,0.60)] rounded-25 border border-[#BBD0FB] hover:bg-nio-grey-background-90 transition-transform duration-300 ease-in-out relative most-used-shadow"
      >
        <div
          class="absolute w-full top-5 left-0 px-5 flex items-center justify-between"
        >
          <div
            :class="['px-2 py-1 text-xs font-medium rounded-30 border', getTypeClass(type)]"
          >
            <span :class="[props.type === 'pending' ? 'animated-gradient-text' : '', getTypeClass(props.type), 'capitalize-text']">
              {{ getTypeLabel(type) }}
            </span>
          </div>
          <BasicPopover>
            <div class="opacity-0 group-hover:opacity-100 duration-300 w-[32px] h-[27px] rounded-30 flex items-center justify-center custom-shadow-001 bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer">
              <ThreeDots class="h-4 w-4" />
            </div>
            <template #popover-content>
              <div class="flex flex-col w-fit select-none">
                <div
                  class="text-sm font-medium text-nio-grey-700 py-2 px-1 flex items-center gap-3 hover:bg-nio-grey-100/20 cursor-pointer rounded-10"
                  @click="deleteVendor"
                >
                  <div class="w-4 h-4 rounded-full border-[1.2px] border-nio-grey-700 flex items-center justify-center">
                    <DeleteIcon />
                  </div>
                  <div>Delete</div>
                </div>
              </div>
            </template>
          </BasicPopover>
        </div>
        <div class="flex flex-col h-full justify-center mt-[100px] gap-[10px] ml-[20px] w-5/6 mb-[30px]">
          <div class="flex flex-col">
            <h2 class="text-[24px] font-normal leading-[29px] text-[#000] break-words">
              {{ displayName }}
            </h2>
            <p class="text-[16px] font-normal leading-[19px] text-[#8a8a8a]">
              cannot be verified as no reliable data is available
            </p>
            <p class="text-[20px] font-normal leading-[19px] text-[#0071E3] mt-[15px]">
              Did you mean?
            </p>
            <div class="flex flex-wrap gap-[10px] pt-5" role="list">
              <div
                v-for="vendor in similarVendors"
                :key="vendor.companyId"
                class="flex w-auto pt-[5px] pr-[10px] pb-[5px] pl-[10px] gap-[10px] items-center shrink-0 flex-nowrap bg-[rgba(255,255,255,0.9)] rounded-10 border-none text-[#000] cursor-pointer hover:bg-nio-blue-800 hover:text-white"
                role="option"
                @click="handleClick(importId, vendor.name, vendor.companyId)"
              >
                {{ vendor?.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div
        class="group flex flex-col h-[300px] p-6 bg-[rgba(249,249,249,0.60)] rounded-25 border border-[#BBD0FB] hover:bg-nio-grey-background-90 transition-transform duration-300 ease-in-out relative most-used-shadow"
      >
        <div
          class="absolute w-full top-5 left-0 px-5 flex items-center justify-between"
        >
          <div
            :class="['px-2 py-1 text-xs font-medium rounded-30 border', getTypeClass(type)]"
          >
            <span :class="[props.type === 'pending' ? 'animated-gradient-text' : '', getTypeClass(props.type), 'capitalize-text']">
              {{ getTypeLabel(type) }}
            </span>
          </div>
          <BasicPopover>
            <div class="opacity-0 group-hover:opacity-100 duration-300 w-[32px] h-[27px] rounded-30 flex items-center justify-center custom-shadow-001 bg-nio-grey-100 hover:bg-nio-grey-hover-100 cursor-pointer">
              <ThreeDots class="h-4 w-4" />
            </div>
            <template #popover-content>
              <div class="flex flex-col w-fit select-none">
                <div
                  class="text-sm font-medium text-nio-grey-700 py-2 px-1 flex items-center gap-3 hover:bg-nio-grey-100/20 cursor-pointer rounded-10"
                  role="button"
                  aria-label="Delete vendor"
                  @click="deleteVendor"
                >
                  <div class="w-4 h-4 rounded-full border-[1.2px] border-nio-grey-700 flex items-center justify-center">
                    <DeleteIcon />
                  </div>
                  <div>Delete</div>
                </div>
              </div>
            </template>
          </BasicPopover>
        </div>
        <div class="flex flex-col h-full justify-center mt-[100px] w-5/6 mb-[5px]">
          <h2 class="text-nio-red-500 text-[24px] font-normal leading-normal tracking-[-0.48px]">
            {{ name }}
          </h2>
          <p class="text-nio-red-500 text-[18px] font-normal leading-normal tracking-[-0.36px]">
            Looks like we couldn't find this vendor.
          </p>
          <p class="text-nio-grey-700 text-[16px] font-normal leading-normal tracking-[-0.32px]">
            We've been made aware of this and are currently working on adding more vendors, including this one, to our
            database!
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useVendorsStore } from '@/modules/intro/stores/companiesStore.ts';
import BasicPopover from '@/common/components/popover/BasicPopover.vue';
import ThreeDots from '@/assets/icons/three-dots.svg';
import DeleteIcon from '@/assets/icons/x-icon.svg';

type CompanyImportStatus = 'verified' | 'unverified' | 'pending';

interface Props {
  type: CompanyImportStatus
  id?: string
  importId: string
  name: string
  similarVendors: { companyId: string; name: string }[]
}

const props = defineProps<Props>();
const vendorsStore = useVendorsStore();
const displayName = ref(props.name);

const handleClick = async(importId: string, name: string, companyId: string) => {
  displayName.value = name;
  try {
    await vendorsStore.updateVendor(importId, name, companyId);
  } catch (error) {
    console.error('Error:', error);
  }
};

const getTypeClass = (type: CompanyImportStatus): string => {
  switch (type) {
    case 'verified':
      return 'bg-nio-green-bg text-nio-blue-800 border-transparent';
    case 'pending':
      return 'bg-transparent border-nio-blue-800';
    case 'unverified':
    default:
      return 'bg-nio-red-background text-nio-red-500 border-transparent';
  }
};

const getTypeLabel = (type: CompanyImportStatus): string => {
  switch (type) {
    case 'verified':
      return 'verified';
    case 'pending':
      return 'pending';
    case 'unverified':
    default:
      return 'unverified';
  }
};

const deleteVendor = () => {
  try {
    vendorsStore.deleteVendor(props.importId);
  } catch (error) {
    console.error('Error deleting vendor:', error);
  }
};
</script>

<style scoped>
.animated-gradient-text {
  background: linear-gradient(87deg, #0071E3, #BBD0FB, #0071E3);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-animation 3s infinite;
}

.capitalize-text {
  text-transform: capitalize;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
