<template>
  <div :class="started ? '' : 'opacity-0'">
    <div ref="textDiv">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { transformText } from '@/common/utils/text';

interface Props {
  speedMultiplier?: number,
  delay?: number
}

const props = withDefaults(defineProps<Props>(), {
  speedMultiplier: 1,
  delay: undefined,
});

const textDiv = ref<HTMLDivElement>();
const started = ref(false);

onMounted(() => {
  if (props.delay) {
    setTimeout(() => {
      started.value = true;
      transformText(textDiv.value!, props.speedMultiplier);
    }, props.delay);
    return;
  }
  started.value = true;
  transformText(textDiv.value!, props.speedMultiplier);
});
</script>
