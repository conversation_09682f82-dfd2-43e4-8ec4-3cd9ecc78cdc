stages:
  - setup
  - test
  - build
  - deploy
  - cleanup

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "master"

default:
  image: node:22
  timeout: 20 minutes

.pnpm:
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
      - node_modules/
  before_script:
    - npm install --global corepack@latest
    - corepack enable
    - corepack prepare pnpm@latest-10 --activate
    - pnpm config set store-dir .pnpm-store

.setup_ssh:
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/gitlab
    - chmod 600 ~/.ssh/gitlab
    - ssh-add ~/.ssh/gitlab
    - ssh-keyscan -p $DEPLOY_PORT $DEPLOY_SERVER >> ~/.ssh/known_hosts

.deploy:
  image: alpine:latest
  before_script:
    - apk update && apk add openssh-client bash rsync
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -p $DEPLOY_PORT $DEPLOY_SERVER >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - ssh $DEPLOY_USER@$DEPLOY_SERVER -p $DEPLOY_PORT "mkdir -p $DEPLOY_PATH"
    - rsync -avz dist/ $DEPLOY_USER@$DEPLOY_SERVER:$DEPLOY_PATH --rsh="ssh -p $DEPLOY_PORT"

setup:
  stage: setup
  extends:
    - .pnpm
  artifacts:
    reports:
      codequality: gitlab-codequality.json
  script:
    - pnpm install
    - pnpm lint:ci

# This job is a workaround so we can run tests optionally,
# without marking pipeline as blocked, but mark it as failed if tests fail.
trigger-test-e2e:
  stage: test
  script: echo "Triggering e2e tests..."
  when: manual
  allow_failure: true # Skippable

test-e2e:
  stage: test
  extends:
    - .pnpm
  needs:
    - job: trigger-test-e2e
  script:
    - pnpm exec playwright install
    - pnpm test:e2e:full

trigger-build:
    stage: build
    script: echo "Triggering build..."
    when: manual
    allow_failure: true # Skippable
    rules:
      - if: $CI_MERGE_REQUEST_ID

build:
  stage: build
  extends:
    - .pnpm
  script:
    - ./node_modules/.bin/envsub
      --env VITE_APP_KEY=$VITE_APP_KEY
      --env VITE_API_BASE_URL=$VITE_API_BASE_URL
      --env VITE_I18N_LOCALE=$VITE_I18N_LOCALE
      --env VITE_I18N_FALLBACK_LOCALE=$VITE_I18N_FALLBACK_LOCALE
      --env AUTH_API_URL=$AUTH_API_URL
      --env AUTH_CLIENT_ID=$AUTH_CLIENT_ID
      --env AUTH_SCOPE=$AUTH_SCOPE
      --env SENTRY_DSN=$SENTRY_DSN
      --env GTAG_ID=$GTAG_ID
      --env CLARITY_ID=$CLARITY_ID
      .env.ci .env
    - pnpm build
  artifacts:
    paths:
      - dist/
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: false
      needs:
        - job: trigger-build
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "master"

deploy-dev:
  stage: deploy
  extends:
    - .deploy
  environment:
    name: develop
  variables:
    DEPLOY_PATH: "/opt/nio/enterprise/fe"
    PROXY_PATH: "/opt/nio/tools/develop-proxy"
  needs:
    - job: build
      artifacts: true
  script:
    - ssh $DEPLOY_USER@$DEPLOY_SERVER -p $DEPLOY_PORT "cd $PROXY_PATH && docker compose down && docker compose up -d"
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"

deploy-mr:
  stage: deploy
  extends:
    - .deploy
  environment:
    name: mr/$CI_MERGE_REQUEST_IID
    url: 'https://$CI_MERGE_REQUEST_IID.mr.enterprise.dev.nordics.io'
    on_stop: undeploy-mr
  variables:
    DEPLOY_PATH: "/opt/nio/enterprise/mr/$CI_MERGE_REQUEST_IID"
  needs:
    - job: build
      artifacts: true
  script:
    - ssh $DEPLOY_USER@$DEPLOY_SERVER -p $DEPLOY_PORT "cd /opt/nio/tools/mr-proxy && docker compose down && docker compose up -d"
  rules:
    - if: $CI_MERGE_REQUEST_ID

undeploy-mr:
  stage: cleanup
  extends:
    - .setup_ssh
  environment:
    name: mr/$CI_MERGE_REQUEST_IID
    action: stop
  variables:
    ROOT_PATH: "/opt/nio/enterprise/mr"
  script:
    - ssh $DEPLOY_USER@$DEPLOY_SERVER -p $DEPLOY_PORT "cd $ROOT_PATH && rm -rf $CI_MERGE_REQUEST_IID"
  needs:
    - job: deploy-mr
  rules:
    - if: $CI_MERGE_REQUEST_ID
  when: manual
