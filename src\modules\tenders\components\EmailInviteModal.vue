<script setup lang="ts">
import { ref, defineProps, onMounted, watch, computed } from 'vue';
import type { Tender } from '../types/tenders-types';
import TenderSubmissionDateForm from './TenderDetail/TenderSubmissionDateForm.vue';
import { DateTime } from 'luxon';

const emailHtml = ref('');
const currentStep = ref<'submission-date' | 'email-preview'>('submission-date');

const props = defineProps<{
  visible: boolean;
  tenderName: string;
  tenderData?: Tender;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'send'): void;
  (e: 'updated-tender', tender: Tender): void;
}>();

const submissionForm = ref<InstanceType<typeof TenderSubmissionDateForm>>();

const hasSeenSubmissionDateToday = computed(() => {
  if (!props.tenderData?.id) {return false;}

  const today = DateTime.now().toISODate();
  const storageKey = `submission-date-seen-${props.tenderData.id}-${today}`;
  return localStorage.getItem(storageKey) === 'true';
});

const getInitialStep = (): 'submission-date' | 'email-preview' => {
  return hasSeenSubmissionDateToday.value ? 'email-preview' : 'submission-date';
};

const fetchEmailTemplate = async(tenderName: string) => {
  try {
    const res = await fetch('/tender-invite-email.html');
    if (!res.ok) {
      throw new Error(`Failed to fetch email template: ${res.status}`);
    }
    let html = await res.text();
    html = html.replace(/\{\{TENDER_NAME\}\}/g, String(tenderName));
    html = html.replace(/\{\{TENDER_SUBMISSIONS_DEADLINE\}\}/g, String(DateTime.fromISO(props.tenderData!.submissions_deadline).toFormat('d LLLL yyyy, HH:mm')));
    emailHtml.value = html;
  } catch (error) {
    console.error('Error loading email template:', error);
    emailHtml.value = '<p>Unable to load email template. Please try again later.</p>';
  }
};

const goToNextStep = async() => {
  if (!props.tenderData?.id) {
    return;
  }
  try {
    await submissionForm.value?.submit();
    const today = DateTime.now().toISODate();
    const storageKey = `submission-date-seen-${props.tenderData.id}-${today}`;
    localStorage.setItem(storageKey, 'true');
    currentStep.value = 'email-preview';
  } catch (e) {
    console.warn(e);
  }
};

const goToSubmissionDateStep = () => {
  currentStep.value = 'submission-date';
};

onMounted(async() => {
  fetchEmailTemplate(props.tenderName);
  currentStep.value = getInitialStep();
});

watch(
  () => [props.visible, props.tenderName, props.tenderData?.submissions_deadline],
  async([visible, tenderName]) => {
    if (visible === true && tenderName && tenderName !== '') {
      await fetchEmailTemplate(tenderName as string);
      currentStep.value = getInitialStep();
    } else if (!visible) {
      emailHtml.value = '';
      currentStep.value = 'submission-date';
    }
  },
  { immediate: true }
);
</script>

<template>
  <transition name="fade-in-x" class="custom-fade-in-duration" appear>
    <div
      v-if="props.visible"
      aria-label="Email invite modal"
      aria-modal="true"
      role="dialog"
      class="fixed top-0 left-0 z-40 w-screen h-screen flex items-center justify-center backdrop-css overflow-y-scroll overscroll-contain"
    >
      <div class="w-full max-w-3xl md:max-w-4xl lg:max-w-5xl max-h-[90vh] p-4 md:p-8 rounded-30 shadow-xl border border-nio-blue-400 bg-white flex flex-col items-center justify-center m-0">
        <!-- Step 1: Submission Date -->
        <div v-if="currentStep === 'submission-date'" class="w-full">
          <h2 class="text-2xl font-semibold mb-6 text-center">
            Update Submission Deadline
          </h2>
          <div class="rounded-2xl mb-4 mx-auto flex items-center justify-center bg-gray-100 py-6 w-fit">
            <TenderSubmissionDateForm
              ref="submissionForm"
              :tender-data="tenderData"
              :external-submit="true"
            />
          </div>
          <div class="flex justify-between mt-8 w-full">
            <button
              class="px-6 py-2 border border-nio-grey-hover-100 hover:bg-nio-grey-hover-100 hover:cursor-pointer text-black rounded-full"
              @click="emit('close')"
            >
              Close
            </button>
            <button
              class="px-6 py-2 bg-nio-blue-800 text-nio-white rounded-full hover:bg-nio-blue-600-hover transition-colors cursor-pointer"
              @click="goToNextStep"
            >
              Next
            </button>
          </div>
        </div>

        <!-- Step 2: Email Preview -->
        <div v-else-if="currentStep === 'email-preview'" class="w-full">
          <h2 class="text-2xl font-semibold mb-6 text-center">
            Send Invite
          </h2>
          <div class="rounded-2xl mb-8 min-h-[200px] max-h-[60vh] w-full flex items-center justify-center overflow-auto">
            <!-- eslint-disable -->
            <div v-if="emailHtml" class="w-full max-w-full overflow-auto" v-html="emailHtml" />
            <!-- eslint-enable -->
            <div v-else class="text-nio-grey-500">
              Loading preview…
            </div>
          </div>
          <div class="flex justify-between mt-8 w-full">
            <div class="flex gap-2">
              <button
                class="px-6 py-2 border border-nio-grey-hover-100 hover:bg-nio-grey-hover-100 hover:cursor-pointer text-black rounded-full"
                @click="emit('close')"
              >
                Close
              </button>
              <button
                class="px-6 py-2 border border-nio-grey-hover-100 hover:cursor-pointer hover:border-nio-blue-800 text-nio-blue-800 rounded-full hover:bg-nio-blue-50 transition-colors cursor-pointer"
                @click="goToSubmissionDateStep"
              >
                Change Submission Deadline
              </button>
            </div>
            <button
              class="px-6 py-2 bg-nio-blue-800 text-nio-white rounded-full hover:bg-nio-blue-600-hover transition-colors cursor-pointer"
              @click="emit('send')"
            >
              Send Invite
            </button>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped lang="css">
.backdrop-css {
  background: linear-gradient(180deg, rgba(217, 217, 217, 0.15) 0%, rgba(166, 173, 182, 0.15) 100%);
  backdrop-filter: blur(15px);
}

.custom-fade-in-duration {
  --fade-in-x: 90ms;
}
</style>