<script setup lang="ts">
import { RouterView } from 'vue-router';
import MenuComponent from '@/common/components/navbar/MenuComponent.vue';
import Submenu from '../components/navbar/Submenu.vue';
</script>

<template>
  <div class="flex flex-col min-h-screen">
    <!-- TOP NAV -->
    <div class="h-[66px] w-full px-2 py-1 flex justify-center">
      <div class="w-full max-w-[2048px]">
        <MenuComponent />
      </div>
    </div>

    <!-- PAGE CONTENT -->
    <div class="flex-1 flex flex-col items-center justify-start">
      <div class="w-full max-w-[2048px] flex flex-col flex-1 justify-start">
        <div class="relative w-full px-[32px] md:px-[60px] pb-[60px] flex-1 flex flex-col">
          <Submenu />
          <router-view v-slot="{ Component }">
            <Suspense timeout="0">
              <template #default>
                <component :is="Component" />
              </template>
              <template #fallback>
                <div />
              </template>
            </Suspense>
          </router-view>
        </div>
      </div>
    </div>
  </div>
</template>
