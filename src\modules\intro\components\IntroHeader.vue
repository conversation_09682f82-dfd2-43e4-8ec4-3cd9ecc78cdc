<script setup lang="ts">
import { IntroSteps } from '../types/intro-steps';
import BreadcrumbChip from '@/common/components/BreadcrumbChip.vue';

interface Props {
    currentStep: IntroSteps,
}

defineProps<Props>();
</script>

<template>
  <div
    class="h-[50px] flex items-center border-b border-b-[#B9B9B9]"
    :class="[currentStep <= IntroSteps.UPLOAD_VENDORS ? 'mb-[47.5px]' : 'mb-[30px]']"
  >
    <h3 class="text-[#0071E3] mr-[20px]">
      {{ $t('intro.title') }}
    </h3>
    <div class="flex items-center gap-[10px]">
      <BreadcrumbChip
        class="chip"
        :class="currentStep >= IntroSteps.UPLOAD_VENDORS ? 'shown' : ''"
        :text="[IntroSteps.UPLOAD_VENDORS, IntroSteps.UPLOADING].includes(currentStep) ? $t('intro.upload-companies') : $t('misc.upload')"
        :is-active="[IntroSteps.UPLOAD_VENDORS, IntroSteps.UPLOADING].includes(currentStep)"
      />
      <BreadcrumbChip
        class="chip"
        :class="currentStep >= IntroSteps.VERIFICATION ? 'shown' : ''"
        :text="currentStep <= IntroSteps.VERIFICATION ? $t('intro.verify-companies') : $t('intro.verify')"
        :is-active="currentStep === IntroSteps.VERIFICATION"
      />
      <BreadcrumbChip
        class="chip"
        :class="currentStep === IntroSteps.ADD_VENDORS ? 'shown' : ''"
        :text="$t('intro.add-companies')"
        :is-active="currentStep === IntroSteps.ADD_VENDORS"
      />
    </div>
  </div>
</template>

<style lang="css">
.chip {
  display: none;
  opacity: 0;
  transition: opacity 0.4s ease, display .2s ease allow-discrete;
  transition-delay: 0.5s;
  &.shown {
    opacity: 1;
    display: block;
  }
}

@starting-style {
  .chip.shown {
    opacity: 0;
  }
}
</style>
