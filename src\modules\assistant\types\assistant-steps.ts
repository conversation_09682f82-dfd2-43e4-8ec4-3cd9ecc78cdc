import { type AssistantRfpGeneral } from '@/modules/assistant/types/assistant-types.ts';

export enum AssistantGlobalSteps {
    STALL,
    RUNNING,
}

export enum AssistantGuideSteps {
    GENERAL,
    RESOURCE_AND_TECH,
    REVIEW,
}

type StepMap = {
    [key in AssistantRfpGeneral['step']]: AssistantGuideSteps;
}

export const AssistantStepMap: StepMap = {
  info: AssistantGuideSteps.GENERAL,
  resources: AssistantGuideSteps.RESOURCE_AND_TECH,
  final: AssistantGuideSteps.REVIEW,
};
