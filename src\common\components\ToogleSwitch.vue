<template>
  <label class="relative inline-flex items-center cursor-pointer">
    <input
      v-model="localValue"
      type="checkbox"
      class="sr-only peer"
      @change="onChange"
    >
    <div
      class="w-10 h-6 bg-gray-300 rounded-full transition-colors duration-300 peer-checked:bg-nio-green-text"
    >
      <span
        class="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-300 "
        :class="localValue ? 'translate-x-4' : ''"
      />
    </div>
  </label>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value: boolean): void;
}>();

const localValue = ref(props.modelValue);

watch(() => props.modelValue, newValue => {
  localValue.value = newValue;
});

const onChange = () => {
  emit('update:modelValue', localValue.value);
};
</script>
