<script lang="ts" setup>
import { isEmptyArray } from '@/common/utils/arrays';
import {
  type BlurPromise,
  type DynamicFormStructure,
  FormComponentMap, type FormErrors,
  FormReactiveTypeMap,
  type FormStandardData
} from '@/common/utils/forms.ts';
import { ref, unref } from 'vue';

type Props = {
  formStructure: DynamicFormStructure<FormStandardData>;
  errors: FormErrors;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});
const emit = defineEmits(['form-submit', 'nio-blur']);

const reactiveValues = ref<Record<string, any>>({});

const filterFormCallback = (input: FormStandardData[0]) => Object.hasOwn(FormComponentMap, input.component.id);

const initReactiveObject = () => {
  props.formStructure.data.forEach(input => {
    reactiveValues.value[input.component.payload_key] = (isEmptyArray(input.value) ? undefined : input.value) ?? input.component.default_value ?? FormReactiveTypeMap[input.component.id](input.component);
  });
};

const onSubmit = () => {
  emit('form-submit', unref(reactiveValues));
};

const emitNioBlur = async(blurPromise?: BlurPromise) => {
  if (props.disabled) {
    return;
  }
  emit('nio-blur', blurPromise);
};

defineExpose({
  getFormData: () => unref(reactiveValues),
  submit: onSubmit,
});

initReactiveObject();
</script>

<template>
  <div>
    <section class="flex flex-col gap-[2px]">
      <div
        v-for="(input, idx) in formStructure.data.filter(filterFormCallback)"
        :key="idx"
        class="bg-nio-grey-100 grid grid-cols-2 first:rounded-t-10 last:rounded-b-10"
      >
        <label :for="`${idx}-${input.component.payload_key}`" class="flex items-center p-6 2xl:p-9 tracking-[-0.32px] leading-[20px] font-semibold text-nio-grey-700">
          {{ input.name }}
        </label>
        <div class="p-6 2xl:p-9">
          <component
            :is="FormComponentMap[input.component.id]"
            v-model="reactiveValues[input.component.payload_key]"
            :disabled="disabled"
            :input-id="`${idx}-${input.component.payload_key}`"
            :error="errors?.find(error => error.payload_key === input.component.payload_key)"
            :input-data="input"
            @nio-blur="emitNioBlur"
          />
        </div>
      </div>
    </section>
    <slot name="form-bottom" />
  </div>
</template>
