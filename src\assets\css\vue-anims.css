@import 'open-props/easings';
@import 'open-props/animations';

/* Slide up 1s */
.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 1s var(--ease-out-3);
}
.slide-up-enter-from {
    opacity: 0;
    transform: translateY(30px);
    filter: blur(10px);
}
.slide-up-leave-to {
    opacity: 0;
    transform: translateY(-30px);
    filter: blur(10px);
}

/* Slide up 0.4s */
.slide-up-04-enter-active,
.slide-up-04-leave-active {
    transition: all 0.4s var(--ease-out-3);
}
.slide-up-04-enter-from {
    opacity: 0;
    transform: translateY(30px);
    filter: blur(10px);
}
.slide-up-04-leave-to {
    opacity: 0;
    transform: translateY(-30px);
    filter: blur(10px);
}

/* Slide down 0.4s delay 500ms */
.slide-down-04-delay-500-leave-active,
.slide-down-04-enter-active,
.slide-down-04-leave-active {
    transition: all 0.4s var(--ease-out-3);
}

.slide-down-04-delay-500-enter-active {
    transition: all 0.4s var(--ease-out-3);
    transition-delay: 0.5s;
}
.slide-down-04-delay-500-enter-from, .slide-down-04-enter-from {
    opacity: 0;
    transform: translateY(-30px);
    filter: blur(10px);
}
.slide-down-04-delay-500-leave-to, .slide-down-04-leave-to {
    opacity: 0;
    transform: translateY(30px);
    filter: blur(10px);
}


/* Fade in 0.4s */
.fade-in-04-enter-active,
.fade-in-04-leave-active {
    transition: all 0.4s var(--ease-out-3);
}
.fade-in-04-enter-from {
    opacity: 0;
    filter: blur(10px);
}
.fade-in-04-leave-to {
    opacity: 0;
    filter: blur(10px);
}

/* Fade in Xs */
.fade-in-x-enter-active,
.fade-in-x-leave-active {
    transition: all var(--fade-in-x, 0.4s) var(--ease-out-3);
}
.fade-in-x-enter-from {
    opacity: 0;
    filter: blur(10px);
}
.fade-in-x-leave-to {
    opacity: 0;
    filter: blur(10px);
}
