import { exec } from './cli-helper.js';

const apiCloneDir = process.env.NIO_API_GIT_CLONE_DIR || './api';

console.log(`➡️ Starting API from: ${apiCloneDir}`);

try {
  await exec('bash', ['-c', `cd "${apiCloneDir}" && docker compose up -d`]);
  console.log('✅ API containers started successfully.');
} catch (err) {
  console.error('❌ Failed to start API containers.');
  console.error(err.stderr || err.message);
  process.exit(1);
}
