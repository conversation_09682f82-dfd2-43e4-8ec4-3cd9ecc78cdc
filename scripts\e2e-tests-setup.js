import path from 'node:path';
import { fileURLToPath } from 'node:url';
import fs from 'fs-extra';
import { execCapture, runShellScript, isWindows } from './cli-helper.js';

if (isWindows && (!process.env.NIO_API_GIT_CLONE_DIR || process.env.NIO_API_GIT_CLONE_DIR === './api')) {
  console.warn('⚠️ NIO_API_GIT_CLONE_DIR env variable is not set. Defaulting to ./api');
  console.warn('⚠️ When running on Windows, it is recommended to set NIO_API_GIT_CLONE_DIR to a location under WSL filesystem to improve Docker performance.');
}

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const apiCloneDir = process.env.NIO_API_GIT_CLONE_DIR || 'api';
const apiGitBranch = process.env.NIO_API_GIT_BRANCH || 'develop';

try {
  const originalProjectDir = process.cwd();
  const envExamplePath = path.join(originalProjectDir, '.env.example');
  const envTestPath = path.join(originalProjectDir, '.env.test');

  console.log('➡️ Creating .env.test from .env.example');
  await fs.copy(envExamplePath, envTestPath);

  console.log('➡️ Setting up api project locally');
  await runShellScript(path.join(__dirname, 'api-setup.sh'), [apiCloneDir, apiGitBranch]);

  console.log('➡️ Reading OAuth client ID');
  const clientId = await execCapture('cat', [`${apiCloneDir}/.client-id.metch-client`]);

  // Replace or append the VITE_AUTH_CLIENT_ID line
  let envTestContent = await fs.readFile(envTestPath, 'utf8');
  if (envTestContent.match(/^VITE_AUTH_CLIENT_ID=/m)) {
    envTestContent = envTestContent.replace(/^VITE_AUTH_CLIENT_ID=.*/m, `VITE_AUTH_CLIENT_ID=${clientId}`);
  } else {
    envTestContent += `\nVITE_AUTH_CLIENT_ID=${clientId}\n`;
  }

  await fs.writeFile(envTestPath, envTestContent);
  console.log(`✅ Wrote Client ID to .env.test: ${clientId}`);
} catch (err) {
  console.error('❌ Error:', err.message);
  process.exit(1);
}
