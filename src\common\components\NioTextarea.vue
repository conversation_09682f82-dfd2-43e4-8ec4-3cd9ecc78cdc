<template>
  <div class="relative w-[calc(100%-120px)] h-[450px] mx-[60px] my-[30px] p-[20px] bg-[#F9F9F999] rounded-30">
    <textarea
      v-model="textValue"
      class="w-full h-full bg-transparent text-p-xl text-nio-grey-700 resize-none outline-hidden"
      placeholder="Start writing..."
      @input="emitTextChange"
    />
    <div
      class="absolute bottom-[15px] left-[15px] w-[40px] h-[40px] flex items-center justify-center rounded-full bg-nio-white cursor-pointer"
      @click="onFullscreenClick"
    >
      <FullscreenIcon />
    </div>
    <div
      class="absolute bottom-[15px] right-[15px] w-[40px] h-[40px] flex items-center justify-center rounded-full transition-colors"
      :class="{
        'bg-nio-white border-nio-blue-400': textValue.length === 0,
        'bg-nio-blue-800 border-transparent cursor-pointer': textValue.length > 0
      }"
      @click="onArrowClick"
    >
      <ArrowIcon
        class="fill-current"
        :class="{
          'text-nio-grey-500': textValue.length === 0,
          'text-white': textValue.length > 0
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import FullscreenIcon from '@/assets/icons/fullscreen-icon.svg';
import ArrowIcon from '@/assets/icons/arrow-icon.svg';

const textValue = ref('');

const emit = defineEmits(['textChange', 'sendText']);

const emitTextChange = () => {
  emit('textChange', textValue.value);
};

const onArrowClick = () => {
  if (textValue.value.trim().length > 0) {
    emit('sendText', textValue.value);
  }
};

const onFullscreenClick = () => {
// TODO: Implement full screen functionality in the future
};
</script>
