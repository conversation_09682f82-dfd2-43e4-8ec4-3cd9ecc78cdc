<script setup lang="ts">
import { ref, defineProps, computed } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  color?: 'blue' | 'dark' ;
}>();
const isActive = ref(props.modelValue);

// computed
const activeClass = computed(() => {
  switch (props.color || 'blue') {
    case 'blue':
      return 'bg-nio-blue-800 text-white border-nio-blue-200';
    case 'dark':
      return 'bg-nio-grey-900 text-white border-nio-grey-200';
    default:
      return 'bg-nio-blue-800 text-white border-nio-blue-200';
  }
});

const inactiveClass = computed(() => {
  switch (props.color || 'blue') {
    case 'blue':
      return 'bg-white text-nio-blue-800 border-white hover:bg-nio-blue-100';
    case 'dark':
      return 'bg-white text-nio-grey-900 border-white hover:bg-nio-grey-100';
    default:
      return 'bg-white text-nio-blue-800 border-white';
  }
});

// emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

// methods
const toggle = () => {
  isActive.value = !isActive.value;
  emit('update:modelValue', isActive.value);
};

</script>

<template>
  <div class="rounded-full text-sm transition-all duration-200 flex items-center cursor-pointer bg-white select-none" @click="toggle">
    <button data-testid="nio-switch-left" class="px-4 py-1.5 rounded-full transition-all duration-200 border" :class="isActive ? inactiveClass : activeClass">
      <slot name="left" />
    </button>
    <button data-testid="nio-switch-right" class="px-4 py-1.5 rounded-full transition-all duration-200 border" :class="isActive ? activeClass : inactiveClass">
      <slot name="right" />
    </button>
  </div>
</template>
