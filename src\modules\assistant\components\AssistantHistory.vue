<script lang="ts" setup>
import ArrowIcon from '@/assets/icons/arrow-icon-2.svg';
import { DateTime } from 'luxon';
import { ref, computed, useTemplateRef } from 'vue';
import { useInfiniteScroll } from '@vueuse/core';
import { nioAxios } from '@/axios.ts';
import type { DateCategory, GroupedHistoryItems, HistoryItem } from '@/modules/assistant/types/assistant-history.ts';
import { categoryConfig } from '@/config/assistant.ts';
import Loader from '@/common/components/Loader.vue';
import { routeMap } from '@/modules/assistant/routes';
import AssistantSideHistory from '@/modules/assistant/components/AssistantSideHistory.vue';
import Drawer from 'primevue/drawer';

const data = ref<HistoryItem[]>([]);
const page = ref(1);
const loading = ref(false);
const hasMore = ref(true);
const historyVisible = ref(false);
const scrollAreaEl = useTemplateRef<HTMLDivElement>('scrollAreaEl');

const fetchData = async() => {
  if (loading.value || !hasMore.value) {
    return;
  }
  loading.value = true;

  try {
    const response = await nioAxios.get('/enterprise/assistant/rfp', { params: { page: page.value } });
    const newData = response.data.data.map((item: any) => ({
      id: item.id,
      title: item.title,
      date: item.created_at,
      tender_id: item.tender_id
    }));

    data.value.push(...newData);
    data.value.sort((a, b) => DateTime.fromISO(b.date).toMillis() - DateTime.fromISO(a.date).toMillis());

    // Check if there are more pages
    hasMore.value = !!response.data.links.next;
    page.value++;
  } catch (error) {
    console.error('Error fetching data:', error);
  } finally {
    loading.value = false;
  }
};

const groupByDateCategory = (items: HistoryItem[]): Partial<GroupedHistoryItems> => {
  const grouped = items.reduce<GroupedHistoryItems>(
    (acc, item) => {
      const itemDate = DateTime.fromISO(item.date).startOf('day');

      const category: DateCategory =
        (Object.entries(categoryConfig) as [DateCategory, (date: DateTime) => boolean][])
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          .find(([_, isMatch]) => isMatch(itemDate))?.[0] || 'Previous 7 days';

      acc[category] = acc[category] || [];
      acc[category].push(item);

      return acc;
    },
    { Today: [], Yesterday: [], 'Previous 7 days': [] }
  );

  // Remove empty categories
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return Object.fromEntries(Object.entries(grouped).filter(([_, items]) => items.length > 0));
};

const groupedData = computed(() => groupByDateCategory(data.value));

useInfiniteScroll(scrollAreaEl, fetchData, {
  distance: 10,
  canLoadMore: () => {
    return true;
  }
});

</script>

<template>
  <section class="bg-nio-grey-background-90 rounded-15 py-4 pl-4 pr-1 pb-8 flex flex-col max-h-full h-[500px]">
    <div class="mb-8 py-1 px-2 rounded-30 border border-nio-grey-200 w-fit">
      <h3 class="text-nio-grey-700 text-sm font-medium -tracking-028px leading-4">
        {{ $t('assistant.history') }}
      </h3>
    </div>
    <div ref="scrollAreaEl" class="min-h-0 flex-[1_1_0] overflow-y-auto custom-scrollbar-01 pr-3">
      <div v-for="(items, category, idxCat) in groupedData" :key="category" class="last:[&_.custom-divider]:hidden">
        <h4 :class="idxCat > 0 ? 'mt-4' : ''" class="text-nio-grey-500 font-semibold text-sm mb-4">
          {{ category }}
        </h4>

        <div class="flex flex-col gap-2.5">
          <div v-for="(rfp, idx) in items" :key="idx" class="flex flex-col gap-1.5">
            <router-link :to="{name: routeMap.assistantRfp.name, params: {id: rfp.id}}" class="flex items-center group gap-2 cursor-pointer max-w-[600px]">
              <div
                class="min-w-5 min-h-5 w-5 h-5 p-[5px] rounded-full flex items-center justify-center"
                :class="{
                  'group-hover:bg-nio-grey-300 bg-nio-grey-100': !rfp.tender_id,
                  'bg-nio-green-text': rfp.tender_id
                }"
              >
                <ArrowIcon
                  v-if="rfp.tender_id"
                  class="text-nio-grey-100 [&_path]:fill-nio-grey-100 group-hover:!fill-nio-white"
                />
              </div>
              <div class="truncate font-medium tracking-[-0.32px] leading-5 flex-1" :title="rfp.title ?? $t('misc.no-name')">
                {{ rfp.title ?? $t('misc.no-name') }}
              </div>
            </router-link>
            <div v-if="category === 'Today'" class="text-xs text-nio-grey-300">
              {{ DateTime.fromISO(rfp.date).toFormat('dd.M. / HH:mm') }}
            </div>
          </div>
        </div>
        <div class="custom-divider h-[1px] bg-nio-grey-100 my-4" />
      </div>
      <div v-if="loading" class="flex flex-col items-center mt-2 text-nio-grey-400">
        <Loader v-if="loading" />
      </div>
    </div>
    <div class="text-nio-blue-800 text-sm font-medium -tracking-028px leading-[14px] mt-8 cursor-pointer hover:text-nio-blue-600-hover" @click="historyVisible = true">
      View All
    </div>
  </section>

  <Drawer
    v-model:visible="historyVisible"
    position="left"
    :show-close-icon="false"
    :block-scroll="true"
    class="w-[536px] h-full bg-nio-grey-background-90 transition-transform duration-300 ease-in-out"
  >
    <div ref="scrollAreaEl" class="min-h-0 flex-[1_1_0] overflow-y-auto custom-scrollbar-01 h-full">
      <AssistantSideHistory />
    </div>
  </Drawer>
</template>
