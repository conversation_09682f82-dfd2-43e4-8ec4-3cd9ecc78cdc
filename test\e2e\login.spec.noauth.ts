import { test, expect } from '@playwright/test';
import { clientUser } from './fixtures/users';

test('user can sign in by pressing Enter', async({ page }) => {
  await page.goto('/');
  await expect(page).toHaveTitle(/Sign in/);

  const emailInput = page.getByPlaceholder(/E-?mail/);
  await emailInput.fill(clientUser.email);
  const passwordInput = page.getByPlaceholder('Password');
  await passwordInput.fill(clientUser.password);
  await passwordInput.press('Enter');

  await page.waitForURL('/assistant');
  await expect(page.locator('body')).toHaveText(/Ignác/);
});
