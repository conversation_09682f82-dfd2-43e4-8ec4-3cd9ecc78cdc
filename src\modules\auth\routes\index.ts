import type { NIORouteMap } from '@/router';

export const routeMap = {
  authorize: {
    path: '/authorize',
    name: 'authorize',
    meta: {
      i18nTitle: 'auth.login',
      authNotRequired: true,
      headTitlePrefix: 'Auth',
    },
  },
} satisfies NIORouteMap;

export const routeIndex = {
  authorize: {
    ...routeMap.authorize,
    component: () => import('../pages/Authorize.vue'),
  },
};

export default Object.values(routeIndex);
