import type { NIORouteMap } from '@/router';

export const routeMap = {
  assistant: {
    path: '/assistant',
    name: 'assistant',
    meta: {
      i18nTitle: 'assistant.title',
      headTitlePrefix: 'Assistant',
    },
    children: {
      assistantDashboard: {
        path: '',
        name: 'assistantDashboard',
        meta: {
          i18nTitle: 'assistant.title',
          headTitlePrefix: 'Assistant',
        }
      },
      assistantHistory: {
        path: 'history',
        name: 'assistantHistory',
        meta: {
          i18nTitle: 'assistant.title',
          headTitlePrefix: 'Assistant',
        }
      }
    },
  },
  assistantRfp: {
    path: '/assistant/rfp/:id',
    name: 'assistantRfp',
    meta: {
      i18nTitle: 'assistant.title',
      headTitlePrefix: 'Assistant',
    },
  },
} satisfies NIORouteMap;

export const routeIndex = [
  {
    ...routeMap.assistant,
    redirect: { name: 'assistantDashboard' },
    children: [
      {
        ...routeMap.assistant.children.assistantDashboard,
        component: () => import('../pages/AssistantIntro.vue'),
      },
      {
        ...routeMap.assistant.children.assistantHistory,
        component: () => import('../pages/AssistantHistoryPage.vue'),
      },
    ],
  },
  {
    ...routeMap.assistantRfp,
    component: () => import('../pages/AssistantRFPPage.vue'),
  },
];

export default routeIndex;
