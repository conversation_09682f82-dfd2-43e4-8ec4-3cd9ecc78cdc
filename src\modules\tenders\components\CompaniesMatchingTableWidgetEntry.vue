<script setup lang="ts">
import { computed, inject } from 'vue';
import type { Company } from '../types/tenders-types';
import { useTenderStore } from '../stores/tenders-store';
import { CompanyCategory, type ProfileResponse } from '@/modules/auth/types/auth-types';
import { tenderScoreToPoints, tenderScoreToText } from '@/common/utils/score';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import { DateTime } from 'luxon';
import InviteChip from '@/modules/tenders/components/InviteChip.vue';

interface Props {
  company: Company | ProfileResponse['workspaces'][0]['companies'][0];
  isOtherVendors?: boolean;
  tenderId: string;
  index: number;
  invited?: boolean;
  allowInvite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  allowInvite: true,
});

const tenderStore = useTenderStore();
const invitedCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => Boolean(vendor.sent_at)).map(vendor => vendor.id));
const pendingCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => !vendor.sent_at).map(vendor => vendor.id));
const isVendorInvited = computed(() => invitedCompanyIds.value.includes(props.company.id));
const isVendorPending = computed(() => pendingCompanyIds.value.includes(props.company.id));
const openInviteDrawer = inject<() => void>('openInviteSidebar');

const toggleInvite = () => {
  if (!props.allowInvite || isVendorInvited.value) {
    return;
  }
  if (isVendorPending.value) {
    tenderStore.uninviteCompany(props.tenderId, props.company.id);
  } else {
    tenderStore.inviteCompany(props.tenderId, props.company.id);
    openInviteDrawer?.();
  }
};

const sentAt = computed(() => {
  const invited = tenderStore.invitedVendors.find(v => v.id === props.company.id);
  return invited?.sent_at ?? null;
});

</script>

<template>
  <div
    class="flex w-full max-w-full items-center justify-between group relative transition-all duration-300 rounded-20"
  >
    <div class="flex-1 p-3 transition-opacity duration-200" :class="allowInvite ? 'group-hover:opacity-30' : ''">
      <div class="flex items-start space-x-sm">
        <span class="text-[12px] font-paragraph text-nio-grey-300 leading-normal mr-2 pt-1">
          {{ index }}
        </span>
        <div>
          <div class="flex items-center gap-1">
            <template v-if="allowInvite">
              <InviteChip :status="isVendorInvited ? 'invited' : (isVendorPending ? 'pending' : 'not-invited')" />
            </template>
          </div>
          <div class="flex items-center gap-1">
            <div
              v-if="[CompanyCategory.AGENCY, CompanyCategory.ENTERPRISE].includes((company as any).category)"
              class="text-xs w-fit px-1.5 py-0.5 rounded-sm"
              :class="{
                'bg-nio-green-text/20 text-green-700': (company as any).category === CompanyCategory.AGENCY,
                'bg-nio-blue-500/15 text-nio-blue-800': (company as any).category === CompanyCategory.ENTERPRISE
              }"
            >
              {{ (company as any).category === CompanyCategory.ENTERPRISE ? 'E' : 'A' }}
            </div>
            <p class="font-paragraph text-[18px] leading-normal text-nio-grey-900 line-clamp-1 break-all">
              {{ company?.name }}
            </p>
          </div>
          <p class="font-paragraph text-sm leading-normal text-nio-grey-500 line-clamp-1 break-all">
            {{ (company as Company)?.country }}, {{ (company as Company)?.headquarters }}
          </p>
        </div>
      </div>
    </div>

    <div v-if="!isOtherVendors" class="w-fit py-sm px-md text-left relative transition-opacity duration-200" :class="allowInvite ? 'group-hover:opacity-30' : ''">
      <div class="flex items-center space-x-sm justify-end">
        <NioScoreProgress
          :points="tenderScoreToPoints((company as Company)?.match_details?.overall_score, 'total')"
          :title="tenderScoreToText((company as Company)?.match_details?.overall_score)"
          size="sm"
        />
      </div>
    </div>

    <div v-if="allowInvite" class="absolute top-0 left-0 w-full h-full rounded-20 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-200">
      <div v-if="sentAt" class="hidden group-hover:block px-4 py-2 bg-[#E5E5EA] text-nio-grey-700 text-[14px] rounded-50">
        {{ DateTime.fromISO(sentAt).toFormat('dd LLLL yyyy') }}
      </div>
      <button
        v-else
        class="hidden group-hover:block px-4 py-2 text-white text-[14px] rounded-50 transition-colors cursor-pointer"
        :class="isVendorPending ? 'bg-nio-red-500 hover:bg-nio-red-500/80' : 'bg-nio-green-text hover:bg-nio-green-text/80'"
        @click="toggleInvite"
      >
        {{ isVendorPending ? 'Cancel invite' : 'Invite' }}
      </button>
    </div>
  </div>
</template>

<style scoped>
.shadow-custom-box-shadow-01 {
  box-shadow: 0 71px 20px 0 rgba(207, 207, 207, 0.00),
  0 45px 18px 0 rgba(207, 207, 207, 0.01),
  0 25px 15px 0 rgba(207, 207, 207, 0.05),
  0 11px 11px 0 rgba(207, 207, 207, 0.09),
  0 3px 6px 0 rgba(207, 207, 207, 0.10);
}
</style>
