<script setup lang="ts">
import { ref, computed, reactive, nextTick, onMounted, onBeforeUnmount, watch } from 'vue';
import NioSearch from '@/common/components/UI/NioSearch.vue';
import NioCheckbox from '@/common/components/UI/NioCheckbox.vue';
import ArrowGreyDown from '@/assets/icons/arrow-grey-down.svg';
import ArrowBlackUp from '@/assets/icons/arrow-black-up.svg';
import type { DynamicFormItemData, FormErrors, FormSelectOption } from '@/common/utils/forms.ts';
import { nioAxios } from '@/axios.ts';
import { debounce } from 'lodash-es';
import DeleteIcon from '@/assets/icons/x-icon.svg';
import ChevronTopIcon from '@/assets/icons/chevron-top.svg';
import { fetchedOptionsCache } from '@/modules/assistant/stores/FetchedOptionsCache';

const modelValue = defineModel<undefined | number | number[] | string | string[]>({ required: true });
const emit = defineEmits(['nio-blur', 'optionsUpdated']);

type Props = {
  placeholder?: string;
  searchPlaceholder?: string;
  inputData: DynamicFormItemData,
  error?: FormErrors[0],
  errorPosition?: 'left' | 'right',
  inputId?: string,
  disabled?: boolean,
  collectionIdentifier?: number,
  inheritedOptions?: DynamicFormItemData['component']['options'],
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: undefined,
  searchPlaceholder: undefined,
  error: undefined,
  errorPosition: 'left',
  inputId: undefined,
  disabled: false,
  collectionIdentifier: undefined,
  inheritedOptions: undefined,
});

const options = ref(props.inheritedOptions ?? props.inputData.component.options!);
const allowMultiple = ref(props.inputData.component.id === 'multi-select');

const isOpen = ref(false);
const searchQuery = ref('');
const dropdownButton = ref<HTMLElement | null>(null);
const containerRef = ref<HTMLElement | null>(null);
const selectedOptionsMap = reactive<Record<string, boolean>>({});
const selectedChipsExpanded = ref(true);
const fetchedOptionsMap = ref(new Map<string | number, FormSelectOption>());

const endpointUrl = props.inputData.component.options_endpoint?.url;
const payloadKey = props.inputData.component.payload_key;
const selectedOptionsLabelsMap = props.inputData.component.selected_options?.reduce<Record<string, string>>((acc, option) => ({
  ...acc,
  [option.value]: option.label
}), {}) ?? {};

let controller = new AbortController();

[
  ...(props.inheritedOptions ?? []),
  ...(props.inputData?.component?.options ?? []),
  ...(props.inputData?.component?.selected_options ?? []),
  ...Array.from(fetchedOptionsCache.get(payloadKey, endpointUrl)?.values() ?? [])
].forEach(option => {
  fetchedOptionsMap.value.set(option.value, option);
});

emit('optionsUpdated', Array.from(fetchedOptionsMap.value.values()));

const changedSearchQuery = async(value: string) => {
  controller.abort();
  controller = new AbortController();

  if (!value || value.length < 2) {
    options.value = props.inputData.component.options!;
    return;
  }

  try {
    // TODO(Marian Rusnak): Check whether to use & or ? in the URL.
    const response = await nioAxios.get(`${endpointUrl}&search=${encodeURIComponent(value)}`, {
      signal: controller.signal,
    });
    const endpointMapping = props.inputData.component.options_endpoint?.mapping;

    const newOptions = response.data?.data?.map((option: any) => {
      const value = option[endpointMapping!.value!];
      const label = option[endpointMapping!.label!];
      return { value, label };
    });

    const newMap = new Map<string | number, FormSelectOption>();
    newOptions.forEach((option: FormSelectOption) => {
      fetchedOptionsMap.value.set(option.value, option);
      newMap.set(option.value, option);
    });

    const payloadKey = props.inputData.component.payload_key;
    fetchedOptionsCache.merge(payloadKey, endpointUrl, newMap);

    options.value = newOptions;
    emit('optionsUpdated', Array.from(fetchedOptionsMap.value.values()));

  } catch {
    console.error('Failed to search');
  }
};

const debouncedChangedSearchQuery = debounce(changedSearchQuery, 440);

watch(
  () => modelValue.value,
  newValue => {
    if (allowMultiple.value && Array.isArray(newValue)) {
      Object.keys(selectedOptionsMap).forEach(key => (selectedOptionsMap[key] = false));
      newValue.forEach(val => {
        if (typeof val === 'string') {
          selectedOptionsMap[val] = true;
        }
      });
    }
  },
  { immediate: true }
);

if (endpointUrl) {
  watch(
    () => searchQuery.value,
    newValue => {
      debouncedChangedSearchQuery(newValue);
    },
    { immediate: true }
  );
}

const filteredOptions = computed(() => options.value?.filter(option =>
  option.label.toLowerCase().includes(searchQuery.value.toLowerCase())
) ?? []);

const toggleDropdown = () => {
  if (props.disabled) {
    return;
  }
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    nextTick(() => dropdownButton.value?.focus());
  }
};

const selectOption = (option: { label: string; value: string | number; disabled?: boolean }) => {
  if (option.disabled) {
    return;
  }

  if (allowMultiple.value) {
    selectedOptionsMap[option.value] = !selectedOptionsMap[option.value];
    selectedOptionsLabelsMap[option.value] = option.label;
    modelValue.value = Object.keys(selectedOptionsMap).filter(key => selectedOptionsMap[key]);
  } else {
    if (modelValue.value === option.value) {
      modelValue.value = '';
    } else {
      modelValue.value = option.value;
    }
    isOpen.value = false;
  }
  emit('nio-blur');
};

const selectedLabel = computed(() => {
  if (allowMultiple.value) {
    const selectedCount = Object.keys(selectedOptionsMap).filter(key => selectedOptionsMap[key]).length;
    switch (selectedCount) {
      case 0:
        return props.placeholder || 'Select';
      case 1:
        return (() => {
          const selectedKey = Object.keys(selectedOptionsMap).find(key => selectedOptionsMap[key]);
          const selectedOption = fetchedOptionsMap.value.get(selectedKey!);
          const selectedLabel = selectedOption?.label || '';
          return selectedLabel;
        })();
      default:
        return `${selectedCount} items selected`;
    }
  } else {
    const selected = options.value?.find(option => option.value === modelValue.value);
    return selected ? selected.label : props.placeholder || 'Select an option';
  }
});

const hasValue = computed(() => {
  if (allowMultiple.value) {
    return Object.keys(selectedOptionsMap).some(key => selectedOptionsMap[key]);
  }
  return modelValue.value !== '';
});

const clickOutsideHandler = (event: MouseEvent) => {
  if (containerRef.value && !containerRef.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
};

watch(() => props.inheritedOptions, (newVal, oldVal) => {
  if (JSON.stringify(newVal) === JSON.stringify(oldVal)) {
    return;
  }
  if (!props.inputData?.component?.options_from?.payload_key) {
    return;
  }
  options.value = props.inheritedOptions ?? [];
  const availableValueMap = props.inheritedOptions?.map(option => option.value) ?? [];
  if (Array.isArray(modelValue.value)) {
    modelValue.value = modelValue.value.filter(val => availableValueMap.includes(val)) as (string[] | number[]);
  } else if (!availableValueMap.includes(modelValue.value as string)) {
    modelValue.value = undefined;
  }
});

onMounted(() => {
  document.addEventListener('click', clickOutsideHandler);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', clickOutsideHandler);
});
</script>

<template>
  <div ref="containerRef" class="relative">
    <div
      v-if="error?.message"
      :id="`${inputData.component.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-6 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ error?.message }}
    </div>
    <div
      v-if="error?.subKeys"
      :id="`${inputData.component.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-5 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : 'left-0'"
    >
      {{ Object.values(error.subKeys).join(', ') }}
    </div>
    <button
      :id="inputId"
      ref="dropdownButton"
      type="button"
      :class="[
        'min-w-[92px] flex items-center justify-between px-4 py-2 border border-gray-300 rounded-[24px] font-medium',
        !disabled && 'focus:outline-none focus:border-nio-blue-400 hover:bg-nio-white hover:text-nio-black-900',
        (isOpen || hasValue) ? 'bg-white text-nio-black-900' : 'bg-nio-grey-background text-nio-grey-500',
        error?.message ? 'border-nio-red-background' : 'border-gray-300',
        disabled ? 'bg-nio-grey-background-60 text-nio-grey-500' : 'cursor-pointer'
      ]"
      :aria-describedby="`${inputData.component.payload_key}-errors${collectionIdentifier ?? ''}`"
      @click="toggleDropdown"
    >
      <div class="flex items-center justify-center w-full gap-2">
        <span>{{ selectedLabel }}</span>
        <span class="flex items-center justify-center h-full">
          <ArrowBlackUp
            v-if="isOpen"
            class="w-3 h-3"
            style="pointer-events: auto;"
            @click.stop="toggleDropdown"
          />
          <ArrowGreyDown
            v-else
            class="w-3 h-3"
            style="pointer-events: auto;"
            @click.stop="toggleDropdown"
          />
        </span>
      </div>
    </button>

    <div
      v-if="isOpen"
      class="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-[10px] z-10 p-[6px] pb-[18px]"
    >
      <NioSearch v-model="searchQuery" class="mb-2" :placeholder="searchPlaceholder || 'Search'" />

      <template v-if="allowMultiple && Object.keys(selectedOptionsMap).some(key => selectedOptionsMap[key])">
        <ul v-if="selectedChipsExpanded" class="grid gap-1 grid-cols-1 max-h-28 custom-scrollbar-01 overflow-y-auto pr-1.5">
          <li
            v-for="key in Object.keys(selectedOptionsMap).filter(key => selectedOptionsMap[key])"
            :key="key"
            class="flex items-center justify-between w-full px-2 py-1 text-center rounded-5 bg-nio-grey-100 has-[button:hover]:bg-red-200/80 text-nio-grey-500 backdrop-blur-sm text-xs font-semibold -tracking-028px gap-1"
          >
            <div
              class="line-clamp-1 break-all"
              :title="fetchedOptionsMap.get(key)?.label"
            >
              {{ fetchedOptionsMap.get(key)?.label }}
            </div>
            <button
              v-if="!disabled"
              type="button"
              class="cursor-pointer p-0.5"
              title="Delete"
              @click.stop="selectOption({ label: selectedOptionsLabelsMap[key], value: key })"
            >
              <DeleteIcon class="text-nio-grey-500" />
            </button>
          </li>
        </ul>
        <div class="flex flex-col items-center mt-2 -mb-2 cursor-pointer" @click.stop="selectedChipsExpanded = !selectedChipsExpanded">
          <div v-if="!selectedChipsExpanded" class="text-xs">
            Show selected
          </div>
          <div v-else class="text-xs">
            Shrink selected
          </div>
          <div class="pb-1.5">
            <ChevronTopIcon :class="selectedChipsExpanded ? '' : 'rotate-180'" />
          </div>
        </div>
      </template>

      <ul class="max-h-60 overflow-y-auto mt-[6px] custom-scrollbar-01">
        <li
          v-for="option in filteredOptions"
          :key="option.value"
          class="flex items-center px-4 py-2 cursor-pointer text-[14px] font-medium transition-colors !text-black"
          :class="{
            'text-gray-400 cursor-not-allowed': option.disabled,
            'text-nio-black-900': !option.disabled && (
              allowMultiple ? selectedOptionsMap[option.value] : option.value === modelValue
            ),
            'text-nio-grey-500 hover:text-nio-black-900': !option.disabled && !(
              allowMultiple ? selectedOptionsMap[option.value] : option.value === modelValue
            )
          }"
          @click.stop="selectOption(option)"
        >
          <div class="flex items-center w-full cursor-pointer gap-2">
            <NioCheckbox
              v-if="allowMultiple"
              v-model="selectedOptionsMap[option.value]"
              :label="option.label"
              :disabled="option.disabled"
            />
            <span
              v-else
              :class="[
                !allowMultiple && option.value === modelValue ? 'text-nio-blue-800' : 'text-black'
              ]"
            >
              {{ option.label }}
              <template v-if="option.description">
                <br>
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span class="text-xs font-light" v-html="option.description" />
              </template>
            </span>
          </div>
        </li>
        <li
          v-if="inputData?.component?.options_from?.label && filteredOptions.length < 1"
          class="flex items-center px-4 py-2 text-[12px] font-medium transition-colors !text-black"
        >
          <div class="w-full text-center">
            <span>Please, fill<br><span class="underline">{{ inputData?.component?.options_from?.label }}</span></span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.animate-height-enter-active,
.animate-height-leave-active {
  transition: all 0.2s;
  max-height: 500px;
}
.animate-height-enter,
.animate-height-leave-to
{
  opacity: 0;
  max-height: 0px;
}
</style>
