<template>
  <PageContainerWrapper>
    <div class="w-full transition-all duration-500 ease-[var(--ease-out-3)]">
      <main class="flex justify-center items-center" />
      <div class="flex justify-center relative">
        <div v-if="isLoading" class="min-h-[1000px]" />
        <Transition leave>
          <div v-if="isLoading" class="absolute top-0 left-0 w-full h-full">
            <div role="list" aria-label="Tenders list" class="grid gap-3 w-full grid-cols-[repeat(auto-fill,minmax(680px,1fr))]">
              <TenderCardSkeleton
                v-for="i in 5"
                :key="i"
              />
            </div>
          </div>
        </Transition>

        <Transition name="transition-fade-in">
          <div
            v-if="!isLoading"
            role="list"
            aria-label="Tenders list"
            class="grid gap-3 w-full grid-cols-[repeat(auto-fill,minmax(680px,1fr))]"
          >
            <TenderCard
              v-for="tender in tenders"
              :key="tender.id"
              :tender="tender"
              role="listitem"
            />
            <div v-if="!tenders.length" class="rounded-3xl bg-nio-grey-background py-4 pl-4 pr-1 col-span-full text-center">
              <div>
                You haven't created any tenders yet. It's great to get started using our
                <router-link class="text-nio-blue-800 hover:text-nio-blue-800/80" :to="{name: routeMap.assistant.name}">
                  Assistant!
                </router-link>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  </PageContainerWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TenderCard from '@/modules/tenders/components/TenderCard.vue';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import type { Tender } from '@/modules/tenders/types/tenders-types';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import { routeMap } from '@/modules/assistant/routes';
import { handleTenderError } from '@/modules/tenders/handlers/error';
import TenderCardSkeleton from '@/modules/tenders/components/TenderCardSkeleton.vue';

const tenderStore = useTenderStore();
const tenders = ref<Tender[]>([]);
const isLoading = ref(true);

const fetchTenders = async() => {
  try {
    tenders.value = await tenderStore.fetchTenders();
  } catch (error) {
    handleTenderError(error);
    isLoading.value = false;
  } finally {
    isLoading.value = false;
  }
};

fetchTenders();
</script>
