import { defineStore } from 'pinia';
import { reactive, ref } from 'vue';
import { nioAxios } from '@/axios';
import type { Company, Tender } from '@/modules/tenders/types/tenders-types';
import type { MatchStatus, MatchStatusResponse } from '@/modules/tenders/types/match-status';
import { startPolling } from '@/common/utils/polling';
import { CompaniesFilter, MatchingStatus } from '@/modules/tenders/types/tenders-enums';

export const useTenderStore = defineStore('tender-store', () => {
  const tenders = ref<Tender[]>([]);
  const pollingInstances = ref(new Map<string, { stop: () => void }>());
  const specificMatchingResult = ref<MatchStatus | null>(null);
  const marketplaceMatchingResult = ref<MatchStatus | null>(null);
  const matchingStatus = reactive<Record<CompaniesFilter, MatchingStatus | null>>({
    [CompaniesFilter.Selection]: null,
    [CompaniesFilter.Marketplace]: null,
  });
  const invitedVendors = ref<Company[]>([]);
  const invitedVendorIds = ref<string[]>([]);

  const fetchTenders = async(): Promise<Tender[]> => {
    const response = await nioAxios.get<{ data: Tender[] }>('/enterprise/tenders');
    tenders.value = response.data.data;
    return response.data.data;
  };

  const fetchTenderDetail = async(tenderId: string): Promise<Tender> => {
    const response = await nioAxios.get<{ data: Tender }>(`/enterprise/tenders/${tenderId}`);
    return response.data.data;
  };

  const startMatchingPoll = (
    tenderId: string,
    interval = 5000,
    companiesFilter: CompaniesFilter
  ): Promise<MatchStatus | null> => {
    const MAX_POLLING_TIME = 240000;
    const MAX_EMPTY_RESPONSES = 5;
    const pollKey = `${tenderId}-${companiesFilter}`;

    return new Promise((resolve, reject) => {
      matchingStatus[companiesFilter] = null;

      if (companiesFilter === CompaniesFilter.Selection) {
        specificMatchingResult.value = null;
      } else {
        marketplaceMatchingResult.value = null;
      }

      pollingInstances.value.get(pollKey)?.stop();

      let pollingTimeout: ReturnType<typeof setTimeout> | null = null;
      let emptyResponseCount = 0;

      pollingTimeout = setTimeout(() => {
        pollingInstances.value.get(pollKey)?.stop();
        pollingInstances.value.delete(pollKey);
        matchingStatus[companiesFilter] = MatchingStatus.Failed;
        resolve(null);
      }, MAX_POLLING_TIME);

      const polling = startPolling<MatchStatusResponse>({
        url: `/enterprise/tenders/${tenderId}/matching?companies_filter=${companiesFilter}`,
        timeout: interval,
        onSuccess: data => {
          const response = data?.data?.[0];
          const isEmpty = !response || (Array.isArray(data?.data) && data.data.length === 0);

          if (isEmpty) {
            // Matching just started, that's why response is empty.
            matchingStatus[companiesFilter] = MatchingStatus.InProgress;
            emptyResponseCount += 1;

            if (emptyResponseCount >= MAX_EMPTY_RESPONSES) {
              matchingStatus[companiesFilter] = MatchingStatus.NoVendorsMatched;
              pollingInstances.value.get(pollKey)?.stop();
              pollingInstances.value.delete(pollKey);
              if (pollingTimeout) {
                clearTimeout(pollingTimeout);
              }
              resolve(null);
            }

            return;
          }

          emptyResponseCount = 0;

          const vendors = (response.companies ?? []).sort((a, b) => {
            const aScore = a?.match_details?.overall_score ?? 0;
            const bScore = b?.match_details?.overall_score ?? 0;
            return bScore - aScore;
          });

          if (response.status === MatchingStatus.Completed) {
            if (vendors.length === 0) {
              matchingStatus[companiesFilter] = MatchingStatus.NoVendorsMatched;
              pollingInstances.value.get(pollKey)?.stop();
              pollingInstances.value.delete(pollKey);
              if (pollingTimeout) {
                clearTimeout(pollingTimeout);
              }
              resolve(null);
              return;
            }

            matchingStatus[companiesFilter] = MatchingStatus.Completed;

            const top = vendors.slice(0, 4);
            const topIds = new Set(top.map((v: any) => v.id));
            const other = vendors.filter((v: any) => v?.id !== undefined && !topIds.has(v.id));

            response.topVendors = top;
            response.otherVendors = other;

            if (companiesFilter === CompaniesFilter.Selection) {
              specificMatchingResult.value = response;
            } else {
              marketplaceMatchingResult.value = response;
            }

            pollingInstances.value.get(pollKey)?.stop();
            pollingInstances.value.delete(pollKey);
            if (pollingTimeout) {
              clearTimeout(pollingTimeout);
            }
            resolve(response);
          } else if (response.status === MatchingStatus.InProgress || !response.status) {
            matchingStatus[companiesFilter] = MatchingStatus.InProgress;
          } else if (response.status === MatchingStatus.Failed) {
            matchingStatus[companiesFilter] = MatchingStatus.Failed;
            pollingInstances.value.get(pollKey)?.stop();
            pollingInstances.value.delete(pollKey);
            if (pollingTimeout) {
              clearTimeout(pollingTimeout);
            }
            resolve(null);
          }
        },
        onError: error => {
          matchingStatus[companiesFilter] = MatchingStatus.Failed;
          pollingInstances.value.get(pollKey)?.stop();
          pollingInstances.value.delete(pollKey);
          if (pollingTimeout) {
            clearTimeout(pollingTimeout);
          }
          reject(error ?? new Error('Polling failed'));
        },
      });

      pollingInstances.value.set(pollKey, polling);
    });
  };

  const stopMatchingPoll = (tenderId: string, companiesFilter: CompaniesFilter) => {
    const pollKey = `${tenderId}-${companiesFilter}`;
    pollingInstances.value.get(pollKey)?.stop();
    pollingInstances.value.delete(pollKey);
  };

  const fetchInvitedCompanies = async(tenderId: string): Promise<Company[]> => {
    const response = await nioAxios.get<{ data: Company[] }>(`/enterprise/tenders/${tenderId}/invitation`);
    invitedVendors.value = response.data.data;
    invitedVendorIds.value = invitedVendors.value.map(company => company.id);
    return invitedVendors.value;
  };

  const inviteCompany = async(tenderId: string, companyId: string): Promise<Company> => {
    const response = await nioAxios.post<{ data: Company }>(`/enterprise/tenders/${tenderId}/invitation/${companyId}`);
    const company = response.data.data;
    if (!invitedVendorIds.value.includes(companyId)) {
      invitedVendorIds.value.push(companyId);
    }
    const idx = invitedVendors.value.findIndex(v => v.id === company.id);
    if (idx !== -1) {
      invitedVendors.value = [
        ...invitedVendors.value.slice(0, idx),
        company,
        ...invitedVendors.value.slice(idx + 1)
      ];
    } else {
      invitedVendors.value = [...invitedVendors.value, company];
    }
    return company;
  };

  const uninviteCompany = async(tenderId: string, companyId: string): Promise<void> => {
    await nioAxios.delete(`/enterprise/tenders/${tenderId}/invitation/${companyId}`);
    invitedVendorIds.value = invitedVendorIds.value.filter(id => id !== companyId);
    invitedVendors.value = invitedVendors.value.filter(v => v.id !== companyId);
  };

  const sendInvitedCompanies = async(tenderId: string): Promise<void> => {
    await nioAxios.post(`/enterprise/tenders/${tenderId}/invitation/send`);
    await fetchInvitedCompanies(tenderId);
  };

  const resetMatchingResults = () => {
    specificMatchingResult.value = null;
    marketplaceMatchingResult.value = null;
    matchingStatus[CompaniesFilter.Selection] = null;
    matchingStatus[CompaniesFilter.Marketplace] = null;
  };

  const resetInvitedCompanies = () => {
    invitedVendors.value = [];
    invitedVendorIds.value = [];
  };

  return {
    tenders,
    specificMatchingResult,
    marketplaceMatchingResult,
    matchingStatus,
    invitedVendors,
    invitedVendorIds,
    fetchTenders,
    fetchTenderDetail,
    startMatchingPoll,
    stopMatchingPoll,
    fetchInvitedCompanies,
    inviteCompany,
    uninviteCompany,
    resetMatchingResults,
    sendInvitedCompanies,
    resetInvitedCompanies,
  };
});
