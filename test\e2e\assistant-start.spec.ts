import * as fs from 'node:fs';
import * as path from 'node:path';
import { fileURLToPath } from 'url';
import { test, expect } from '@playwright/test';
import { AssistantStart } from './page-objects/assistant/AssistantStart';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const fixturesDir = path.join(__dirname, 'fixtures');
const sampleProjectFilePath = path.join(fixturesDir, 'project.txt');
const RFP_CREATE_TIMEOUT = 30000;

let assistantStartPage: AssistantStart;

test.beforeEach(async({ page }) => {
  assistantStartPage = new AssistantStart(page);
  await assistantStartPage.goto();
});

test('create RFP from text input', async({ page }) => {
  const projectDescription = await fs.promises.readFile(sampleProjectFilePath, 'utf-8');

  await assistantStartPage.textInsertButton.click();
  await assistantStartPage.textInput.fill(projectDescription);
  await assistantStartPage.createButton.click();

  await expect(page).toHaveURL(/assistant\/rfp/, { timeout: RFP_CREATE_TIMEOUT });
});

test.fixme('cannot create RFP without text or file', async({ page }) => {
  await assistantStartPage.textInsertButton.click();

  await expect(page.getByRole('button', { name: 'Create' })).toBeDisabled();
});

test('create RFP from file upload', async({ page }) => {
  await assistantStartPage.uploadFiles([sampleProjectFilePath]);

  await expect(page.locator('body')).toHaveText(/project\.txt/);

  await assistantStartPage.createButton.click();

  await expect(page).toHaveURL(/assistant\/rfp/, { timeout: RFP_CREATE_TIMEOUT });
});

test('create RFP from upload of multiple files', async({ page }) => {
  await assistantStartPage.uploadFiles([
    sampleProjectFilePath,
    `${fixturesDir}/project-attachment.txt`,
  ]);

  await expect(page.locator('body')).toHaveText(/project.txt/);
  await expect(page.locator('body')).toHaveText(/project-attachment.txt/);

  await assistantStartPage.createButton.click();

  await expect(page).toHaveURL(/assistant\/rfp/, { timeout: RFP_CREATE_TIMEOUT });
});

test('create RFP from file upload & then text input', async({ page }) => {
  await assistantStartPage.uploadFiles([sampleProjectFilePath]);

  await expect(page.locator('body')).toHaveText(/project.txt/);

  await assistantStartPage.textInsertButton.click();
  await assistantStartPage.textInput.fill('Additional information');
  await assistantStartPage.createButton.click();

  await expect(page).toHaveURL(/assistant\/rfp/, { timeout: RFP_CREATE_TIMEOUT });
});

test('create RFP from text input & then file upload', async({ page }) => {
  const projectDescription = await fs.promises.readFile(sampleProjectFilePath, 'utf-8');

  await assistantStartPage.textInsertButton.click();
  await assistantStartPage.textInput.fill(projectDescription);
  await assistantStartPage.uploadFiles([`${fixturesDir}/project-attachment.txt`]);

  await expect(page.locator('body')).toHaveText(/project-attachment.txt/);

  await assistantStartPage.createButton.click();

  await expect(page).toHaveURL(/assistant\/rfp/, { timeout: RFP_CREATE_TIMEOUT });
});

