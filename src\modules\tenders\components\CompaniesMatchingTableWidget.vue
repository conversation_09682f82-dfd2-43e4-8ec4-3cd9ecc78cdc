<script setup lang="ts">
import type { useRoute } from 'vue-router';
import CompaniesMatchingTableWidgetEntry from './CompaniesMatchingTableWidgetEntry.vue';
import type { Company } from '../types/tenders-types';

interface Props {
  companies: Company[];
  size: number;
  startingIndex: number;
  tenderPublicId: string;
  matchId: string;
  loading: boolean;
  showMoreRoute: ReturnType<typeof useRoute>;
  allowInvite?: boolean;
}

withDefaults(defineProps<Props>(), {
  allowInvite: true,
});
</script>

<template>
  <div v-if="loading">
    <table class="w-full border-collapse">
      <thead>
        <tr class="text-nio-grey-700">
          <th class="py-sm px-md w-[40%] text-left text-sm font-paragraph">
            Vendor
          </th>
          <th class="py-sm px-md w-[20%] text-right text-sm font-paragraph">
            Match
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="i in 8" :key="i" class="animate-pulse">
          <td class="py-2.5 px-md">
            <div class="flex items-center space-x-3">
              <div class="w-[14px] h-[14px] bg-nio-grey-200 rounded-10 mb-4" />
              <div>
                <div class="w-[98px] h-[22px] bg-nio-grey-200 rounded-10 mb-1" />
                <div class="w-[42px] h-[13px] bg-nio-grey-200 rounded-10" />
              </div>
            </div>
          </td>
          <td class="py-2.5 px-md flex justify-end">
            <div class="w-[60px] h-[40px] bg-nio-grey-200 rounded-10" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div v-else-if="companies.length === 0">
    <table class="w-full border-collapse">
      <thead>
        <tr class="text-nio-grey-700">
          <th class="py-sm px-md w-[40%] text-left text-sm font-paragraph">
            Vendor
          </th>
          <th class="py-sm px-md w-[20%] text-right text-sm font-paragraph">
            Match
          </th>
        </tr>
      </thead>
    </table>

    <div class="flex justify-center items-center h-full mt-[124px] mb-[127px]">
      <p
        class="text-nio-red-500 text-center font-paragraph text-[24px] px-[14px] py-[7px] rounded-15 border border-nio-blue-outline-stroke-400 shadow-custom-box-shadow-01 inline-block"
      >
        Unfortunately, none of the vendors met the required criteria.
      </p>
    </div>
  </div>

  <div v-else class="w-full grid xl:grid-cols-2 gap-x-3 ">
    <CompaniesMatchingTableWidgetEntry
      v-for="(company, index) in companies"
      :key="company?.id"
      :company="company as Company"
      :tender-id="tenderPublicId"
      :index="index + startingIndex"
      :allow-invite="allowInvite"
    />
  </div>
</template>

<style scoped>
.shadow-custom-box-shadow-01 {
  box-shadow: 0 71px 20px 0 rgba(207, 207, 207, 0.00),
  0 45px 18px 0 rgba(207, 207, 207, 0.01),
  0 25px 15px 0 rgba(207, 207, 207, 0.05),
  0 11px 11px 0 rgba(207, 207, 207, 0.09),
  0 3px 6px 0 rgba(207, 207, 207, 0.10);
}
</style>
