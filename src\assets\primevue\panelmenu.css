.p-panelmenu {
    @apply flex flex-col gap-2
}

.p-panelmenu-panel {
    @apply bg-surface-0 dark:bg-surface-900 rounded-md p-1
        border border-surface-200 dark:border-surface-700
        text-surface-700 dark:text-surface-0
}

.p-panelmenu-header {
    @apply outline-none
}

.p-panelmenu-header-content {
    @apply rounded-md transition-colors duration-200 text-surface-700 dark:text-surface-0
}

.p-panelmenu-header-link {
    @apply flex items-center gap-2 px-3 py-2 select-none cursor-pointer relative no-underline text-inherit
}

.p-panelmenu-header-icon,
.p-panelmenu-item-icon {
    @apply text-surface-400 dark:text-surface-500
}

.p-panelmenu-submenu-icon {
    @apply text-surface-400 dark:text-surface-500
}

.p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content .p-panelmenu-header-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content .p-panelmenu-submenu-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-panelmenu-header:not(.p-panelmenu-header-active) .p-panelmenu-submenu-icon:dir(rtl) {
    @apply rotate-180;
}

.p-panelmenu-header:not(.p-disabled) .p-panelmenu-header-content:hover {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-panelmenu-header:not(.p-disabled) .p-panelmenu-header-content:hover .p-panelmenu-header-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-panelmenu-header:not(.p-disabled) .p-panelmenu-header-content:hover .p-panelmenu-submenu-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-panelmenu-submenu {
    @apply m-0 py-0 ps-4 outline-none list-none
}

.p-panelmenu-item-link {
    @apply flex items-center gap-2 px-3 py-2 select-none cursor-pointer no-underline text-inherit relative overflow-hidden
}

.p-panelmenu-item-content {
    @apply rounded-md transition-colors duration-200 text-surface-700 dark:text-surface-0
}

.p-panelmenu-item.p-focus > .p-panelmenu-item-content {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-panelmenu-item.p-focus > .p-panelmenu-item-content .p-panelmenu-item-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-panelmenu-item.p-focus > .p-panelmenu-item-content .p-panelmenu-submenu-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-panelmenu-item:not(.p-disabled) > .p-panelmenu-item-content:hover {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-panelmenu-item:not(.p-disabled) > .p-panelmenu-item-content:hover .p-panelmenu-item-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-panelmenu-item:not(.p-disabled) > .p-panelmenu-item-content:hover .p-panelmenu-submenu-icon {
    @apply text-surface-500 dark:text-surface-400
}
