import type { NIORouteMap } from '@/router';
import { ConsultantTableType } from '../types/consultants-types';

export const routeMap = {
  consultants: {
    path: '/consultants',
    name: 'consultantsRoot',
    meta: {
      i18nTitle: 'consultants.title',
      headTitlePrefix: 'Consultants',
    },
    children: {
      available: {
        path: 'available',
        name: 'consultantsAvailable',
        meta: {
          i18nTitle: 'consultants.available.title',
          headTitlePrefix: 'Available consultants',
        }
      },
      engaged: {
        path: 'engaged',
        name: 'consultantsEngaged',
        meta: {
          i18nTitle: 'consultants.engaged.title',
          headTitlePrefix: 'Engaged consultants',
        }
      }
    },
  },
} satisfies NIORouteMap;

export const routeIndex = [
  {
    ...routeMap.consultants,
    redirect: { name: routeMap.consultants.children.engaged.name },
    children: [
      {
        ...routeMap.consultants.children.available,
        component: () => import('../pages/ConsultantsPage.vue'),
        props: () => ({
          tableType: ConsultantTableType.AVAILABLE,
        })
      },
      {
        ...routeMap.consultants.children.engaged,
        component: () => import('../pages/ConsultantsPage.vue'),
        props: () => ({
          tableType: ConsultantTableType.ENGAGED,
        })
      },
    ],
  },
];

export default routeIndex;
