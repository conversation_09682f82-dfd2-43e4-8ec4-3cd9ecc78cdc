<script setup lang="ts">
import MetchAirImg from '@/assets/images/metch_air-min.jpg';
</script>

<template>
  <section class="grid grid-cols-2 gap-2.5">
    <div class="h-56 bg-nio-black-900 p-4 rounded-15 custom-shadow-01  flex flex-col justify-between">
      <h4 class="w-fit block py-1 px-2 rounded-5 backdrop-blur-sm bg-nio-grey-background-15 text-sm text-nio-white font-medium -tracking-028px leading-[14px]">
        Hints
      </h4>
      <h5 class="text-p-xl text-nio-white font-medium -tracking-[0.36px] leading-[22px] text-balance">
        Projects similar to yours typically require a strong focus on Java. Make sure to prioritize this in your RFP.
      </h5>
    </div>
    <div class="h-56 p-4 rounded-15 custom-shadow-01 flex flex-col justify-between" :style="{backgroundImage: `url('${MetchAirImg}')`, backgroundSize: 'cover', backgroundPosition: 'center'}">
      <h4 class="w-fit block py-1 px-2 rounded-5 backdrop-blur-sm bg-nio-grey-background-15 text-sm text-nio-white font-medium -tracking-028px leading-[14px]">
        Projects
      </h4>
      <h3 class="block text-h28 text-nio-white">
        Project 1
      </h3>
    </div>
  </section>
</template>