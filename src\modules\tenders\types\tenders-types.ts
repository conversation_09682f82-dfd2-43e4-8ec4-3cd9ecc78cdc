export type Company = {
  id: string;
  name: string;
  region: string;
  score: number;
  hq?: string;
  country?: string;
  technologies?: string[];
  contact_email?: string;
  contact_phone?: string;
  headquarters?: string;
  main_industry?: string;
  founded_at?: number;
  employees?: Employees;
  match_details?: MatchDetails;
  industries?: string[];
  created_at?: string;
  about?: string;
  image?: string | null;
  cover?: string | null;
  logo?: string | null;
  matching?: MatchingScores;
  employee_emails?: string[];
  sent_at?: string | null;
  rates?: {
    juniors?: number,
    mediors?: number,
    seniors?: number,
    leads?: number,
  }
};
export interface CompanyWithProposalStats extends Company {
  sent_at?: string;
  first_viewed_at?: string;
  candidates_count?: number;
}

export type TechnologyCategory = {
  name: string;
  items: string[];
};

export type Tender = {
  id: string;
  created_at: string;
  created_by: string;
  submissions_deadline: string;
  name: string;
  description: string;
  region: string;
  main_industry: string;
  positions: Position[];
  pre_matching_exists: boolean;
  matching_exists: boolean;
  matching_in_progress: boolean;
  tags?: string[];
  invitedVendors?: string;
  views?: string;
  applications?: string;
  budget?: number;
  deadline?: string;
  status: TenderState;
  matching?: Company[];
  invited?: number,
  proposals?: number | undefined,
  technologies: TechnologyCategory[];
  attachments?: {
    name: string;
    url: string;
  }[];
  total_invited_companies?: number;
  total_viewed_companies?: number;
};

export type Position = {
  name?: string;
  count?: number;
  seniority?: string;
}

export interface MatchingScores {
  score?: number;
  tech_score?: number;
}

export type ProjectDetailMinimal = {
  id: string;
  name: string;
  country: string;
  created_at: string;
  about: string;
  industries: string[];
  technologies: string[];
  matching?: MatchingScores;
};

export interface MatchDetails {
  overall_score?: number;
  projects_score?: number;
  projects_count?: number;
  technologies_score?: number;
  location_match?: string;
  location_score?: number;
  matching_rates?: number | { min: number; max: number };
  technologies?: {
    name: string;
    score: number;
    found_by?: string[]
  }[]
}

export interface Employees {
  employees: number;
  developers: number;
  positions: string[];
}

export type TenderCandidate = {
  id: string;
  name: string;
  profession?: string;
  seniority?: string;
  rate?: number;
  country?: string;
  company: {
    id: string;
    name: string;
    country?: string;
    headquarters?: string;
  };
  position: {
    id: string;
    name: string;
    price: number;
    price_to: number;
    seniority: string;
  };
  application: {
    status: string;
  };

  /**
   * When conditioning matching, distinguish between null and 0.
   * 0 is a valid value that can occur in some matchings,
   * while null indicates that this data does not exist.
   */
  matching?: {
    score: number | null;
    technologies_score?: number | null;
    experiences_score?: number | null;
    experiences_count?: number;
    technologies?: {
      name: string;
      score: number;
      found_by?: string[]
    }[],
    rate_difference_percentage?: number | null,
    seniority_match?: string | null,
  }
};

export type Candidate = {
  id: string;
  finished?: boolean;
  name: string;
  country: string;
  city: string;
  rate?: number;
  profession?: string;
  seniority?: string;
  lastJobTitle?: string;
  yearsOfExperience?: number;
  highestEducation?: string;
  fieldOfStudy?: string;
  cv?: CV;
  skills?: {
    technology: string;
    years_of_experience: number;
  }[];
  experiences?: {
    id: string;
    name: string;
    description: string;
    length: number;
  }[];
  vendor: {
    id: string;
    slug: string;
    companySlug: string;
    name: string;
  };
};

export type CV = {
    id: string;
    format: string;
    mime: string;
    size: number;
    name: string;
    url: string;
};

export type TenderState = 'incubation' | 'open' | 'reviewing' | 'ended';
