<script setup lang="ts">
import 'vue-awesome-paginate/dist/style.css';
import { ChevronRightIcon } from 'lucide-vue-next';

interface Props {
  itemsLength: number,
  itemsPerPage: number,
}

defineProps<Props>();

const currentPage = defineModel<number>({ required: true });

</script>

<template>
  <vue-awesome-paginate
    v-model="currentPage"
    data-testid="nio-pagination"
    :total-items="itemsLength"
    :items-per-page="itemsPerPage"
    :max-pages-shown="5"
  >
    <template #prev-button>
      <ChevronRightIcon class="w-5 h-5" />
    </template>

    <template #next-button>
      <span>
        <ChevronRightIcon class="w-5 h-5" />
      </span>
    </template>
  </vue-awesome-paginate>
</template>

<style lang="css">
.nio-pagination .pagination-container {
  column-gap: 6px;
  align-items: center;
}
.nio-pagination .paginate-buttons {
  height: 32px;
  width: 32px;
  cursor: pointer;
  border-radius: 9999px;
  background-color: transparent;
  border: none;
  color: black;
  font-size: 14px;
}

.nio-pagination .back-button,
.nio-pagination .next-button {
  /* bg-nio-grey-11 hover:bg-nio-grey-hover-100 hover:cursor-pointer text-nio-grey-700 */
  background-color: var(--color-nio-grey-11);
  color: var(--color-nio-grey-700);
  border-radius: 9999px;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nio-pagination .back-button svg,
.nio-pagination .next-button svg {
  position: relative;
  bottom: 1.5px;
}
.nio-pagination .active-page {
  /* background-color: #e5e5e5; */
  /* background-color: var(--color-nio-grey-11); */
  border: 1px solid var(--color-nio-grey-400);
}
.nio-pagination .paginate-buttons:hover {
  background-color: var(--color-nio-grey-11);
}
.nio-pagination .active-page:hover {
  border: 1px solid var(--color-nio-grey-400);
  background-color: transparent;
}

.nio-pagination .back-button svg {
  transform: rotate(180deg) translateY(-2px);
}
.nio-pagination .next-button svg {
  transform: translateY(2px);
}

.nio-pagination .back-button:hover,
.nio-pagination .next-button:hover {
  background-color: var(--color-nio-grey-hover-100);
}
</style>