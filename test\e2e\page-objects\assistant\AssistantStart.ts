import { type Locator, type Page } from '@playwright/test';
import { BasePage } from '../BasePage';
import path from 'node:path';

export class AssistantStart extends BasePage {
  readonly urlPath: string = '/assistant';
  readonly textInsertButton: Locator;
  readonly uploadButton: Locator;
  readonly textInput: Locator;
  readonly createButton: Locator;

  constructor(page: Page) {
    super(page);
    this.textInsertButton = page.getByRole('button', { name: 'Insert Text' });
    this.uploadButton = page.getByRole('button', { name: 'Upload' });
    this.textInput = page.getByRole('textbox');
    this.createButton = page.getByRole('button', { name: 'Create' });
  }

  async goto() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Assistant' }).click();
    await this.page.waitForURL(this.urlPath);
  }

  async uploadFiles(filePaths) {
    const fileChooserPromise = this.page.waitForEvent('filechooser');
    await this.uploadButton.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(filePaths.map(filePath => path.join(filePath)));
  }
}
