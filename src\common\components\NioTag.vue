<template>
  <span
    class="self-start bg-transparent border border-nio-grey-700 text-nio-grey-800 font-normal leading-[18px]"
    :class="[
      sizeClasses[size],
      rounded ? 'rounded-full' : 'rounded-5'
    ]"
  >
    <slot />
  </span>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  size?: 'sm' | 'md' | 'lg'
  rounded?: boolean
}>(), {
  size: 'md',
  rounded: false
});

const sizeClasses = {
  sm: 'px-2 py-0.5 text-[12px]',
  md: 'px-3 py-1 text-[14px]',
  lg: 'px-4 py-1.5 text-[16px]',
};
</script>