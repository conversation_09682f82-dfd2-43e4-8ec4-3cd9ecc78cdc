<script setup lang="ts">
import { ref, watch } from 'vue';
import { debounce } from 'lodash-es';
import MagnifyingGlassIcon from '@/assets/icons/magnifying-glass-icon.svg';
import { XIcon } from 'lucide-vue-next';

const modelValue = ref<any>('');
const emit = defineEmits<{ (e: 'update:modelValue', value: any): void }>();

defineProps<{
  type?: 'text' | 'number';
  name?: string;
}>();

const debouncedEmit = debounce((value: any) => {
  emit('update:modelValue', value);
}, 300);

watch(modelValue, newValue => {
  debouncedEmit(newValue);
});
</script>

<template>
  <div class="relative">
    <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
      <MagnifyingGlassIcon class="w-4 h-4 text-gray-400" />
    </div>
    <input
      v-model="modelValue"
      :type="type"
      :placeholder="name ?? 'Search...'"
      class="w-64 h-9 pl-10 pr-10 py-2 bg-gray-100 text-gray-700 text-sm rounded-full border border-gray-300/40 focus:outline-none focus:ring-0 focus:bg-white transition-all duration-200 hover:bg-gray-50"
    >
    <button
      v-if="modelValue"
      class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
      @click="modelValue = ''"
    >
      <XIcon class="w-4 h-4 cursor-pointer" />
    </button>
  </div>
</template>