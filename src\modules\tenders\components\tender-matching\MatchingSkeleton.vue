<script setup lang="ts">
import { AlertTriangle } from 'lucide-vue-next';
import { MatchingStatus } from '@/modules/tenders/types/tenders-enums';
const props = defineProps<{ error?: boolean; matchingStatus?: MatchingStatus }>();
</script>

<template>
  <div v-if="props.error || props.matchingStatus === MatchingStatus.Failed || props.matchingStatus === MatchingStatus.NoVendorsMatched" class="flex items-center justify-center w-full h-full min-h-[200px]">
    <div class="text-[20px] font-normal text-nio-black-900 leading-[28px] tracking-[0.52px] mb-2 text-center">
      <template v-if="props.matchingStatus === MatchingStatus.NoVendorsMatched">
        <span class="text-[16px]">
          No vendors matched your current request.
        </span>
        <p class="text-xs">
          This may be due to narrowly defined criteria, limited vendor availability in your network, or a low match
          score across key capabilities. Matching is based on verified data — if no options appear, it means none
          currently meet the requirements.
        </p>
      </template>
      <template v-else>
        <span class="text-[16px] flex items-center gap-2"> <AlertTriangle class="w-4 h-4 text-nio-red-500 inline-block" /> Matching failed.</span>
      </template>
    </div>
  </div>
  <div v-else class="grid gap-4 w-full grid-cols-2 animate-pulse">
    <div class="flex flex-row justify-between min-h-[290px] w-full">
      <div class="flex flex-col justify-between px-4 py-2 bg-[#F2F2F7] w-1/2 rounded-l-20">
        <div>
          <div class="w-[120px] h-[40px] bg-nio-grey-200 rounded-10" />
          <div class="w-[42px] h-[13px] bg-nio-grey-200 rounded-10 mt-2" />
        </div>

        <div>
          <div class="w-[50px] h-[13px] bg-nio-grey-200 rounded-10" />
          <div class="mt-2">
            <div class="w-full h-[13px] bg-nio-grey-200 rounded-10" />
            <div class="w-full h-[13px] bg-nio-grey-200 rounded-10 mt-1" />
            <div class="w-full h-[13px] bg-nio-grey-200 rounded-10 mt-1" />
          </div>
        </div>

        <div class="flex flex-wrap gap-2 mt-1">
          <div class="w-[80px] h-[16px] bg-nio-grey-200 rounded-10" />
          <div class="w-[80px] h-[16px] bg-nio-grey-200 rounded-10" />
        </div>
      </div>
      <div class="flex flex-col justify-between px-4 py-2 bg-[#E5E5EA] w-1/2 rounded-r-20">
        <div class="w-[100px] h-[68px] bg-nio-grey-200 rounded-10" />

        <div class="w-full h-[116px] bg-nio-grey-200 rounded-10" />
        <div class="w-full h-[38px] bg-nio-grey-200 rounded-20" />
      </div>
    </div>
    <div class="flex flex-row justify-between min-h-[290px] w-full">
      <div class="flex flex-col justify-between px-4 py-2 bg-[#F2F2F7] w-1/2 rounded-l-20">
        <div>
          <div class="w-[120px] h-[40px] bg-nio-grey-200 rounded-10" />
          <div class="w-[42px] h-[13px] bg-nio-grey-200 rounded-10 mt-2" />
        </div>

        <div>
          <div class="w-[50px] h-[13px] bg-nio-grey-200 rounded-10" />
          <div class="mt-2">
            <div class="w-full h-[13px] bg-nio-grey-200 rounded-10" />
            <div class="w-full h-[13px] bg-nio-grey-200 rounded-10 mt-1" />
            <div class="w-full h-[13px] bg-nio-grey-200 rounded-10 mt-1" />
          </div>
        </div>

        <div class="flex flex-wrap gap-2 mt-1">
          <div class="w-[80px] h-[16px] bg-nio-grey-200 rounded-10" />
          <div class="w-[80px] h-[16px] bg-nio-grey-200 rounded-10" />
        </div>
      </div>
      <div class="flex flex-col justify-between px-4 py-2 bg-[#E5E5EA] w-1/2 rounded-r-20">
        <div class="w-[100px] h-[68px] bg-nio-grey-200 rounded-10" />

        <div class="w-full h-[116px] bg-nio-grey-200 rounded-10" />
        <div class="w-full h-[38px] bg-nio-grey-200 rounded-20" />
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>

</style>
