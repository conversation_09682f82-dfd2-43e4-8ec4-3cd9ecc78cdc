import { computed } from 'vue';
import { useWorkspaceRate } from '@/common/composables/useHourlyRate';

export function useFormattedRates(rates: number | { min: number; max: number } | undefined | null) {
  const { getValidRateRange, getValidRate } = useWorkspaceRate();

  return computed(() => {
    if (!rates) {
      return 'N/A';
    }

    if (typeof rates === 'number') {
      return rates < 20 ? 'N/A' : getValidRate(rates);
    }

    if (typeof rates === 'object' && 'min' in rates && 'max' in rates) {
      const { min, max } = rates;

      if (min === max) {
        return min < 20 ? 'N/A' : getValidRate(min);
      }

      if (min < 20) {
        return max < 20 ? 'N/A' : `≤ ${getValidRate(max)}`;
      }

      return getValidRateRange([min, max]);
    }

    return 'N/A';
  });
}