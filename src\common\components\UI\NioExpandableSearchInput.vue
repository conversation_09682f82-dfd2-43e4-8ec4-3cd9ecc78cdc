<script setup lang="ts">
import { ref, nextTick } from 'vue';

const searchQuery = defineModel<string>();
const isExpanded = ref(false);
const showChip = ref(true);
const inputElement = ref<HTMLInputElement | null>(null);

const toggleSearch = async() => {
  showChip.value = false;
  isExpanded.value = true;
  await nextTick();
  inputElement.value?.focus();
};

const closeSearch = () => {
  if (!searchQuery.value) {
    isExpanded.value = false;
    setTimeout(() => {
      showChip.value = true;
    }, 700);
  }
};
</script>

<template>
  <div class="relative inline-block">
    <div
      v-if="showChip"
      class="h-[26px] px-3 flex items-center bg-nio-grey-100 text-nio-grey-500 text-[14px] font-medium leading-[14px] tracking-[-0.28px] rounded-[30px] cursor-pointer transition-all duration-700 ease-in-out hover:bg-nio-white hover:text-nio-black-900"
      @click="toggleSearch"
    >
      Search
    </div>

    <div
      v-if="!showChip || isExpanded"
      class="relative h-[26px] overflow-hidden transition-all duration-700 ease-in-out"
      :class="{ 'w-[69px]': !isExpanded, 'w-[200px]': isExpanded }"
    >
      <input
        ref="inputElement"
        v-model="searchQuery"
        type="text"
        placeholder="Search"
        class="w-full h-full px-3 bg-nio-white text-nio-black-900 rounded-[30px] text-[14px] font-medium leading-[14px] tracking-[-0.28px] border-none focus:ring-0 focus:outline-none focus:border-none transition-all duration-700 ease-in-out"
        @blur="closeSearch"
      >
    </div>
  </div>
</template>
