<template>
  <div class="intro-text-container">
    <transition
      mode="out-in"
      name="fade-in-04"
      :duration="400"
      appear
    >
      <div v-if="currentStep === IntroSteps.INTRO" class="step-text mb-[92.5px]">
        <h3 ref="stepTitle" class="step-title text-h3">
          {{ $t('intro.desc-texts.hello') }} {{ userName }},
        </h3>
        <div ref="stepDesc" class="step-desc text-[#8A8A8A] text-[20px] rounded">
          {{ $t('intro.desc-texts.welcome-to-nio') }}
        </div>
      </div>
      <div v-else-if="currentStep === IntroSteps.UPLOAD_VENDORS" class="step-text mb-[92.5px]">
        <h3 ref="stepTitle" class="step-title text-h3">
          {{ $t('intro.desc-texts.in-the-first-step') }},
        </h3>
        <div ref="stepDesc" class="step-desc text-[#8A8A8A] text-[20px] rounded ">
          {{ $t('intro.desc-texts.upload-between') }}
          <span class="!text-[#0071E3]">{{ vendorLimits.min }}</span>
          {{ $t('intro.desc-texts.and') }}
          <span class="!text-[#0071E3]">{{ vendorLimits.max === Infinity ? '∞' : vendorLimits.max }}</span>
          {{ $t('misc.vendors') }}
        </div>
      </div>
      <div v-else-if="currentStep === IntroSteps.UPLOADING" class="step-text mb-[92.5px]">
        <h3 ref="stepTitle" class="step-title text-h3">
          {{ $t('intro.desc-texts.uploading') }},
        </h3>
        <div ref="stepDesc" class="step-desc text-[#8A8A8A] text-[20px] rounded ">
          {{ $t('intro.desc-texts.search-underway') }}..
        </div>
      </div>
      <div v-else-if="currentStep === IntroSteps.VERIFICATION" class="step-text mb-[92.5px]">
        <h3 ref="stepTitle" class="step-title text-h3">
          {{ $t('intro.desc-texts.verification') }},
        </h3>
        <div ref="stepDesc" class="step-desc text-[#8A8A8A] text-[20px] rounded ">
          {{ $t('intro.desc-texts.just-a-moment') }}...
        </div>
      </div>
      <div v-else-if="currentStep === IntroSteps.ADD_VENDORS" class="step-text mb-[92.5px]">
        <h3 ref="stepTitle" class="step-title text-h3">
          {{ $t('intro.add') }},
        </h3>
        <div ref="stepDesc" class="step-desc text-[#8A8A8A] text-[20px] rounded ">
          {{ $t('intro.desc-texts.its-nearly-ready') }}...
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { IntroSteps } from '@/modules/intro/types/intro-steps';
import { onWatcherCleanup, ref, watch } from 'vue';
import { transformText } from '@/common/utils/text';
import { maxVendors } from '@/config/intro.ts';

interface Props {
  currentStep: IntroSteps,
  userName: string,
}

defineProps<Props>();

const stepTitle = ref<HTMLHeadingElement>();
const stepDesc = ref<HTMLDivElement>();

const vendorLimits = {
  min: 2,
  max: maxVendors,
};

watch(stepTitle, newVal => {
  if (newVal) {
    transformText(newVal, 1.4);
  }
});

watch(stepDesc, newVal => {
  let timeout: ReturnType<typeof setTimeout>;
  if (newVal) {
    timeout = setTimeout(() => {
      transformText(newVal, 1.4);
    }, 1000);
  }
  onWatcherCleanup(() => {
    clearTimeout(timeout);
  });
});
</script>

<style lang="css" scoped>
.step-desc {
  --blur-anim-color: #8A8A8A;
  opacity: 0;
}

.intro-text-container:has(.step-desc.blur-anim) {
  .step-desc {
    opacity: 1;
  }
}
</style>
