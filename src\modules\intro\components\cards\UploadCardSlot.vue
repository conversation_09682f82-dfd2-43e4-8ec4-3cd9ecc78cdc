<template>
  <div>
    <div>
      <div
        v-if="!fileDetail"
        class="absolute left-[30px] top-[30px] w-[40px] h-[40px] rounded-full bg-[#F3F4F4]"
      >
        <PlusIcon class="text-nio-blue-800" />
      </div>
      <div
        v-else
        class="absolute flex items-center justify-center left-[30px] top-[30px] w-[40px] h-[40px] rounded-full bg-[#F3F4F4]"
      >
        <FileIcon />
      </div>
    </div>
    <div>
      <div
        v-if="!fileDetail"
        class="absolute left-[30px] bottom-[40px]"
      >
        <h3 class="text-h4 text-[#0071E3]">
          {{ $t('intro.upload-companies') }}
        </h3>
        <p class="block text-p-x text-[#A1A1A1]">
          {{ $t('intro.drag-drop-upload') }}
        </p>
      </div>
      <div
        v-else
        class="absolute left-[30px] bottom-[40px] w-[calc(100%-60px)]"
      >
        <div class="w-full h-full relative">
          <p class="text-sm text-[#A1A1A1]">
            {{ fileDetail.name.split('.').pop() }}, {{ (fileDetail.size * 0.001).toFixed(1) }} KB
          </p>
          <h3 class="text-h5 text-[#292929] line-clamp-1 w-full whitespace-nowrap">
            {{ fileDetail.name }}
          </h3>
          <!--          <div class="absolute right-0 bottom-0 w-[50px] h-8 bg-gradient-to-r from-transparent to-[#e2e2e4]"></div>-->
        </div>
      </div>
    </div>
    <div
      v-if="fileDetail"
      class="cancel-btn absolute z-10 -translate-y-1/2 translate-x-1/2 top-[-4px] right-[-10px] w-[30px] h-[30px] bg-[rgba(249,249,249,0.60)] border border-[#BBD0FB] p-1.5 rounded-full"
      @click.stop.prevent="emit('removeFile')"
    >
      <CloseIcon class="h-full w-full fill-[#A1A1A1]" />
    </div>
  </div>
</template>

<script setup lang="ts">
import PlusIcon from '@/assets/icons/plus-icon.svg';
import { IntroSteps } from '@/modules/intro/types/intro-steps';
import FileIcon from '@/assets/icons/file-icon.svg';
import CloseIcon from '@/assets/icons/close-icon.svg';

interface Props {
  currentStep?: IntroSteps,
  fileDetail?: File,
}

defineProps<Props>();
const emit = defineEmits(['removeFile']);

</script>
