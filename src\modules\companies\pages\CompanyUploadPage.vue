<template>
  <PageContainerWrapper transparent>
    <Transition
      appear
      enter-active-class="transition-opacity duration-150 ease-in"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-150 ease-out"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <main class="flex items-center w-full">
        <div
          class=" w-full"
        >
          <div class="flex gap-[30px] [&>.card]:rounded-50 [&>.card]:border [&>.card]:border-[#BBD0FB]">
            <Card
              v-if="!isLoadingVisible && !searchState.isOpen"
              class="h-[315px]"
              :hover-scale-active="!simulatedFile"
              @click="onUploadCardClick"
            >
              <UploadCardSlot
                :file-detail="simulatedFile"
                @remove-file="resetUploadState"
              />
            </Card>
            <Card
              v-if="!isLoadingVisible"
              ref="introCardSearchEl"
              class="transition-none!"
              :class="[
                (currentStep >= UploadSteps.VERIFICATION || searchState.isOpen) ? 'flex-1' : '',
                searchState.isOpen ? 'mr-[30px] min-h-[315px] h-fit!' : '',
              ]"
              :hover-enabled="!pendingCompanies"
              @click="onClickSearch"
            >
              <SearchCardSlot
                v-model="searchState.text"
                :show-input="searchState.isOpen && !pendingCompanies"
                :pending-companies="pendingCompanies"
                @close-search="searchState.isOpen = false"
                @added-company-to-list="currentStep = UploadSteps.VERIFICATION"
              />
            </Card>
          </div>

          <div
            v-if="isLoadingVisible"
            class="flex gap-[30px] [&>.card]:rounded-25 [&>.card]:border [&>.card]:border-[#BBD0FB]"
          >
            <Card
              :hover-scale-active="!simulatedFile"
              :hover-enabled="false"
              class="flex-1"
            >
              <UploadLoadingCardSlot
                :file-detail="simulatedFile"
                @remove-file="resetUploadState"
              />
            </Card>
          </div>

          <div
            v-if="currentStep === UploadSteps.UPLOADING"
            class="w-full text-center pt-[135px]"
          >
            <AnimatedText
              class="text-h1"
              :text="$t('intro.uploading-animated-text') + '.'"
            />
          </div>
          <CompanyList
            v-if="!isLoadingVisible && (currentStep > UploadSteps.UPLOADING || vendorsStore.companies.length > 0)"
            @all-companies-verified="onAllVendorsVerified"
          />
        </div>
      </main>
    </Transition>
    <UploadCompanyDialog
      v-model:show="modalState.upload.isShown"
      @file-uploaded="onFileUploaded"
    />
  </PageContainerWrapper>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, reactive, ref } from 'vue';
import { useVendorsStore } from '@/modules/intro/stores/companiesStore.ts';
import { UploadSteps } from '@/modules/companies/types/upload-steps.ts';
import Card from '@/common/components/Card.vue';
import UploadCardSlot from '@/modules/intro/components/cards/UploadCardSlot.vue';
import SearchCardSlot from '@/modules/intro/components/cards/SearchCardSlot.vue';
import CompanyList from '@/modules/intro/components/CompanyList.vue';
import UploadLoadingCardSlot from '@/modules/intro/components/cards/UploadLoadingCardSlot.vue';
import AnimatedText from '@/common/components/AnimatedText.vue';
import UploadCompanyDialog from '@/modules/intro/components/UploadCompanyDialog.vue';
import { uploadingDelayMillis } from '@/config/intro.ts';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';

const vendorsStore = useVendorsStore();
const simulatedFile = ref<File | undefined>();
const currentStep = ref<UploadSteps>(UploadSteps.UPLOAD_VENDORS);
const modalState = reactive({
  upload: {
    isShown: false,
  },
});
const searchState = reactive({
  isOpen: false,
  text: '',
});
const isLoadingVisible = ref(false);
const uploadError = ref<string | null>(null);
const introCardSearchEl = ref();

const pendingCompanies = computed(() => vendorsStore.companies.some(v => [undefined, 'pending'].includes(v.status)));

const onClickSearch = () => {
  if (pendingCompanies?.value) {
    return;
  }
  searchState.isOpen = true;
};

const searchIntroCardListener = (event: MouseEvent) => {
  if (searchState.text?.length > 0) {
    return;
  }
  const target = introCardSearchEl.value?.$el;
  if (!target) {
    return;
  }
  const withinBoundaries = event.composedPath().includes(target);

  if (!withinBoundaries) {
    searchState.isOpen = false;
  }
};

onMounted(() => {
  document.addEventListener('click', searchIntroCardListener);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', searchIntroCardListener);
});

const onUploadCardClick = () => {
  if (simulatedFile.value) {
    return;
  }
  currentStep.value = UploadSteps.UPLOAD_VENDORS;
  modalState.upload.isShown = true;
};

const onFileUploaded = async(file: File) => {
  simulatedFile.value = file;
  currentStep.value = UploadSteps.UPLOADING;
  modalState.upload.isShown = false;
  isLoadingVisible.value = true;
  uploadError.value = null;

  try {
    await Promise.all([
      vendorsStore.uploadVendors(file),
      new Promise(resolve => setTimeout(resolve, uploadingDelayMillis)),
    ]);

    isLoadingVisible.value = false;
    currentStep.value = UploadSteps.VERIFICATION;
  } catch (error) {
    console.error('File upload failed:', error);
    uploadError.value = 'Failed to upload the file. Please try again.';
    isLoadingVisible.value = false;
    resetUploadState();
    if (vendorsStore.companies.length > 1) {
      currentStep.value = vendorsStore.filteredVendors.value.every(v => v.status === 'verified') ? UploadSteps.ADD_VENDORS : UploadSteps.VERIFICATION;
      return;
    }
    currentStep.value = UploadSteps.UPLOAD_VENDORS;
  }
};

const onAllVendorsVerified = () => {
  currentStep.value = UploadSteps.ADD_VENDORS;
};

const resetUploadState = () => {
  simulatedFile.value = undefined;
};
</script>

<style lang="css" scoped>
.custom-color {
  --blur-anim-color: --nio-grey-500;
}
</style>
