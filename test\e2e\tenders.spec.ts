import { expect, test } from '@playwright/test';
import { TendersPage } from './page-objects/tenders/TendersPage';

test('Has tenders list', async({ page }) => {
  test.slow();
  const tendersPage = new TendersPage(page);
  await tendersPage.goto();
  await expect(tendersPage.tenderItems.first()).toBeVisible();
  await tendersPage.openFirstTender();
  await expect(page).toHaveURL(/tenders\/[^/]+\/detail/);
});