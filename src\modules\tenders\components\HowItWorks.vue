<script setup lang="ts">
import howItWorksImage from '@/assets/images/how-it-works.jpg';
import ArrowIcon from '@/assets/icons/arrow-icon.svg';
</script>

<template>
  <div
    class="fixed bottom-[30px] right-[30px] w-[145px] flex items-center justify-center"
  >
    <div
      class="relative rounded-30 border border-nio-blue-400 bg-linear-to-b from-[rgba(255,255,255,0.08)] to-[rgba(19,19,19,0.25)] shadow-[0px_149px_42px_rgba(222,222,222,0.00),0px_95px_38px_rgba(222,222,222,0.01),0px_54px_32px_rgba(222,222,222,0.05),0px_24px_24px_rgba(222,222,222,0.09),0px_6px_13px_rgba(222,222,222,0.10)] w-[145px] h-[195px] bg-center bg-cover flex flex-col justify-end items-center overflow-hidden group"
      :style="{ backgroundImage: `url(${howItWorksImage})` }"
    >
      <p class="text-white text-[14px] font-normal leading-normal tracking-[0.56px] text-center mb-[30px] px-[28px]">
        {{ $t('tenders.how-it-works') }}?
      </p>
      <div
        class="absolute top-[15px] right-[15px] w-10 h-10 flex items-center justify-center rounded-full bg-nio-blue-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      >
        <ArrowIcon class="bg-nio-blue-800 text-white rounded-full" hover />
      </div>
    </div>
  </div>
</template>
