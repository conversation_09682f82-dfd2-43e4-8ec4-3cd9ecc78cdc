import type { DateCategory } from '@/modules/assistant/types/assistant-history.ts';
import { DateTime } from 'luxon';

export const categoryConfig: { [key in DateCategory]: (date: DateTime) => boolean } = {
  Today: date => date.hasSame(DateTime.local(), 'day'),
  Yesterday: date => date.hasSame(DateTime.local().minus({ days: 1 }), 'day'),
  'Previous 7 days': date => date < DateTime.local().minus({ days: 1 }),
};
