// types/match-status.ts

export type MatchStatusState = 'in_progress' | 'completed'

export interface MatchStatusResponse {
  data: MatchStatus[]
}

export interface Employees {
  employees: number
  developers: number
  positions: unknown[]
}

export interface Company {
  id: string
  name: string
  country: string
  headquarters: string
  website: string
  linkedin: string
  about: string
  founded_at: number
  cover: string | null
  logo: string | null
  main_industry: string
  industries: string[]
  technologies: string[]
  clients: unknown[]
  employees: Employees
  rates: unknown[]
  overall_score: number
  technologies_score: number
  projects_score: number
  location_match: string
  matching_rates: string
}

export interface MatchStatus {
  id: string;
  status: string;
  companies: any[];
  topVendors?: any[];
  otherVendors?: any[];
}
