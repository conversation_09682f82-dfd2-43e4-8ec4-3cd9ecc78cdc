import { type Locator, type Page } from '@playwright/test';
import { AssistantBaseStep } from './AssistantBaseStep';

export class AssistantStep3 extends AssistantBaseStep {
  readonly heading: Locator;
  readonly createRfpButton: Locator;
  readonly deliverablesEditButton: Locator;
  readonly milestonesEditButton: Locator;
  readonly teamCompositionEditButton: Locator;
  readonly certificationsSelect: Locator;
  readonly certificationsOptions: Locator;

  constructor(page: Page) {
    super(page);
    this.heading = page.getByRole('heading', { name: 'Scope & Deliverables' });
    this.createRfpButton = page.getByRole('button').filter({ hasText: 'Create RFP' });
    this.deliverablesEditButton = page.getByRole('button', { name: 'Key Deliverables' });
    this.milestonesEditButton = page.getByRole('button', { name: 'Milestones' });
    this.teamCompositionEditButton = page.getByRole('button', { name: 'Team Composition' });
    this.certificationsSelect = page.getByRole('button', { name: 'Vendor Certifications' });
    this.certificationsOptions = this.getFormCellByLabel('Vendor Certifications').getByRole('listitem');
  }
}
