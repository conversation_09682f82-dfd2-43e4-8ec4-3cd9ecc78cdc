<template>
  <div>
    <BaseLayout />
    <NioToast ref="notificationRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, inject } from 'vue';
import BaseLayout from '@/common/layouts/BaseLayout.vue';
import NioToast from '@/common/components/NioToast.vue';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import { useHead } from '@unhead/vue';
import { useRouter } from 'vue-router';
import type { NIORouteMap } from '@/router';

const authStore = useAuthStore();
const notificationRef = ref();
const router = useRouter();
inject('toast');

router.beforeEach(to => {
  const headTitlePrefix = (to.meta as NIORouteMap[0]['meta']).headTitlePrefix ? `${(to.meta as NIORouteMap[0]['meta']).headTitlePrefix} | ` : '';
  useHead({
    title: `${headTitlePrefix}Nordics Platform`
  });
});

(window as Window & typeof globalThis & { logout: () => void }).logout = () => {
  authStore.logout();
};

</script>
