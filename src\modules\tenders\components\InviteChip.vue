<template>
  <div v-if="status == 'invited'" class="text-xs bg-nio-green-text/20 text-green-700 w-fit px-1.5 py-0.5 rounded-sm">
    Invited
  </div>
  <div v-else-if="status == 'pending'" class="text-xs bg-amber-300/30 text-amber-600 w-fit px-1.5 py-0.5 rounded-sm">
    Pending
  </div>
  <div v-else class="text-xs bg-nio-grey-10/15 text-nio-grey-4 w-fit px-1.5 py-0.5 rounded-sm">
    Not invited
  </div>
</template>

<script setup lang="ts">
defineProps<{
  status: 'invited' | 'pending' | 'not-invited';
}>();
</script>