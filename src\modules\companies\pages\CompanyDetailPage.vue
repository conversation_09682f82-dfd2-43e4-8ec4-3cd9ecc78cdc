<template>
  <CompanyDetailDialog v-model:is-opened="isDialogOpened" :data="vendorData || null" :loading="loading" />
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuestionStore } from '@/modules/companies/stores/question-store';
import CompanyDetailDialog from '@/modules/companies/components/dialogs/CompanyDetailDialog.vue';
import type { Vendor } from '@/modules/companies/types/company';

const route = useRoute();
const router = useRouter();
const questionStore = useQuestionStore();
const { getCompanyProfile } = questionStore;

const vendorData = ref<Vendor>();

const loading = ref(true);
const isDialogOpened = ref(false);

watch(
  isDialogOpened,
  newValue => {
    if (!newValue) {
      router.back();
    }
  }
);

onMounted(async() => {
  isDialogOpened.value = true;
  loading.value = true;

  const vendorId = route.params.id as string;
  if (!vendorId) {
    loading.value = false;
    return;
  }

  const data = await getCompanyProfile(vendorId);
  if (data) {
    vendorData.value = data;
  }
  loading.value = false;
});
</script>