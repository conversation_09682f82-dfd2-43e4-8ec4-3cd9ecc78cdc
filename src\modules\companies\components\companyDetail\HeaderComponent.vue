<template>
  <div class="bg-transparent relative">
    <template v-if="loading">
      <div class="flex items-center justify-center  ">
        <span class=" text-[26px] w-[80px] h-[80px] bg-nio-grey-200 uppercase relative flex items-center justify-center rounded-full" />
      </div>
      <div class="flex flex-col items-center">
        <div class="h-[48px] w-[320px] bg-nio-grey-200 rounded-10 mt-[20px] animate-pulse" />
        <div class="flex gap-4 mt-[30px]">
          <div class="h-[32px] w-[130px] px-3 py-1 bg-nio-grey-200 rounded-5 animate-pulse" />
          <div class="h-[32px] w-[130px] px-3 py-1 bg-nio-grey-200 rounded-5 animate-pulse" />
        </div>
      </div>
    </template>
    <template v-else>
      <div class="absolute top-3 right-3">
        <BasicPopover class="inline-block">
          <div role="listbox" class="ml-auto w-7 h-7 rounded-full flex items-center justify-center border bg-nio-grey-background-90 border-nio-grey-200  hover:bg-nio-grey-100 cursor-pointer" title="Actions">
            <ThreeDots class="h-3 w-3" />
          </div>
          <template #popover-content>
            <div class="flex flex-col">
              <div
                class="text-sm justify-center text-center font-medium text-nio-grey-700 py-1 px-2 flex items-center gap-3 hover:bg-nio-grey-100 cursor-pointer rounded-10"
                role="option"
                @click.stop="modal?.show()"
              >
                <div>Remove</div>
                <ConfirmationModal
                  ref="modal"
                  class="absolute text-center top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] cursor-auto"
                  :modal-title="`Delete Vendor: ${name}`"
                  modal-description="Do you really want to delete this vendor? This action cannot be undone."
                  confirm-button-text="Delete"
                  @confirmed.stop="emit('deleteCompany', id)"
                />
              </div>
            </div>
          </template>
        </BasicPopover>
      </div>
      <Transition appear>
        <div class="flex flex-col items-center ">
          <div class="flex items-center w-[80px] h-[80px] relative justify-center text-nio-white bg-nio-grey-900 rounded-full overflow-hidden">
            <img
              v-if="logo && isImageLoaded"
              :src="logo"
              alt="logo"
              class="w-[80px] h-[80px] shadow-lg rounded-full object-cover"
              @error="isImageLoaded = false"
            >

            <span v-else class="text-[26px] uppercase flex items-center justify-center">
              {{ companyNoLogoText }}
            </span>
          </div>
          <h1 class="text-[48px] font-normal text-nio-grey-900 mt-[15px] leading-normal">
            {{ name }}
          </h1>
          <div class="flex gap-2 mt-[15px]">
            <span
              class="inline-block px-3 py-1 text-sm font-normal bg-white text-nio-grey-700 rounded-5 shadow-custom"
            >
              {{ location }}
            </span>
            <span
              class="inline-block px-3 py-1 text-sm font-normal bg-white text-nio-grey-700 rounded-5 shadow-custom"
            >
              Founded {{ foundedYear }}
            </span>
          </div>
        </div>
      </Transition>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import BasicPopover from '@/common/components/popover/BasicPopover.vue';
import ThreeDots from '@/assets/icons/three-dots.svg';
import ConfirmationModal from '@/common/components/popover/ConfirmationModal.vue';

const emit = defineEmits(['deleteCompany']);
const modal = ref<InstanceType<typeof ConfirmationModal>>();

interface HeaderProps {
  name: string;
  location: string;
  foundedYear: string;
  cover?: string | null;
  logo?: string | null;
  loading?: boolean;
  id: string;
}

const props = withDefaults(defineProps<HeaderProps>(), {
  loading: false,
  cover: null,
  logo: null
});

const companyNoLogoText = computed(() => {
  const nameParts = props.name.split(' ');
  const firstChar = props.name.charAt(0);
  const secondChar = nameParts.length > 1 ? nameParts[nameParts.length - 1].charAt(0) : (props.name.length > 1 ? props.name.charAt(1) : '');
  return firstChar + secondChar;
});

const isImageLoaded = ref(true);
</script>

<style scoped>
.shadow-custom{
  box-shadow: 0 71px 20px 0 rgba(207, 207, 207, 0.00), 0 45px 18px 0 rgba(207, 207, 207, 0.01), 0 25px 15px 0 rgba(207, 207, 207, 0.05), 0 11px 11px 0 rgba(207, 207, 207, 0.09), 0 3px 6px 0 rgba(207, 207, 207, 0.10);
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
  transform: translateY(5px);
}
</style>
