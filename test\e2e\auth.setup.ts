import * as path from 'node:path';
import { fileURLToPath } from 'url';
import { test as setup, expect } from '@playwright/test';
import { clientUser } from './fixtures/users';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const authFile = path.join(`${__dirname}/.auth/user.json`);

setup('authenticate', async({ page }) => {
  await page.goto('/');
  await expect(page).toHaveTitle(/Sign in/);

  const emailInput = page.getByPlaceholder(/E-?mail/);
  await emailInput.fill(clientUser.email);
  const passwordInput = page.getByPlaceholder('Password');
  await passwordInput.fill(clientUser.password);
  await page.getByRole('button', { name: 'Continue' }).click();

  await page.waitForURL('/assistant');

  await page.context().storageState({ path: authFile });
});
