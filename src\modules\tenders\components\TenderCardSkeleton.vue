<template>
  <div
    class="flex flex-col justify-between border border-nio-border-default rounded-20 p-5 bg-nio-white w-full animate-pulse"
  >
    <div class="flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <div class="h-6 bg-nio-grey-100 rounded-full w-60" />
        <div class="h-6 bg-nio-grey-100 rounded-full w-60" />
      </div>
      <div class="h-6 bg-nio-grey-100 rounded-full mb-4 w-2/3" />
      <div class="h-14 bg-nio-grey-100 rounded-full mb-4 w-full" />
      <div class="flex flex-wrap gap-2 mt-1">
        <span class="h-7 bg-nio-grey-100 rounded-full w-1/4" />
        <span class="h-7 bg-nio-grey-100 rounded-full w-1/4" />
      </div>
    </div>
    <div class="flex justify-between items-center mt-3">
      <div class="h-5 bg-nio-grey-100 rounded-full w-95" />
      <div class="h-9 bg-nio-grey-100 rounded-full w-19" />
    </div>
  </div>
</template>

<script setup lang="ts">
</script>
