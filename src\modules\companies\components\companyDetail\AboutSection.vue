<template>
  <div class="flex flex-col gap-[30px]">
    <div v-if="topContent" class="flex flex-col gap-[30px]">
      <div class="inline-block px-[12px] py-[6px] bg-transparent border border-nio-grey-700 text-nio-grey-700 text-[18px] font-normal rounded-[30px] leading-[14px] w-fit">
        {{ topTitle }}
      </div>
      <h1
        class="text-[34px] font-normal text-nio-black-900 leading-normal tracking-[0.68px]"
        :class="{ 'max-w-[50%] text-left': bottomContent, 'w-full text-left': !bottomContent }"
      >
        {{ topContent }}
      </h1>
    </div>

    <div v-if="bottomContent" class="flex justify-end">
      <p
        class="text-[24px] font-normal text-nio-grey-500 leading-normal text-left max-w-[50%] tracking-[0.48px]"
      >
        {{ bottomContent }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  topTitle: string;
  topContent?: string;
  bottomContent?: string;
}

defineProps<Props>();
</script>
