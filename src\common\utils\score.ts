export type TenderScoreType = 'technologies' | 'projects' | 'location' | 'total';
export type CandidateScoreType = 'rate' | 'technologies' | 'location' | 'language' | 'seniority' | 'experience' | 'total';

// TODO: change the conditions according to business logic
export const TENDER_SCORE_THRESHOLDS = {
  total: [
    { min: 0, max: 0.99, text: 'No Match', points: 0 },
    { min: 1, max: 49, text: 'Limited', points: 1 },
    { min: 50, max: 69, text: 'Partial', points: 2 },
    { min: 70, max: 100, text: 'Strong', points: 3 },
  ],
  technologies: [
    { min: 0, max: 0.99, text: 'No Match', points: 0 },
    { min: 1, max: 49, text: 'Limited', points: 1 },
    { min: 50, max: 69, text: 'Partial', points: 2 },
    { min: 70, max: 100, text: 'Strong', points: 3 },
  ],
  projects: [
    { min: 0, max: 0.99, text: 'No Match', points: 0 },
    { min: 1, max: 49, text: 'Limited', points: 1 },
    { min: 50, max: 69, text: 'Partial', points: 2 },
    { min: 70, max: 100, text: 'Strong', points: 3 },
  ],
  location: [
    { min: 0, max: 0.99, text: 'Different', points: 0 },
    { min: 1, max: 33, text: 'Same Continent', points: 1 },
    { min: 34, max: 66, text: 'Same Economic Region', points: 2 },
    { min: 67, max: 100, text: 'Same Country', points: 3 },
  ],
};
// TODO: change the conditions according to business logic
export const CANDIDATE_SCORE_THRESHOLDS = TENDER_SCORE_THRESHOLDS;

export function isScoreAvailable(score: any): boolean {
  return typeof score === 'number';
}

export function tenderScoreToPoints(score: any, type: TenderScoreType): number {
  if (typeof score !== 'number') {return 0;}
  switch (type) {
    case 'total':
      return TENDER_SCORE_THRESHOLDS.total.find(t => score >= t.min && score <= t.max)?.points ?? 0;
    case 'technologies':
      return TENDER_SCORE_THRESHOLDS.technologies.find(t => score >= t.min && score <= t.max)?.points ?? 0;
    case 'projects':
      return TENDER_SCORE_THRESHOLDS.projects.find(t => score >= t.min && score <= t.max)?.points ?? 0;
    case 'location':
      return TENDER_SCORE_THRESHOLDS.location.find(t => score >= t.min && score <= t.max)?.points ?? 0;
  }

  return 0;
}

export function tenderScoreToText(score: any, type: TenderScoreType = 'total'): string {
  if (typeof score !== 'number') {return '-';}

  switch (type) {
    case 'total':
      return TENDER_SCORE_THRESHOLDS.total.find(t => score >= t.min && score <= t.max)?.text ?? '-';
    case 'technologies':
      return TENDER_SCORE_THRESHOLDS.technologies.find(t => score >= t.min && score <= t.max)?.text ?? '-';
    case 'projects':
      return TENDER_SCORE_THRESHOLDS.projects.find(t => score >= t.min && score <= t.max)?.text ?? '-';
    case 'location':
      return TENDER_SCORE_THRESHOLDS.location.find(t => score >= t.min && score <= t.max)?.text ?? '-';
  }

  return '-';
}
export function candidateScoreToPoints(score: any, type: CandidateScoreType): number {
  if (typeof score !== 'number') {return 0;}
  switch (type) {
    case 'total':
      return CANDIDATE_SCORE_THRESHOLDS.total.find(t => score >= t.min && score <= t.max)?.points ?? 0;
    case 'technologies':
      return CANDIDATE_SCORE_THRESHOLDS.technologies.find(t => score >= t.min && score <= t.max)?.points ?? 0;
    case 'experience':
      // TODO: create own tresholds for candidate
      return CANDIDATE_SCORE_THRESHOLDS.projects.find(t => score >= t.min && score <= t.max)?.points ?? 0;
    case 'location':
      return CANDIDATE_SCORE_THRESHOLDS.location.find(t => score >= t.min && score <= t.max)?.points ?? 0;
  }

  return 0;
}

export function candidateScoreToText(score: any, type: CandidateScoreType = 'total'): string {
  if (typeof score !== 'number') {return '-';}

  switch (type) {
    case 'total':
      return CANDIDATE_SCORE_THRESHOLDS.total.find(t => score >= t.min && score <= t.max)?.text ?? '-';
    case 'technologies':
      return CANDIDATE_SCORE_THRESHOLDS.technologies.find(t => score >= t.min && score <= t.max)?.text ?? '-';
    case 'experience':
      return CANDIDATE_SCORE_THRESHOLDS.projects.find(t => score >= t.min && score <= t.max)?.text ?? '-';
    case 'location':
      return CANDIDATE_SCORE_THRESHOLDS.location.find(t => score >= t.min && score <= t.max)?.text ?? '-';
  }

  return '-';
}
