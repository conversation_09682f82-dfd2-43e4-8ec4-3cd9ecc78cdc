export const transformText = (
  ref: HTMLElement,
  speedMultiplier: number = 1 // Default multiplier is 1 (current speed)
): number => {
  const childNodes = Array.from(ref.childNodes);
  let cumulativeDelay = 0;

  // Base delays
  const baseWordDelay = 0.15 / speedMultiplier; // Adjust delay based on speed multiplier
  const baseBrDelay = 0.25 / speedMultiplier;

  childNodes.forEach((node, nodeIndex) => {
    if (node.nodeType === Node.TEXT_NODE) {
      const textContent = node.textContent!;
      const words = textContent.split(/(\s+)/); // Split by spaces, retaining them as separate elements
      let lastSibling: Node | null = node;

      words.forEach(word => {
        if (word.trim() === '') {
          // If the word is just a space, add it as a text node
          const spaceNode = document.createTextNode(word);
          ref.insertBefore(spaceNode, lastSibling?.nextSibling || null);
          lastSibling = spaceNode;
        } else {
          // Wrap words in <span> elements
          const span = document.createElement('span');
          span.style.animationDelay = `${cumulativeDelay}s`;
          span.style.display = 'inline-block';
          span.textContent = word;
          ref.insertBefore(span, lastSibling?.nextSibling || null);
          lastSibling = span;
          cumulativeDelay += baseWordDelay;
        }
      });

      // Remove the original text node after processing
      ref.removeChild(node);
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement;

      if (element.tagName === 'SPAN') {
        // Adjust existing <span> animation delay in place
        element.style.animationDelay = `${cumulativeDelay}s`;
        cumulativeDelay += baseWordDelay;

        // Add space after the span if the next sibling is a text node starting with a space
        const nextNode = childNodes[nodeIndex + 1];
        if (nextNode?.nodeType === Node.TEXT_NODE && nextNode.textContent?.startsWith(' ')) {
          const spaceNode = document.createTextNode(' ');
          ref.insertBefore(spaceNode, element.nextSibling);
        }
      } else if (element.tagName === 'BR') {
        // Keep <br> elements intact, adjust delay
        cumulativeDelay += baseBrDelay;
      } else {
        // Keep other elements intact
        // Optional: Adjust their styles if necessary
      }
    }
  });

  // Set animation duration based on the speed multiplier
  const animationDuration = 1.25 / speedMultiplier; // Adjust duration dynamically
  ref.style.setProperty('--animation-duration', `${animationDuration}s`);

  ref.classList.add('blur-anim');

  // Convert cumulative delay to milliseconds and return
  return Math.ceil(cumulativeDelay * 1000);
};
