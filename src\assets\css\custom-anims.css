@keyframes blurOut {
    0% {
        filter: blur(30px);
        opacity: 0;
        scale: 1.5;
        color: #a1a1a1;
    }
    35% {
        color: #a1a1a1;
    }
    50% {
        scale: 1;
    }
    100% {
        filter: blur(0);
        opacity: 1;
        scale: 1;
        color: var(--blur-anim-color, #000000);
    }
}

.blur-anim {
    will-change: transform;
    > span {
        color: #a1a1a1;
        opacity: 0;
        animation: blurOut var(--animation-duration, 1.25s) forwards ease;
    }
}
