import type { NIORouteMap } from '@/router';

export const routeMap = {
  companies: {
    path: '/companies',
    name: 'vendorsRoute',
    meta: {
      i18nTitle: 'companies.title',
      authNotRequired: true,
      headTitlePrefix: 'Companies',
    },
    children: {
      companiesDashboard: {
        path: '',
        name: 'vendorsRouteDashboard',
        meta: {
          i18nTitle: 'companies.title',
          authNotRequired: true,
          headTitlePrefix: 'Companies',
        },
      },
      // temporarily disabled due to issues with the upload functionality
      //vendorsUpload: {
      //  path: '/companies/upload',
      //  name: 'vendorsUploadRoute',
      //  meta: {
      //    i18nTitle: 'company.upload.title',
      //    authNotRequired: true,
      //    headTitlePrefix: 'Companies Upload',
      //  },
      //},
    }
  },
  vendorDetail: {
    path: ':id',
    name: 'vendorDetailRoute',
    meta: {
      i18nTitle: 'company.detail.title',
      authNotRequired: true,
      headTitlePrefix: 'Company Detail',
    },
  },
} satisfies NIORouteMap;

export const routeIndex = {
  companies: {
    ...routeMap.companies,
    redirect: { name: 'vendorsRouteDashboard' },
    children: [
      {
        ...routeMap.companies.children.companiesDashboard,
        component: () => import('../pages/CompanyListPage.vue'),
      },
      // temporarily disabled due to issues with the upload functionality
      //{
      //  ...routeMap.companies.children.vendorsUpload,
      //  component: () => import('../pages/CompanyUploadPage.vue'),
      //},
      {
        ...routeMap.vendorDetail,
        component: () => import('../pages/CompanyDetailPage.vue'),
      },
    ],
  },
};

export default Object.values(routeIndex);
