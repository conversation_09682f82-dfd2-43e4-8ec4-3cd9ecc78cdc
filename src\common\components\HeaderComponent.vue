<template>
  <div class="relative z-40">
    <header class="sticky top-0 flex items-center justify-end h-[5.3rem] bg-transparent">
      <div v-if="![routeMap.assistant.name, routeMap.assistantRfp.name].includes(route.name as string)" class="no-fixed no-top-[22px] flex items-center justify-end gap-2.5">
        <div class="text-nio-grey-500 font-medium leading-4 -tracking-028px text-sm">
          {{ $t('assistant.create-q-rfp') }}
        </div>
        <router-link :to="{name: routeMap.assistant.name}" class="py-3 px-4 text-sm text-nio-blue-800 hover:text-nio-white hover:bg-nio-blue-800 transition-all duration-150 font-medium leading-4 -tracking-028px rounded-full border border-nio-blue-outline-stroke-400">
          {{ $t('navigation.projectAssistant') }}
        </router-link>
      </div>
      <div v-if="route.name === routeMap.assistantRfp.name" class="no-fixed no-top-[22px] flex items-center justify-end gap-2.5">
        <div class="text-nio-grey-500 font-medium leading-4 -tracking-028px text-sm">
          {{ $t('assistant.create-q-rfp') }}
        </div>
        <router-link :to="{name: routeMap.assistant.name}" class="group cursor-pointer py-3 px-4 text-sm text-nio-blue-800 hover:text-nio-white hover:bg-nio-blue-800 font-medium leading-4 -tracking-028px rounded-full border border-nio-blue-outline-stroke-400 flex items-center gap-1 transition-all duration-150">
          <PlusIcon class="min-w-5 min-h-5 bg-nio-blue-800 rounded-full fill-white text-white group-hover:*:fill-nio-blue-800 group-hover:bg-nio-white transition-all duration-150" />
          <div class="whitespace-nowrap">
            {{ $t('assistant.create-rfp') }}
          </div>
        </router-link>
      </div>
    </header>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { routeMap } from '@/modules/assistant/routes';
import PlusIcon from '@/assets/icons/plus-icon-respo.svg';

const route = useRoute();
</script>
