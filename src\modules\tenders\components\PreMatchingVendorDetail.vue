<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { Company } from '@/modules/tenders/types/tenders-types';
import AboutMatchingPopover from './AboutMatchingPopover.vue';
import NioScoreDots from '@/common/components/NioScoreDots.vue';
import { tenderScoreToPoints } from '@/common/utils/score';
import { useFormattedRates } from '@/common/composables/useFormattedRates.ts';
import NioInfoBlock from '@/common/components/NioInfoBlock.vue';

interface Props {
  invited: boolean
  company: Company;
}

const props = defineProps<Props>();
defineEmits(['invite', 'uninvite']);
const showInviteButtons = computed(() => !window.location.href.includes('/matching/marketplace'));

const isPopoverOpen = ref(false);

const formattedRates = useFormattedRates(props.company.match_details?.matching_rates);
</script>

<template>
  <div class="flex">
    <div class="flex w-fit gap-x-2 mt-2 ml-3">
      <div class="flex items-start gap-3 cursor-pointer text-xs">
        <NioInfoBlock title="Technologies">
          <NioScoreDots class="mt-auto mb-[3px]" :points="tenderScoreToPoints(company.match_details?.technologies_score, 'technologies')" />
        </NioInfoBlock>
        <NioInfoBlock title="Domain">
          <NioScoreDots class=" mb-[3px]" :points="tenderScoreToPoints(company.match_details?.projects_score, 'projects')" />
        </NioInfoBlock>

        <NioInfoBlock title="Location">
          <span class="text-nio-green-text whitespace-nowrap">
            {{ company.match_details?.location_match }}
          </span>
        </NioInfoBlock>
        <NioInfoBlock title="Rates">
          <span class="whitespace-nowrap">
            {{ formattedRates }}
          </span>
        </NioInfoBlock>
      </div>
    </div>

    <div class="flex items-end justify-end mt-auto w-full gap-2">
      <button
        class="px-3 py-1.5 bg-nio-blue-800 text-nio-white text-[12px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer"
        @click.stop="isPopoverOpen = true"
      >
        About Matching
      </button>

      <template v-if="showInviteButtons">
        <button
          v-if="invited"
          class="px-4 py-2 bg-nio-red-500 text-white text-[14px] rounded-50 hover:opacity-90 transition-colors cursor-pointer"
          @click.stop="$emit('uninvite')"
        >
          Cancel invite
        </button>

        <button
          v-else
          class="px-4 py-2 bg-nio-green-text text-white text-[14px] rounded-50 hover:opacity-90 transition-colors cursor-pointer"
          @click.stop="$emit('invite')"
        >
          Invite
        </button>
      </template>
    </div>
  </div>
  <AboutMatchingPopover v-model="isPopoverOpen" :company="company" />
</template>
